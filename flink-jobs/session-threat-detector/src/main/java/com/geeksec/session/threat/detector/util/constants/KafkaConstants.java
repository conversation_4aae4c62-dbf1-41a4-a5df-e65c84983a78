package com.geeksec.session.threat.detector.util.constants;

/**
 * Threat Detector 专用的 Kafka 常量
 * 基础配置常量请使用 shared-core 中的 ConfigConstants
 *
 * <AUTHOR>
 * @deprecated 建议使用 shared-core 中的 ConfigConstants，此类将在未来版本中移除
 */
@Deprecated
public final class KafkaConstants {

    private KafkaConstants() {
        // 防止实例化
    }

    // 默认值（用于向后兼容）
    public static final String BOOTSTRAP_SERVERS = "localhost:9092";
    public static final String GROUP_ID = "threat-detector-group";

    // Threat Detector 专用主题
    public static final String SESSION_TOPIC = "session-metadata";
    public static final String PROTOCOL_TOPIC = "protocol-metadata";
    public static final String CONFIG_TOPIC = "threat-detector-config";
    public static final String ALARM_TOPIC = "threat-alarms";
    public static final String NOTIFICATION_TOPIC = "threat-notifications";

    // Kafka配置参数
    public static final String AUTO_OFFSET_RESET = "latest";
    public static final String ENABLE_AUTO_COMMIT = "false";
    public static final String MAX_POLL_RECORDS = "500";
    public static final String FETCH_MIN_BYTES = "1";
    public static final String FETCH_MAX_WAIT_MS = "500";

    // 序列化器
    public static final String STRING_SERIALIZER = "org.apache.kafka.common.serialization.StringSerializer";
    public static final String STRING_DESERIALIZER = "org.apache.kafka.common.serialization.StringDeserializer";
}
