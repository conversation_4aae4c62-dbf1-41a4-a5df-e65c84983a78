package com.geeksec.certificate.analyzer.operator.analysis.attribute;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.validator.routines.EmailValidator;
import org.apache.flink.api.common.functions.MapFunction;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.common.network.NetworkUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书属性提取器
 * 
 * 职责：
 * 1. 提取证书的基础属性和元数据信息
 * 2. 不涉及安全判断，仅进行纯数据提取
 * 3. 为后续的合规验证、信任评估和威胁检测提供基础数据
 * 
 * 提取的属性包括：
 * - 域名信息
 * - 证书字段信息（Subject、Issuer等）
 * - 时间属性（有效期等）
 * - 加密算法信息
 * - 颁发者信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateAttributeExtractor implements MapFunction<X509Certificate, X509Certificate> {



    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书属性提取，证书ID: {}", certificate.getDerSha1());

        // 提取域名信息
        extractDomainAttributes(certificate);

        // 提取证书字段信息
        extractCertificateFields(certificate);

        // 提取时间属性
        extractTemporalAttributes(certificate);

        // 提取加密算法信息
        extractCryptographicAttributes(certificate);

        // 提取颁发者信息
        extractIssuerAttributes(certificate);

        // 提取SAN值信息
        extractSanAttributes(certificate);

        // 提取证书扩展信息
        extractExtensionAttributes(certificate);

        return certificate;
    }

    /**
     * 提取域名信息
     * 纯属性提取，不进行任何安全判断
     */
    private void extractDomainAttributes(X509Certificate certificate) {
        List<String> domains = certificate.getCertDomains();

        if (domains != null && !domains.isEmpty()) {
            log.debug("证书包含 {} 个域名", domains.size());

            // 统计域名属性信息
            int wildcardCount = 0;
            int ipCount = 0;

            for (String domain : domains) {
                if (domain.startsWith("*.")) {
                    wildcardCount++;
                }
                if (NetworkUtils.isValidIp(domain)) {
                    ipCount++;
                }
            }

            log.debug("域名统计 - 总数: {}, 通配符: {}, IP地址: {}",
                domains.size(), wildcardCount, ipCount);

            // 确保证书对象中的域名列表已设置
            if (certificate.getCertDomains().isEmpty()) {
                certificate.setCertDomains(domains);
            }
        }
    }

    /**
     * 提取证书字段信息
     * 提取Subject、Issuer等字段的基础信息
     */
    private void extractCertificateFields(X509Certificate certificate) {
        // 提取Subject字段信息
        Map<String, String> subject = certificate.getSubject();
        if (subject != null) {
            log.debug("Subject字段包含 {} 个属性", subject.size());

            // 提取通用名称(CN)
            String commonName = subject.get("CN");
            if (commonName != null && certificate.getCommonName() == null) {
                certificate.setCommonName(commonName);
                log.debug("设置通用名称: {}", commonName);
            }

            // 提取组织信息
            String organization = subject.get("O");
            boolean shouldSetOrganization = organization != null &&
                (certificate.getOrganization() == null || certificate.getOrganization().isEmpty());
            if (shouldSetOrganization) {
                certificate.setOrganization(organization);
                log.debug("设置组织信息: {}", organization);
            }
        }

        // 提取Issuer字段信息
        Map<String, String> issuer = certificate.getIssuer();
        if (issuer != null) {
            log.debug("Issuer字段包含 {} 个属性", issuer.size());
            // Issuer信息已经存储在证书对象中，这里只记录统计信息
        }
    }

    /**
     * 提取时间属性
     * 提取证书的时间相关属性
     */
    private void extractTemporalAttributes(X509Certificate certificate) {
        LocalDateTime notBefore = certificate.getNotBefore();
        LocalDateTime notAfter = certificate.getNotAfter();

        if (notBefore != null && notAfter != null) {
            Duration validityPeriod = Duration.between(notBefore, notAfter);
            long validityDays = validityPeriod.toDays();

            log.debug("证书有效期: {} 天", validityDays);

            // 将有效期天数信息存储到证书对象中，为后续的合规性检查提供数据支持
            certificate.setDuration(validityDays);

            // 设置导入时间（如果尚未设置）
            if (certificate.getImportTime() == null) {
                certificate.setImportTime(LocalDateTime.now());
            }
        }
    }

    /**
     * 提取加密算法信息
     * 提取证书使用的加密算法相关信息
     */
    private void extractCryptographicAttributes(X509Certificate certificate) {
        String signatureAlgorithm = certificate.getSignatureAlgorithm();
        
        if (signatureAlgorithm != null) {
            log.debug("签名算法: {}", signatureAlgorithm);
            // 可以添加算法类型的分类信息提取
        }
        
        // 可以提取其他加密相关属性
        String publicKeyAlgorithm = certificate.getPublicKeyAlgorithm();
        if (publicKeyAlgorithm != null) {
            log.debug("公钥算法: {}", publicKeyAlgorithm);
        }
    }

    /**
     * 提取颁发者信息
     * 提取证书颁发者的相关信息
     */
    private void extractIssuerAttributes(X509Certificate certificate) {
        Map<String, String> issuer = certificate.getIssuer();

        if (issuer != null) {
            String issuerOrg = issuer.get("O");
            String issuerCommonName = issuer.get("CN");

            if (issuerOrg != null) {
                log.debug("颁发机构: {}", issuerOrg);
            }

            if (issuerCommonName != null) {
                log.debug("颁发者CN: {}", issuerCommonName);
            }

            // 这里可以进一步提取颁发者的其他属性
            // 如国家(C)、州/省(ST)、城市(L)等
            String issuerCountry = issuer.get("C");
            String issuerState = issuer.get("ST");
            String issuerLocality = issuer.get("L");

            if (issuerCountry != null) {
                log.debug("颁发者国家: {}", issuerCountry);
            }
        }
    }

    /**
     * 提取SAN值信息
     * 提取Subject Alternative Names的基础信息
     */
    private void extractSanAttributes(X509Certificate certificate) {
        List<String> sanList = certificate.getSubjectAltNames();
        if (sanList == null || sanList.isEmpty()) {
            log.debug("证书不包含SAN值");
            return;
        }

        log.debug("证书包含 {} 个SAN值", sanList.size());

        // 统计不同类型的SAN值数量
        int ipCount = 0;
        int wildcardCount = 0;
        int emailCount = 0;
        int uriCount = 0;

        // 分离IP地址到专门的列表
        List<String> certIps = new ArrayList<>();

        for (String san : sanList) {
            // 检查IP地址
            if (NetworkUtils.isValidIp(san)) {
                ipCount++;
                certIps.add(san);
            }

            // 检查通配符
            if (san.startsWith("*.")) {
                wildcardCount++;
            }

            // 检查邮箱格式
            if (EmailValidator.getInstance().isValid(san)) {
                emailCount++;
            }

            // 检查URI格式
            if (san.startsWith("http://") || san.startsWith("https://") || san.startsWith("ftp://")) {
                uriCount++;
            }
        }

        // 将提取的IP地址存储到证书对象中
        if (!certIps.isEmpty()) {
            certificate.setCertIps(certIps);
            log.debug("从SAN中提取到 {} 个IP地址", certIps.size());
        }

        log.debug("SAN值统计 - IP: {}, 通配符: {}, 邮箱: {}, URI: {}",
            ipCount, wildcardCount, emailCount, uriCount);
    }

    /**
     * 提取证书扩展信息
     * 提取证书扩展的基础信息
     */
    private void extractExtensionAttributes(X509Certificate certificate) {
        if (certificate.getCertificateExtensions() != null) {
            // 使用 miscellaneousExtensions 字段来获取扩展映射
            Map<String, Object> extensionMap = certificate.getCertificateExtensions().getMiscellaneousExtensions();

            if (extensionMap != null && !extensionMap.isEmpty()) {
                log.debug("证书包含 {} 个扩展", extensionMap.size());

                // 记录关键扩展的存在性并提取相关信息
                String[] keyExtensions = {
                    CertificateConstants.EXTENSION_BASIC_CONSTRAINTS, "keyUsage", "extendedKeyUsage",
                    "subjectAltName", "certificatePolicies", "authorityInfoAccess"
                };

                for (String extension : keyExtensions) {
                    if (extensionMap.containsKey(extension)) {
                        log.debug("发现扩展: {}", extension);
                        // 这里可以进一步提取具体扩展的详细信息
                        extractSpecificExtensionInfo(certificate, extension, extensionMap.get(extension));
                    }
                }
            } else {
                log.debug("证书扩展映射为空");
            }
        } else {
            log.debug("证书不包含扩展信息");
        }
    }

    /**
     * 提取特定扩展的详细信息
     */
    private void extractSpecificExtensionInfo(X509Certificate certificate, String extensionName, Object extensionValue) {
        // 根据扩展类型提取相关信息
        switch (extensionName) {
            case CertificateConstants.EXTENSION_BASIC_CONSTRAINTS:
                // 提取基本约束信息，用于判断是否为CA证书
                if (extensionValue != null) {
                    String basicConstraints = extensionValue.toString();
                    log.debug("基本约束: {}", basicConstraints);
                }
                break;
            case "keyUsage":
                // 密钥用途信息已经在证书对象中，这里只记录日志
                log.debug("密钥用途: {}", certificate.getKeyUsage());
                break;
            case "extendedKeyUsage":
                // 扩展密钥用途信息
                if (certificate.getExtendedKeyUsage() != null) {
                    log.debug("扩展密钥用途数量: {}", certificate.getExtendedKeyUsage().size());
                }
                break;
            case "subjectAltName":
                // SAN信息已经在extractSanAttributes中处理
                break;
            default:
                log.debug("其他扩展 {}: {}", extensionName, extensionValue);
                break;
        }
    }

}
