package com.geeksec.alarm.processor.pipeline;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmEvent;
import com.geeksec.alarm.processor.pipeline.function.AlarmEventToAlarmConverter;
import com.geeksec.alarm.processor.pipeline.function.EnhancedAlarmSuppressionFunction;
import com.geeksec.alarm.processor.suppression.SuppressionRuleManager;
import com.geeksec.alarm.processor.pipeline.function.AttackChainAnalysisFunction;
import com.geeksec.alarm.processor.pipeline.function.DeduplicationFunction;
import com.geeksec.alarm.processor.pipeline.function.FormattingFunction;
import com.geeksec.alarm.processor.pipeline.function.KnowledgeEnrichmentFunction;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 告警处理流水线
 * 实现完整的告警处理流程：转换 -> 去重 -> 格式化 -> 攻击链分析
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmProcessingPipeline {
    
    /**
     * 构建告警处理流水线
     *
     * @param alarmEventStream 告警事件数据流
     * @param config 配置对象
     * @param env Flink 执行环境
     * @param suppressionManager 抑制规则管理器
     * @return 流水线结果
     */
    public static PipelineResult build(DataStream<AlarmEvent> alarmEventStream,
                                     AlarmProcessorConfig config,
                                     org.apache.flink.streaming.api.environment.StreamExecutionEnvironment env,
                                     SuppressionRuleManager suppressionManager) {
        
        log.info("开始构建告警处理流水线");
        
        // 1. 事件转换：将告警事件转换为告警对象
        DataStream<Alarm> alarmStream = alarmEventStream
                .map(new AlarmEventToAlarmConverter())
                .name("告警事件转换")
                .uid("alarm-event-to-alarm-converter");
        
        log.info("告警事件转换组件已添加");
        
        // 2. 告警抑制规则过滤：过滤掉符合抑制规则的告警
        DataStream<Alarm> filteredAlarmStream = alarmStream
                .filter(new EnhancedAlarmSuppressionFunction(suppressionManager))
                .name("告警抑制规则过滤")
                .uid("suppression-filter");
        log.info("增强的告警抑制规则过滤组件已添加");

        // 3. 告警去重：根据配置进行去重处理
        DataStream<Alarm> dedupedAlarmStream;
        if (config.isDeduplicationEnabled()) {
            dedupedAlarmStream = filteredAlarmStream
                    .keyBy(Alarm::getDeduplicationKey)
                    .flatMap(new DeduplicationFunction(config))
                    .name("告警去重")
                    .uid("alarm-deduplication");
            log.info("告警去重组件已添加");
        } else {
            dedupedAlarmStream = filteredAlarmStream;
            log.info("告警去重已禁用，跳过去重处理");
        }

        // 4. 知识库增强：使用知识库服务增强告警信息并应用类型特化处理
        DataStream<Alarm> enrichedAlarmStream = dedupedAlarmStream
                .map(new KnowledgeEnrichmentFunction(config))
                .name("知识库增强")
                .uid("knowledge-enrichment");
        log.info("知识库增强组件已添加");

        // 5. 告警格式化：对增强后的告警进行最终格式化
        DataStream<Alarm> formattedAlarmStream;
        if (config.isFormattingEnabled()) {
            formattedAlarmStream = enrichedAlarmStream
                    .map(new FormattingFunction(config))
                    .name("告警格式化")
                    .uid("alarm-formatting");
            log.info("告警格式化组件已添加");
        } else {
            formattedAlarmStream = enrichedAlarmStream;
            log.info("告警格式化已禁用，跳过格式化处理");
        }
        
        // 6. 攻击链分析：分析告警间的关联关系
        DataStream<Alarm> attackChainAnalyzedStream;
        if (config.isAttackChainEnabled()) {
            attackChainAnalyzedStream = formattedAlarmStream
                    .keyBy(alarm -> alarm.getSrcIp() + "|" + alarm.getDstIp())
                    .process(new AttackChainAnalysisFunction(config))
                    .name("攻击链分析")
                    .uid("attack-chain-analysis");
            log.info("攻击链分析组件已添加");
        } else {
            attackChainAnalyzedStream = formattedAlarmStream;
            log.info("攻击链分析已禁用，跳过攻击链分析");
        }
        
        // 7. 分流处理：根据告警类型和优先级进行分流
        StreamSplitter splitter = new StreamSplitter(attackChainAnalyzedStream, config);

        DataStream<Alarm> highPriorityAlarms = splitter.getHighPriorityAlarms();
        DataStream<Alarm> normalAlarms = splitter.getNormalAlarms();
        DataStream<Alarm> certificateAlarms = splitter.getCertificateAlarms();

        log.info("告警分流组件已添加");

        log.info("告警处理流水线构建完成");

        return PipelineResult.builder()
                .processedAlarms(attackChainAnalyzedStream)
                .highPriorityAlarms(highPriorityAlarms)
                .normalAlarms(normalAlarms)
                .certificateAlarms(certificateAlarms)
                .build();
    }
    
    /**
     * 流水线结果
     */
    @Data
    @lombok.Builder
    public static class PipelineResult {
        
        /** 处理后的所有告警流 */
        private DataStream<Alarm> processedAlarms;
        
        /** 高优先级告警流 */
        private DataStream<Alarm> highPriorityAlarms;
        
        /** 普通告警流 */
        private DataStream<Alarm> normalAlarms;
        
        /** 证书相关告警流 */
        private DataStream<Alarm> certificateAlarms;
    }
    
    /**
     * 流分割器
     * 根据告警属性将流分割为不同的子流
     */
    private static class StreamSplitter {
        
        private final DataStream<Alarm> highPriorityAlarms;
        private final DataStream<Alarm> normalAlarms;
        private final DataStream<Alarm> certificateAlarms;
        
        public StreamSplitter(DataStream<Alarm> alarmStream, AlarmProcessorConfig config) {
            // 高优先级告警过滤
            this.highPriorityAlarms = alarmStream
                    .filter(Alarm::isHighPriority)
                    .name("高优先级告警过滤")
                    .uid("high-priority-alarm-filter");
            
            // 普通告警过滤
            this.normalAlarms = alarmStream
                    .filter(alarm -> !alarm.isHighPriority())
                    .name("普通告警过滤")
                    .uid("normal-alarm-filter");
            
            // 证书相关告警过滤
            this.certificateAlarms = alarmStream
                    .filter(Alarm::isCertificateAlarm)
                    .name("证书告警过滤")
                    .uid("certificate-alarm-filter");
        }
        
        public DataStream<Alarm> getHighPriorityAlarms() {
            return highPriorityAlarms;
        }
        
        public DataStream<Alarm> getNormalAlarms() {
            return normalAlarms;
        }
        
        public DataStream<Alarm> getCertificateAlarms() {
            return certificateAlarms;
        }
    }
}
