{{- if .Values.infrastructure.flink.enabled -}}


{{- if .Values.infrastructure.flink.jobs.threat-detector.enabled -}}
---
apiVersion: v1
kind: Service
metadata:
  name: flink-session-threat-detector
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app: flink-session-threat-detector
spec:
  selector:
    app: flink-session-threat-detector
    component: jobmanager
  ports:
  - name: ui
    port: 8081
    targetPort: 8081
  type: ClusterIP
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.session-processor.enabled -}}
---
apiVersion: v1
kind: Service
metadata:
  name: flink-session-processor
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app: flink-session-processor
spec:
  selector:
    app: flink-session-processor
    component: jobmanager
  ports:
  - name: ui
    port: 8081
    targetPort: 8081
  type: ClusterIP
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.certificate-analyzer.enabled -}}
---
apiVersion: v1
kind: Service
metadata:
  name: flink-certificate-analyzer
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app: flink-certificate-analyzer
spec:
  selector:
    app: flink-certificate-analyzer
    component: jobmanager
  ports:
  - name: ui
    port: 8081
    targetPort: 8081
  type: ClusterIP
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.alarm-processor.enabled -}}
---
apiVersion: v1
kind: Service
metadata:
  name: flink-alarm-processor
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app: flink-alarm-processor
spec:
  selector:
    app: flink-alarm-processor
    component: jobmanager
  ports:
  - name: ui
    port: 8081
    targetPort: 8081
  type: ClusterIP
{{- end -}}
{{- end -}}
