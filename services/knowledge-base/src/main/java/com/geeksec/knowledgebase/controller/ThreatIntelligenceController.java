package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.domain.entity.ThreatIntelligence;
import com.geeksec.knowledgebase.service.ThreatIntelligenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/threat-intelligence")
@RequiredArgsConstructor
@Tag(name = "Threat Intelligence Controller", description = "威胁情报查询")
public class ThreatIntelligenceController {

    private final ThreatIntelligenceService threatIntelligenceService;

    @GetMapping("/check/{indicator}")
    @Operation(summary = "检查指标是否存在于威胁情报库中")
    public ApiResponse<ThreatIntelligence> checkIndicator(
            @Parameter(description = "威胁指标(IP, 域名, URL, 文件哈希等)") @PathVariable String indicator) {
        log.debug("检查威胁指标: {}", indicator);
        Optional<ThreatIntelligence> result = threatIntelligenceService.checkIndicator(indicator);
        return ApiResponse.success(result.orElse(null));
    }

    @GetMapping("/name/{threatName}")
    @Operation(summary = "根据威胁名称获取威胁情报")
    public ApiResponse<Map<String, Object>> getThreatIntelligenceByName(
            @Parameter(description = "威胁名称") @PathVariable String threatName) {
        log.debug("根据威胁名称获取威胁情报: {}", threatName);
        Optional<ThreatIntelligence> result = threatIntelligenceService.findByThreatName(threatName);
        if (result.isPresent()) {
            ThreatIntelligence threat = result.get();
            Map<String, Object> response = Map.of(
                    "threatName", threat.getThreatName(),
                    "threatType", threat.getThreatType(),
                    "severity", threat.getSeverity(),
                    "description", threat.getDescription() != null ? threat.getDescription() : "",
                    "iocValue", threat.getIocValue(),
                    "confidence", threat.getConfidence()
            );
            return ApiResponse.success(response);
        }
        return ApiResponse.success(null);
    }
}
