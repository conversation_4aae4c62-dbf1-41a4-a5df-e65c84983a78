package com.geeksec.certificate.analyzer.source.kafka;

import java.time.LocalDateTime;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import com.geeksec.certificate.analyzer.enums.CertificateSource;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.util.cert.CertificateUtils;
import com.geeksec.common.utils.crypto.HashUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书反序列化器
 * 将Kafka消息中的原始证书字节数据反序列化为X509Certificate对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateDeserializationSchema implements KafkaRecordDeserializationSchema<X509Certificate> {

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<X509Certificate> collector) {
        byte[] certBytes = record.value();
        
        if (certBytes == null || certBytes.length == 0) {
            log.warn("接收到空的证书数据");
            return;
        }

        try {
            // 创建证书对象
            X509Certificate certificate = new X509Certificate();
            
            // 设置原始证书数据
            certificate.setCert(certBytes);
            
            // 解析证书
            java.security.cert.X509Certificate javaCert = CertificateUtils.parseCertificateAdvanced(certBytes);
            
            if (javaCert != null) {
                // 解析成功，填充证书信息
                populateCertificateInfo(certificate, javaCert);
                certificate.setIsParsedSuccessfully(true);
                log.debug("成功解析证书，SHA1: {}", certificate.getDerSha1());
            } else {
                // 解析失败，但仍然保存原始数据
                handleParsingFailure(certificate, certBytes);
                certificate.setIsParsedSuccessfully(false);
                log.warn("证书解析失败，但保留原始数据用于后续处理");
            }
            
            // 设置证书来源和时间信息
            setCertificateMetadata(certificate, record);
            
            collector.collect(certificate);
            
        } catch (Exception e) {
            log.error("反序列化证书数据时发生异常", e);
            
            // 即使发生异常，也尝试创建一个基础的证书对象
            try {
                X509Certificate certificate = new X509Certificate();
                certificate.setCert(certBytes);
                handleParsingFailure(certificate, certBytes);
                setCertificateMetadata(certificate, record);
                collector.collect(certificate);
            } catch (Exception ex) {
                log.error("创建基础证书对象失败", ex);
            }
        }
    }

    /**
     * 填充证书信息
     */
    private void populateCertificateInfo(X509Certificate certificate, java.security.cert.X509Certificate javaCert) {
        try {
            // 计算各种哈希值
            CertificateUtils.calculateHashes(certificate, javaCert);
            
            // 提取基本信息
            CertificateUtils.extractBasicInfo(certificate, javaCert);
            
            // 提取主题和颁发者信息
            CertificateUtils.extractSubjectAndIssuer(certificate, javaCert);
            
            // 提取时间信息
            CertificateUtils.extractTimeInfo(certificate, javaCert);
            
            // 提取公钥信息
            CertificateUtils.extractPublicKeyInfo(certificate, javaCert);
            
            // 提取签名信息
            CertificateUtils.extractSignatureInfo(certificate, javaCert);
            
            // 提取扩展信息
            CertificateUtils.extractExtensions(certificate, javaCert);
            
        } catch (Exception e) {
            log.error("填充证书信息时发生异常", e);
        }
    }

    /**
     * 处理解析失败的情况
     */
    private void handleParsingFailure(X509Certificate certificate, byte[] certBytes) {
        try {
            // 计算原始数据的哈希值作为标识
            String sha1 = HashUtils.sha1(certBytes);
            certificate.setDerSha1(sha1);
            
            // 设置错误状态
            certificate.setIsCorrupted(true);
            certificate.setParseStatus("PARSE_FAILED");
            
        } catch (Exception e) {
            log.error("处理解析失败时发生异常", e);
        }
    }

    /**
     * 设置证书元数据
     */
    private void setCertificateMetadata(X509Certificate certificate, ConsumerRecord<byte[], byte[]> record) {
        // 设置证书来源
        String topic = record.topic();
        CertificateSource source = CertificateSource.fromKafkaTopic(topic);
        certificate.setSource(source.getCode());
        
        // 设置导入时间
        certificate.setImportTime(LocalDateTime.now());
        certificate.setFirstSeen(LocalDateTime.now());
        certificate.setLastSeen(LocalDateTime.now());
        certificate.setCreateTime(LocalDateTime.now());
        certificate.setUpdateTime(LocalDateTime.now());
        
        // 设置处理方法
        certificate.setProcessingMethod("KAFKA_STREAM");
    }

    @Override
    public TypeInformation<X509Certificate> getProducedType() {
        return TypeInformation.of(X509Certificate.class);
    }
}
