package com.geeksec.session.threat.detector.detection.detector.domain;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import com.geeksec.session.threat.detector.detection.DetectorType;
import com.geeksec.session.threat.detector.detection.ThreatDetector;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.DnsInfo;
import com.geeksec.session.threat.detector.model.input.HttpInfo;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import com.geeksec.session.threat.detector.model.input.SslInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 恶意域名检测器
 * 检测恶意域名访问、DGA域名和可疑域名模式
 *
 * <AUTHOR>
 */
@Slf4j
public class MaliciousDomainDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 恶意域名黑名单
    private static final Set<String> MALICIOUS_DOMAINS = new HashSet<>();
    
    // 恶意域名关键词
    private static final Set<String> MALICIOUS_KEYWORDS = new HashSet<>();
    
    // DGA域名检测模式
    private static final List<Pattern> DGA_PATTERNS = new ArrayList<>();
    
    // 可疑TLD列表
    private static final Set<String> SUSPICIOUS_TLDS = new HashSet<>();

    // 检测阈值
    private static final double DGA_ENTROPY_THRESHOLD = 3.5; // DGA域名熵值阈值
    private static final int DGA_MIN_LENGTH = 8; // DGA域名最小长度
    private static final int DGA_MAX_LENGTH = 20; // DGA域名最大长度
    private static final double CONSONANT_RATIO_THRESHOLD = 0.7; // 辅音比例阈值

    static {
        initializeMaliciousDomains();
        initializeMaliciousKeywords();
        initializeDgaPatterns();
        initializeSuspiciousTlds();
    }

    private static void initializeMaliciousDomains() {
        // 已知恶意域名黑名单
        MALICIOUS_DOMAINS.addAll(Arrays.asList(
                "malware-domain.com",
                "phishing-site.net",
                "trojan-host.org",
                "botnet-c2.info",
                "ransomware-payment.onion"
        ));
        
        log.info("初始化恶意域名黑名单，共 {} 个域名", MALICIOUS_DOMAINS.size());
    }

    private static void initializeMaliciousKeywords() {
        // 恶意域名关键词
        MALICIOUS_KEYWORDS.addAll(Arrays.asList(
                "malware", "trojan", "virus", "botnet", "phishing",
                "ransomware", "exploit", "payload", "backdoor", "rootkit",
                "keylogger", "stealer", "rat", "c2", "command"
        ));
        
        log.info("初始化恶意域名关键词，共 {} 个关键词", MALICIOUS_KEYWORDS.size());
    }

    private static void initializeDgaPatterns() {
        // DGA域名检测正则模式
        DGA_PATTERNS.add(Pattern.compile("^[a-z]{8,20}\\.(com|net|org|info|biz)$")); // 纯字母DGA
        DGA_PATTERNS.add(Pattern.compile("^[a-z0-9]{10,25}\\.(tk|ml|ga|cf)$")); // 免费域名DGA
        DGA_PATTERNS.add(Pattern.compile("^[bcdfghjklmnpqrstvwxyz]{6,}[aeiou]{1,2}[bcdfghjklmnpqrstvwxyz]{2,}\\.")); // 辅音+元音模式
        
        log.info("初始化DGA检测模式，共 {} 个模式", DGA_PATTERNS.size());
    }

    private static void initializeSuspiciousTlds() {
        // 可疑顶级域名
        SUSPICIOUS_TLDS.addAll(Arrays.asList(
                "tk", "ml", "ga", "cf", "pw", "top", "click", "download",
                "onion", "i2p", "bit", "crypto", "coin", "wallet"
        ));
        
        log.info("初始化可疑TLD列表，共 {} 个TLD", SUSPICIOUS_TLDS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.MALICIOUS_DOMAIN;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 1. DNS查询恶意域名检测
            if (event.getEventType() == NetworkEvent.EventType.DNS && event.getDnsInfo() != null) {
                DetectionResult dnsResult = detectMaliciousDnsQuery(event);
                if (dnsResult != null) {
                    results.add(dnsResult);
                }
            }

            // 2. HTTP访问恶意域名检测
            if (event.getEventType() == NetworkEvent.EventType.HTTP && event.getHttpInfo() != null) {
                DetectionResult httpResult = detectMaliciousHttpDomain(event);
                if (httpResult != null) {
                    results.add(httpResult);
                }
            }

            // 3. SSL连接恶意域名检测
            if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
                DetectionResult sslResult = detectMaliciousSslDomain(event);
                if (sslResult != null) {
                    results.add(sslResult);
                }
            }

        } catch (Exception e) {
            log.error("恶意域名检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测DNS查询中的恶意域名
     */
    private DetectionResult detectMaliciousDnsQuery(NetworkEvent event) {
        DnsInfo dnsInfo = event.getDnsInfo();
        String queryName = dnsInfo.getQueryName();
        
        if (queryName == null) {
            return null;
        }

        // 检查黑名单
        DetectionResult blacklistResult = checkDomainBlacklist(event, queryName);
        if (blacklistResult != null) {
            return blacklistResult;
        }

        // 检查DGA域名
        DetectionResult dgaResult = checkDgaDomain(event, queryName);
        if (dgaResult != null) {
            return dgaResult;
        }

        // 检查可疑模式
        DetectionResult suspiciousResult = checkSuspiciousDomainPattern(event, queryName);
        if (suspiciousResult != null) {
            return suspiciousResult;
        }

        return null;
    }

    /**
     * 检测HTTP访问中的恶意域名
     */
    private DetectionResult detectMaliciousHttpDomain(NetworkEvent event) {
        HttpInfo httpInfo = event.getHttpInfo();
        String host = httpInfo.getHost();
        
        if (host == null) {
            return null;
        }

        return checkDomainBlacklist(event, host);
    }

    /**
     * 检测SSL连接中的恶意域名
     */
    private DetectionResult detectMaliciousSslDomain(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        String serverName = sslInfo.getServerName();
        
        if (serverName == null) {
            return null;
        }

        return checkDomainBlacklist(event, serverName);
    }

    /**
     * 检查域名黑名单
     */
    private DetectionResult checkDomainBlacklist(NetworkEvent event, String domain) {
        String lowerDomain = domain.toLowerCase();
        
        // 精确匹配黑名单
        if (MALICIOUS_DOMAINS.contains(lowerDomain)) {
            return createDetectionResult(event, "MALICIOUS_DOMAIN_BLACKLIST", 
                    "恶意域名访问",
                    DetectionResult.ThreatLevel.HIGH, 0.95,
                    "访问已知恶意域名: " + domain);
        }

        // 关键词匹配
        for (String keyword : MALICIOUS_KEYWORDS) {
            if (lowerDomain.contains(keyword)) {
                return createDetectionResult(event, "MALICIOUS_DOMAIN_KEYWORD", 
                        "疑似恶意域名访问",
                        DetectionResult.ThreatLevel.MEDIUM, 0.7,
                        String.format("域名包含恶意关键词 '%s': %s", keyword, domain));
            }
        }

        return null;
    }

    /**
     * 检查DGA域名
     */
    private DetectionResult checkDgaDomain(NetworkEvent event, String domain) {
        String lowerDomain = domain.toLowerCase();
        
        // 提取域名主体部分
        String[] parts = lowerDomain.split("\\.");
        if (parts.length < 2) {
            return null;
        }
        
        String domainName = parts[0];
        String tld = parts[parts.length - 1];
        
        // 检查域名长度
        if (domainName.length() < DGA_MIN_LENGTH || domainName.length() > DGA_MAX_LENGTH) {
            return null;
        }

        // 计算熵值
        double entropy = calculateEntropy(domainName);
        if (entropy < DGA_ENTROPY_THRESHOLD) {
            return null;
        }

        // 检查辅音比例
        double consonantRatio = calculateConsonantRatio(domainName);
        if (consonantRatio < CONSONANT_RATIO_THRESHOLD) {
            return null;
        }

        // 检查是否匹配DGA模式
        for (Pattern pattern : DGA_PATTERNS) {
            if (pattern.matcher(lowerDomain).matches()) {
                return createDetectionResult(event, "DGA_DOMAIN_PATTERN", 
                        "DGA域名检测",
                        DetectionResult.ThreatLevel.HIGH, 0.85,
                        String.format("检测到DGA域名: %s，熵值: %.2f，辅音比例: %.2f", 
                                domain, entropy, consonantRatio));
            }
        }

        return null;
    }

    /**
     * 检查可疑域名模式
     */
    private DetectionResult checkSuspiciousDomainPattern(NetworkEvent event, String domain) {
        String lowerDomain = domain.toLowerCase();
        String[] parts = lowerDomain.split("\\.");
        
        if (parts.length < 2) {
            return null;
        }
        
        String tld = parts[parts.length - 1];
        
        // 检查可疑TLD
        if (SUSPICIOUS_TLDS.contains(tld)) {
            return createDetectionResult(event, "SUSPICIOUS_TLD", 
                    "可疑顶级域名",
                    DetectionResult.ThreatLevel.LOW, 0.5,
                    String.format("使用可疑顶级域名: %s", domain));
        }

        return null;
    }

    /**
     * 计算字符串熵值
     */
    private double calculateEntropy(String text) {
        if (text == null || text.isEmpty()) {
            return 0.0;
        }
        
        Map<Character, Integer> charCount = new HashMap<>();
        for (char c : text.toCharArray()) {
            charCount.put(c, charCount.getOrDefault(c, 0) + 1);
        }
        
        double entropy = 0.0;
        int length = text.length();
        
        for (int count : charCount.values()) {
            double probability = (double) count / length;
            entropy -= probability * (Math.log(probability) / Math.log(2));
        }
        
        return entropy;
    }

    /**
     * 计算辅音比例
     */
    private double calculateConsonantRatio(String text) {
        if (text == null || text.isEmpty()) {
            return 0.0;
        }
        
        int consonantCount = 0;
        String vowels = "aeiou";
        
        for (char c : text.toLowerCase().toCharArray()) {
            if (Character.isLetter(c) && !vowels.contains(String.valueOf(c))) {
                consonantCount++;
            }
        }
        
        return (double) consonantCount / text.length();
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String ruleId, String threatType,
                                                  DetectionResult.ThreatLevel level, double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .ruleId(ruleId)
                .threatType(threatType)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(event.getTimestamp())
                .build();
    }
}
