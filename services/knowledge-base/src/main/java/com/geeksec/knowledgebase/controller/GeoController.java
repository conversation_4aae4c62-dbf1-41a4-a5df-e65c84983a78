package com.geeksec.knowledgebase.controller;

import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.GeoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/geo")
@RequiredArgsConstructor
@Tag(name = "Geo Controller", description = "地理位置相关知识库API")
public class GeoController extends BaseController {

    private final GeoService geoService;

    // ==================== KnowledgeBaseClient 需要的接口 ====================

    @GetMapping("/ip/{ip}")
    @Operation(summary = "获取IP地理位置信息")
    public ApiResponse<Map<String, Object>> getGeoLocation(
            @Parameter(description = "IP地址") @PathVariable String ip) {
        log.debug("获取IP地理位置: {}", ip);
        Map<String, Object> geoInfo = geoService.getGeoLocation(ip);
        return ApiResponse.success(geoInfo);
    }

    @GetMapping("/country/code/{code}")
    @Operation(summary = "根据国家代码获取国家信息")
    public ApiResponse<Map<String, Object>> getCountryByCode(
            @Parameter(description = "国家代码") @PathVariable String code) {
        log.debug("根据国家代码获取国家信息: {}", code);
        Map<String, Object> countryInfo = geoService.getCountryByCode(code);
        return ApiResponse.success(countryInfo);
    }

    @GetMapping("/country/name/{name}")
    @Operation(summary = "根据国家名称获取国家信息")
    public ApiResponse<Map<String, Object>> getCountryByName(
            @Parameter(description = "国家名称") @PathVariable String name) {
        log.debug("根据国家名称获取国家信息: {}", name);
        Map<String, Object> countryInfo = geoService.getCountryByName(name);
        return ApiResponse.success(countryInfo);
    }

    @GetMapping("/company-suffix/country/{country}")
    @Operation(summary = "获取指定国家的公司后缀", description = "根据国家名称或代码获取其公司后缀列表")
    public ApiResponse<Map<String, Object>> getCompanySuffixesByCountry(
            @Parameter(description = "国家名称或代码") @PathVariable String country) {
        log.debug("获取国家公司后缀: {}", country);
        List<String> suffixes = geoService.getCompanySuffixesByCountry(country);
        Map<String, Object> response = Map.of(
                "country", country,
                "suffixes", suffixes,
                "count", suffixes.size()
        );
        return ApiResponse.success(response);
    }

    // ==================== 现有接口（保持兼容性） ====================

    @GetMapping("/country-suffixes/{country}")
    @Operation(summary = "获取指定国家的公司后缀（兼容接口）", description = "根据国家名称或代码获取其公司后缀列表")
    public ApiResponse<Map<String, Object>> getCompanySuffixesByCountryCompat(
            @Parameter(description = "国家名称或代码") @PathVariable String country) {
        log.debug("获取国家公司后缀（兼容接口）: {}", country);
        List<String> suffixes = geoService.getCompanySuffixesByCountry(country);
        Map<String, Object> response = Map.of(
                "country", country,
                "suffixes", suffixes,
                "count", suffixes.size()
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/company-suffix/check/{suffix}")
    @Operation(summary = "检查公司后缀", description = "检查指定的公司后缀是否为已知的有效后缀")
    public ApiResponse<Map<String, Object>> checkCompanySuffix(
            @Parameter(description = "公司后缀") @PathVariable String suffix) {
        log.debug("检查公司后缀: {}", suffix);
        List<String> countries = geoService.getCountriesBySuffix(suffix);
        boolean isValid = !countries.isEmpty();
        Map<String, Object> response = Map.of(
                "suffix", suffix,
                "isValid", isValid,
                "countries", countries
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/company-suffix/list")
    @Operation(summary = "获取所有公司后缀列表", description = "获取所有已知的公司后缀列表")
    public ApiResponse<Map<String, Object>> getAllCompanySuffixes() {
        log.debug("获取所有公司后缀列表");
        Map<String, List<String>> suffixesByCountry = geoService.getAllCompanySuffixes();
        Map<String, Object> response = Map.of(
                "suffixesByCountry", suffixesByCountry,
                "totalCountries", suffixesByCountry.size()
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/country/search")
    @Operation(summary = "搜索国家", description = "根据关键词搜索国家，支持中文名、英文名、国家代码")
    public ApiResponse<List<Map<String, Object>>> searchCountries(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        log.debug("搜索国家: {}", keyword);
        List<Map<String, Object>> results = geoService.searchCountries(keyword);
        return ApiResponse.success(results);
    }
}
