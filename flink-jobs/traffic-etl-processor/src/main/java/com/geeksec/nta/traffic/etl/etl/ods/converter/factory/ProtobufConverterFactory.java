package com.geeksec.nta.traffic.etl.etl.ods.converter.factory;

import com.geeksec.nta.traffic.etl.common.MessageType;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.converter.protocol.*;
import com.geeksec.nta.traffic.etl.etl.ods.converter.session.SessionConverter;

/**
 * Protobuf转换器工厂类
 * 根据消息类型创建对应的协议特定转换器
 *
 * <AUTHOR>
 */
public class ProtobufConverterFactory {

    /**
     * 根据消息类型获取对应的转换器
     *
     * @param messageType 消息类型
     * @return 对应的转换器实例
     * @throws IllegalArgumentException 如果消息类型不受支持
     */
    public static AbstractProtobufMessageConverter getConverter(int messageType) {
        MessageType type = MessageType.fromCode(messageType);
        if (type == null) {
            throw new IllegalArgumentException("不支持的消息类型: " + messageType);
        }

        return switch (type) {
            case SESSION -> new SessionConverter();
            case DNS -> new DnsConverter();
            case HTTP -> new HttpConverter();
            case SSL -> new SslConverter();
            case RLOGIN -> new RloginConverter();
            case TELNET -> new TelnetConverter();
            case SSH -> new SshConverter();
            case RDP -> new RdpConverter();
            case VNC -> new VncConverter();
            case XDMCP -> new XdmcpConverter();
            case NTP -> new NtpConverter();
            case ICMP -> new IcmpConverter();
            case S7 -> new S7Converter();
            case MODBUS -> new ModbusConverter();
            case IEC104 -> new Iec104Converter();
            case EIP -> new EipConverter();
            case OPC -> new OpcConverter();
            case ESP -> new EspConverter();
            case L2TP -> new L2tpConverter();
        };
    }
}
