package com.geeksec.nta.traffic.etl.etl.dim.function;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

// import com.geeksec.common.utils.knowledgebase.DomainWhoisManager;

/**
 * 域名WHOIS混合设计测试类
 * 测试新的混合设计：核心字段 + JSON完整数据
 *
 * <AUTHOR>
 */
@DisplayName("域名WHOIS混合设计测试")
public class DomainWhoisHybridDesignTest {

    private DomainWhoisManager domainWhoisManager;

    @BeforeEach
    public void setUp() {
        domainWhoisManager = DomainWhoisManager.getInstance();
    }

    @Test
    @DisplayName("测试核心字段提取")
    public void testCoreFieldsExtraction() {
        // 测试样例数据中的域名
        DomainWhoisManager.DomainWhoisInfo whoisInfo = domainWhoisManager.getDomainWhoisInfo("beetil.asia");
        
        if (whoisInfo != null) {
            // 验证核心字段
            assertNotNull(whoisInfo.getDomainName(), "域名不应为null");
            assertEquals("beetil.asia", whoisInfo.getDomainName(), "域名应该匹配");
            
            System.out.println("=== 核心字段测试 ===");
            System.out.println("域名: " + whoisInfo.getDomainName());
            System.out.println("注册商: " + whoisInfo.getRegistrarName());
            System.out.println("注册人组织: " + whoisInfo.getRegistrantOrganization());
            System.out.println("注册人国家: " + whoisInfo.getRegistrantCountry());
            System.out.println("创建日期: " + whoisInfo.getCreatedDate());
            System.out.println("更新日期: " + whoisInfo.getUpdatedDate());
            System.out.println("过期日期: " + whoisInfo.getExpiresDate());
            System.out.println("状态: " + whoisInfo.getStatus());
            System.out.println("域名服务器: " + whoisInfo.getNameServers());
            System.out.println("WHOIS服务器: " + whoisInfo.getWhoisServer());
            System.out.println("联系邮箱: " + whoisInfo.getContactEmail());
        } else {
            System.out.println("未找到beetil.asia的WHOIS信息");
        }
    }

    @Test
    @DisplayName("测试域名服务器数组解析")
    public void testNameServersArray() {
        DomainWhoisManager.DomainWhoisInfo whoisInfo = domainWhoisManager.getDomainWhoisInfo("beetil.asia");
        
        if (whoisInfo != null) {
            String[] nameServers = whoisInfo.getNameServersArray();
            
            System.out.println("=== 域名服务器数组测试 ===");
            System.out.println("域名服务器数量: " + nameServers.length);
            
            for (int i = 0; i < nameServers.length; i++) {
                System.out.println("NS" + (i + 1) + ": " + nameServers[i]);
            }
            
            // 验证数组不为空（如果有域名服务器数据的话）
            if (whoisInfo.getNameServers() != null && !whoisInfo.getNameServers().isEmpty()) {
                assertTrue(nameServers.length > 0, "域名服务器数组不应为空");
            }
        }
    }

    @Test
    @DisplayName("测试JSON格式WHOIS数据生成")
    public void testWhoisJsonGeneration() {
        DomainWhoisManager.DomainWhoisInfo whoisInfo = domainWhoisManager.getDomainWhoisInfo("beetil.asia");
        
        if (whoisInfo != null) {
            String whoisJson = whoisInfo.getWhoisJson();
            
            System.out.println("=== JSON格式WHOIS数据测试 ===");
            System.out.println("JSON长度: " + whoisJson.length());
            System.out.println("JSON内容: " + whoisJson);
            
            // 验证JSON格式
            assertNotNull(whoisJson, "JSON数据不应为null");
            assertTrue(whoisJson.startsWith("{"), "JSON应该以{开始");
            assertTrue(whoisJson.endsWith("}"), "JSON应该以}结束");
            assertTrue(whoisJson.contains("domainName"), "JSON应该包含domainName字段");
            
            // 验证JSON包含核心字段
            if (whoisInfo.getRegistrarName() != null) {
                assertTrue(whoisJson.contains("registrarName"), "JSON应该包含registrarName字段");
            }
        }
    }

    @Test
    @DisplayName("测试多个域名的混合设计")
    public void testMultipleDomainsHybridDesign() {
        String[] testDomains = {
            "beetil.asia",
            "aquasource.asia", 
            "abercrombieandfitch.asia",
            "13thstreet.asia"
        };

        System.out.println("=== 多域名混合设计测试 ===");
        
        for (String domain : testDomains) {
            DomainWhoisManager.DomainWhoisInfo whoisInfo = domainWhoisManager.getDomainWhoisInfo(domain);
            
            if (whoisInfo != null) {
                System.out.println("\n--- " + domain + " ---");
                System.out.println("注册商: " + whoisInfo.getRegistrarName());
                System.out.println("创建日期: " + whoisInfo.getCreatedDate());
                System.out.println("JSON长度: " + whoisInfo.getWhoisJson().length());
                System.out.println("摘要: " + whoisInfo.getWhoisSummary());
                
                // 验证数据一致性
                assertNotNull(whoisInfo.getDomainName(), "域名不应为null");
                assertNotNull(whoisInfo.getWhoisJson(), "JSON数据不应为null");
                assertNotNull(whoisInfo.getWhoisSummary(), "摘要不应为null");
            } else {
                System.out.println(domain + ": 无WHOIS数据");
            }
        }
    }

    @Test
    @DisplayName("测试向后兼容性")
    public void testBackwardCompatibility() {
        // 测试旧的getDomainWhois方法仍然工作
        String whoisSummary = domainWhoisManager.getDomainWhois("beetil.asia");
        
        System.out.println("=== 向后兼容性测试 ===");
        System.out.println("旧方法返回的摘要: " + whoisSummary);
        
        // 验证旧方法仍然返回有效数据
        assertNotNull(whoisSummary, "旧方法应该返回非null值");
        
        // 比较新旧方法的结果
        DomainWhoisManager.DomainWhoisInfo detailedInfo = domainWhoisManager.getDomainWhoisInfo("beetil.asia");
        if (detailedInfo != null) {
            String newSummary = detailedInfo.getWhoisSummary();
            assertEquals(whoisSummary, newSummary, "新旧方法的摘要应该一致");
        }
    }

    @Test
    @DisplayName("测试性能对比")
    public void testPerformanceComparison() {
        String domain = "beetil.asia";
        int iterations = 1000;
        
        System.out.println("=== 性能对比测试 ===");
        
        // 测试旧方法性能
        long startTime1 = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            domainWhoisManager.getDomainWhois(domain);
        }
        long duration1 = System.currentTimeMillis() - startTime1;
        
        // 测试新方法性能
        long startTime2 = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            DomainWhoisManager.DomainWhoisInfo info = domainWhoisManager.getDomainWhoisInfo(domain);
            if (info != null) {
                info.getWhoisJson(); // 包含JSON生成的时间
            }
        }
        long duration2 = System.currentTimeMillis() - startTime2;
        
        System.out.println("旧方法(" + iterations + "次): " + duration1 + "ms");
        System.out.println("新方法(" + iterations + "次): " + duration2 + "ms");
        System.out.println("性能比率: " + (double)duration2/duration1);
        
        // 新方法由于包含更多处理，可能会稍慢，但应该在合理范围内
        assertTrue(duration2 < duration1 * 5, "新方法性能不应该比旧方法慢太多");
    }

    @Test
    @DisplayName("测试数据完整性")
    public void testDataIntegrity() {
        DomainWhoisManager.DomainWhoisInfo whoisInfo = domainWhoisManager.getDomainWhoisInfo("beetil.asia");
        
        if (whoisInfo != null) {
            System.out.println("=== 数据完整性测试 ===");
            
            // 验证核心字段和JSON数据的一致性
            String json = whoisInfo.getWhoisJson();
            
            if (whoisInfo.getDomainName() != null) {
                assertTrue(json.contains(whoisInfo.getDomainName()), 
                    "JSON应该包含域名信息");
            }
            
            if (whoisInfo.getRegistrarName() != null) {
                assertTrue(json.contains(whoisInfo.getRegistrarName()), 
                    "JSON应该包含注册商信息");
            }
            
            // 验证域名服务器数据一致性
            String[] nameServers = whoisInfo.getNameServersArray();
            if (nameServers.length > 0) {
                for (String ns : nameServers) {
                    if (ns != null && !ns.trim().isEmpty()) {
                        assertTrue(json.contains(ns), 
                            "JSON应该包含域名服务器: " + ns);
                    }
                }
            }
            
            System.out.println("数据完整性验证通过");
        }
    }
}
