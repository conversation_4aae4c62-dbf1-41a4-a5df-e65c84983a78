package com.geeksec.alarm.processor.example;

import com.geeksec.alarm.processor.cache.RedisAlarmCacheManager;
import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.config.RedisConfig;
import com.geeksec.alarm.processor.suppression.RedisSuppressionCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

/**
 * Redis集成使用示例
 * 展示如何在alarm-processor中使用统一的RedisConnectionManager
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class RedisIntegrationExample {

    /**
     * 示例：在Flink算子中使用Redis缓存管理器
     */
    public static class AlarmProcessorWithRedis extends RichMapFunction<String, String> {
        
        private transient RedisAlarmCacheManager cacheManager;
        private transient RedisSuppressionCache suppressionCache;
        private AlarmProcessorConfig config;
        
        public AlarmProcessorWithRedis(AlarmProcessorConfig config) {
            this.config = config;
        }
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            
            // 初始化Redis缓存管理器
            RedisConfig redisConfig = config.getRedisConfig();
            cacheManager = new RedisAlarmCacheManager(redisConfig);
            suppressionCache = new RedisSuppressionCache(redisConfig);
            
            log.info("Redis缓存管理器初始化完成");
        }
        
        @Override
        public String map(String alarmData) throws Exception {
            try {
                // 示例1：告警去重检查
                String alarmKey = generateAlarmKey(alarmData);
                if (cacheManager.isDuplicateAlarm(alarmKey)) {
                    log.debug("发现重复告警，跳过处理: {}", alarmKey);
                    return null; // 跳过重复告警
                }
                
                // 示例2：添加到去重缓存（5分钟过期）
                cacheManager.addAlarmToDedup(alarmKey, 300);
                
                // 示例3：检查告警抑制
                String victim = extractVictim(alarmData);
                String attacker = extractAttacker(alarmData);
                String label = extractLabel(alarmData);
                
                if (suppressionCache.shouldSuppress(victim, attacker, label)) {
                    log.debug("告警被抑制，跳过处理: {}|{}|{}", victim, attacker, label);
                    return null; // 跳过被抑制的告警
                }
                
                // 示例4：增加告警统计
                String statsKey = String.format("%s:%s", label, victim);
                long count = cacheManager.incrementAlarmStats(statsKey, 3600); // 1小时过期
                log.debug("告警统计更新: {} = {}", statsKey, count);
                
                // 示例5：根据统计结果进行处理
                if (count > 10) {
                    log.warn("检测到高频告警: {} 在1小时内出现{}次", statsKey, count);
                    // 可以在这里添加高频告警的特殊处理逻辑
                }
                
                // 处理告警数据
                String processedAlarm = processAlarm(alarmData);
                
                log.debug("告警处理完成: {}", alarmKey);
                return processedAlarm;
                
            } catch (Exception e) {
                log.error("处理告警时发生错误: {}", alarmData, e);
                throw e;
            }
        }
        
        @Override
        public void close() throws Exception {
            super.close();
            
            // 关闭Redis连接
            if (cacheManager != null) {
                cacheManager.close();
            }
            if (suppressionCache != null) {
                suppressionCache.close();
            }
            
            log.info("Redis缓存管理器已关闭");
        }
        
        /**
         * 生成告警唯一标识
         */
        private String generateAlarmKey(String alarmData) {
            // 这里应该根据实际的告警数据结构来生成唯一标识
            // 示例：使用源IP、目标IP、告警类型等组合
            return "alarm:" + alarmData.hashCode();
        }
        
        /**
         * 提取受害者IP
         */
        private String extractVictim(String alarmData) {
            // 这里应该根据实际的告警数据结构来提取受害者IP
            return "*************"; // 示例
        }
        
        /**
         * 提取攻击者IP
         */
        private String extractAttacker(String alarmData) {
            // 这里应该根据实际的告警数据结构来提取攻击者IP
            return "********"; // 示例
        }
        
        /**
         * 提取告警标签
         */
        private String extractLabel(String alarmData) {
            // 这里应该根据实际的告警数据结构来提取告警标签
            return "MALWARE_DETECTED"; // 示例
        }
        
        /**
         * 处理告警数据
         */
        private String processAlarm(String alarmData) {
            // 这里应该包含实际的告警处理逻辑
            return "processed:" + alarmData;
        }
    }
    
    /**
     * 示例：配置Redis连接参数
     */
    public static RedisConfig createRedisConfig() {
        RedisConfig redisConfig = new RedisConfig();
        
        // 基础连接配置
        redisConfig.setHost("localhost");
        redisConfig.setPort(6379);
        redisConfig.setPassword(""); // 如果有密码则设置
        redisConfig.setDatabase(0);
        
        // 连接池配置
        redisConfig.setMaxTotal(20);
        redisConfig.setMaxIdle(10);
        redisConfig.setMinIdle(2);
        redisConfig.setConnectionTimeout(5000);
        redisConfig.setSocketTimeout(5000);
        
        // 缓存配置
        redisConfig.setSuppressionKeyPrefix("alarm:suppression:");
        redisConfig.setCacheExpireSeconds(3600); // 1小时过期
        
        return redisConfig;
    }
    
    /**
     * 示例：在作业中使用Redis缓存
     */
    public static void demonstrateRedisUsage() {
        log.info("=== Redis集成使用示例 ===");
        
        try {
            // 1. 创建Redis配置
            RedisConfig redisConfig = createRedisConfig();
            
            // 2. 创建缓存管理器
            RedisAlarmCacheManager cacheManager = new RedisAlarmCacheManager(redisConfig);
            
            // 3. 演示告警去重功能
            String alarmKey = "test:alarm:001";
            log.info("检查告警是否重复: {}", cacheManager.isDuplicateAlarm(alarmKey));
            
            // 4. 添加到去重缓存
            boolean added = cacheManager.addAlarmToDedup(alarmKey, 300);
            log.info("添加告警到去重缓存: {}", added);
            
            // 5. 再次检查
            log.info("再次检查告警是否重复: {}", cacheManager.isDuplicateAlarm(alarmKey));
            
            // 6. 演示抑制功能
            boolean suppressed = cacheManager.shouldSuppressAlarm("*************", "********", "MALWARE");
            log.info("检查告警是否被抑制: {}", suppressed);
            
            // 7. 添加抑制规则
            boolean suppressionAdded = cacheManager.addSuppressionRule("*************", "********", "MALWARE", 3600);
            log.info("添加抑制规则: {}", suppressionAdded);
            
            // 8. 再次检查抑制
            suppressed = cacheManager.shouldSuppressAlarm("*************", "********", "MALWARE");
            log.info("再次检查告警是否被抑制: {}", suppressed);
            
            // 9. 演示统计功能
            long count = cacheManager.incrementAlarmStats("MALWARE:*************", 3600);
            log.info("告警统计计数: {}", count);
            
            // 10. 获取统计值
            long currentCount = cacheManager.getAlarmStats("MALWARE:*************");
            log.info("当前统计值: {}", currentCount);
            
            // 11. 关闭缓存管理器
            cacheManager.close();
            
            log.info("=== Redis集成示例完成 ===");
            
        } catch (Exception e) {
            log.error("Redis集成示例执行失败", e);
        }
    }
    
    public static void main(String[] args) {
        demonstrateRedisUsage();
    }
}
