package com.geeksec.nta.traffic.etl.etl.ods.processor;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.converter.factory.ProtobufConverterFactory;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * 消息处理器，负责将不同的消息路由到对应的侧输出流
 * 直接实现ProcessFunction接口，根据消息类型将数据分发到各个侧输出流
 *
 * <AUTHOR>
 */
@Slf4j
public class MessageProcessor extends ProcessFunction<ZMPNMsg.JKNmsg, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * 处理消息并将其路由到对应的侧输出流
     * 根据消息类型获取对应的转换器，然后将消息转换为Row并输出到对应的侧输出流
     *
     * @param message 输入消息
     * @param ctx 处理函数上下文
     * @param out 主流输出收集器
     * @throws Exception 如果处理过程中发生错误
     */
    @Override
    public void processElement(ZMPNMsg.JKNmsg message, Context ctx, Collector<Row> out) throws Exception {
        if (message == null) {
            log.warn("收到空消息");
            return;
        }

        if (!message.hasType()) {
            log.warn("消息缺少协议类型: {}", message);
            return;
        }

        // 获取协议类型
        int msgType = message.getType();
        log.debug("处理协议类型: {}", msgType);

        try {
            // 获取对应的转换器
            AbstractProtobufMessageConverter converter = ProtobufConverterFactory.getConverter(msgType);
            if (converter != null) {
                // 将消息转换为Row
                Row row = converter.convert(message);

                if (row == null) {
                    log.debug("跳过空数据映射");
                    return;
                }

                // 获取对应的输出标签并路由
                OutputTag<Row> outputTag = converter.getOutputTag();
                if (outputTag != null) {
                    // 输出到侧输出流
                    ctx.output(outputTag, row);
                    log.debug("输出到侧输出流: tag={}, session_id={}",
                            outputTag.getId(), row.getField(FieldConstants.FIELD_SESSION_ID));
                } else {
                    // 如果没有对应的输出标签，直接输出到主流
                    out.collect(row);
                    log.debug("直接输出到主流: session_id={}",
                            row.getField(FieldConstants.FIELD_SESSION_ID));
                }
            } else {
                log.warn("未找到协议类型 [{}] 的转换器", msgType);
            }
        } catch (IllegalArgumentException e) {
            log.warn("不支持的协议类型: {}, 错误: {}", msgType, e.getMessage());
        } catch (Exception e) {
            log.error("处理消息时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }
}
