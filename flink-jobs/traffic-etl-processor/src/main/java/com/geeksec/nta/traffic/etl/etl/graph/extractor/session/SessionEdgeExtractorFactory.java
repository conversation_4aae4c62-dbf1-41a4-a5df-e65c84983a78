package com.geeksec.nta.traffic.etl.etl.graph.extractor.session;

import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge.*;

import java.util.Arrays;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:00$ 2025/6/17$
 **/
public class SessionEdgeExtractorFactory {
    public static List<BaseEdgeExtractor> getEdgeExtractors() {
        return Arrays.asList(
                new AppDeployServerEdgeExtractor(),
                new AppUseDomainEdgeExtractor(),
                new AppUsesIPEdgeExtractor(),
                new ClientAccessAppEdgeExtractor(),
                new ClientHttpRequestDomainEdgeExtractor(),
                new ConnectIPEdgeExtractor(),
                new ConnectMacEdgeExtractor(),
                new DomainDerivesRegDomainEdgeExtractor(),
                new IPMapsMacEdgeExtractor(),
                new FingerprintIdentifiesAppEdgeExtractor(),
                new OrgOwnsDomainEdgeExtractor(),
                new OrgOwnsIPEdgeExtractor(),
                new IPMapsMacEdgeExtractor(),
                new ServerHttpServeDomainEdgeExtractor(),
                new UAHasAppEdgeExtractor()
        );
    }
}
