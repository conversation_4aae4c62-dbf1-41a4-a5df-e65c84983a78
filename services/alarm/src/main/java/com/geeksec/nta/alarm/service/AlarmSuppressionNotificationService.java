package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.entity.AlarmSuppression;

import java.util.List;

/**
 * 告警抑制规则通知服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface AlarmSuppressionNotificationService {
    
    /**
     * 通知抑制规则添加
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     */
    void notifySuppressionAdded(String victim, String attacker, String label);
    
    /**
     * 通知抑制规则移除
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     */
    void notifySuppressionRemoved(String victim, String attacker, String label);
    
    /**
     * 通知批量抑制规则添加
     * 
     * @param suppressionItems 抑制规则项列表
     */
    void notifyBatchSuppressionAdded(List<AlarmSuppression> suppressionItems);
    
    /**
     * 通知批量抑制规则移除
     * 
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @param count 移除数量
     */
    void notifyBatchSuppressionRemoved(String victim, String attacker, String label, int count);
}
