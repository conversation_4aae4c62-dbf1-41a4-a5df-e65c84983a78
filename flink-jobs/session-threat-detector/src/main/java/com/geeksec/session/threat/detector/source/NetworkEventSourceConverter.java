package com.geeksec.session.threat.detector.source;

import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.util.Map;

/**
 * 网络事件源转换器
 * 简化版的转换器，用于基本的数据转换
 *
 * <AUTHOR>
 */
@Slf4j
public class NetworkEventSourceConverter extends RichFlatMapFunction<Map<String, Object>, NetworkEvent> {

    private static final long serialVersionUID = 1L;

    private final NetworkEvent.EventType defaultEventType;

    public NetworkEventSourceConverter(NetworkEvent.EventType defaultEventType) {
        this.defaultEventType = defaultEventType;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("网络事件源转换器初始化完成，默认事件类型: {}", defaultEventType);
    }

    @Override
    public void flatMap(Map<String, Object> rawData, Collector<NetworkEvent> out) throws Exception {
        if (rawData == null || rawData.isEmpty()) {
            return;
        }

        try {
            // 构建基础网络事件
            NetworkEvent event = NetworkEvent.builder()
                    .sessionId(getStringValue(rawData, "session_id", "sessionId"))
                    .srcIp(getStringValue(rawData, "src_ip", "srcIp"))
                    .dstIp(getStringValue(rawData, "dst_ip", "dstIp"))
                    .srcPort(getIntegerValue(rawData, "src_port", "srcPort"))
                    .dstPort(getIntegerValue(rawData, "dst_port", "dstPort"))
                    .protocol(getStringValue(rawData, "protocol"))
                    .eventType(defaultEventType)
                    .rawData(rawData)
                    .dataSource(getStringValue(rawData, "data_source", "source"))
                    .build();

            out.collect(event);

            log.debug("转换网络事件: 类型={}, 源IP={}, 目标IP={}", 
                    event.getEventType(), event.getSrcIp(), event.getDstIp());

        } catch (Exception e) {
            log.error("网络事件转换异常: {}", e.getMessage(), e);
        }
    }

    // 辅助方法：从Map中获取各种类型的值
    private String getStringValue(Map<String, Object> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    private Integer getIntegerValue(Map<String, Object> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                try {
                    return Integer.parseInt((String) value);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }
        return null;
    }
}
