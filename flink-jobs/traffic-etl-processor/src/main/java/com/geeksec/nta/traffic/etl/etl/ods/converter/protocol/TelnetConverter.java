package com.geeksec.nta.traffic.etl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for Telnet protocol messages.
 * Maps Telnet protocol data from protobuf messages to Doris
 * ods_telnet_protocol_metadata table format.
 * This converter is aligned with the latest ods_telnet_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class TelnetConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasTelnet()) {
            log.warn("JKNmsg does not contain Telnet message");
            return null;
        }
        Row row = Row.withNames();
        ZMPNMsg.telnet_msg telnet = msg.getTelnet();
        if (telnet.hasCommMsg()){
            // Common fields from Comm_msg
            enrichComMsg(row, telnet.getCommMsg());
        }

        // Telnet终端信息 (telnet_terminfo_msg)
        if (telnet.hasTerminfo()) {
            ZMPNMsg.telnet_terminfo_msg terminfo = telnet.getTerminfo();
            row.setField(FieldConstants.FIELD_TERM_WIDTH, terminfo.getTermWidth());
            row.setField(FieldConstants.FIELD_TERM_HEIGHT, terminfo.getTermHeight());
            row.setField(FieldConstants.FIELD_TERM_SPEED, terminfo.getTermSpeed());
            row.setField(FieldConstants.FIELD_TERM_TYPE, terminfo.getTermType());
        }

        // Telnet用户信息 (telnet_userinfo_msg)
        if (telnet.hasUserinfo()) {
            ZMPNMsg.telnet_userinfo_msg userinfo = telnet.getUserinfo();
            row.setField(FieldConstants.FIELD_USERNAME, userinfo.getUsername());
            row.setField(FieldConstants.FIELD_PASSWD, userinfo.getPasswd());
        }

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.TELNET_STREAM;
    }
}
