package com.geeksec.session.threat.detector.model.output;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

import com.geeksec.session.threat.detector.detection.DetectorType;

/**
 * 告警数据模型
 * 用于写入Doris告警表
 *
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class Alarm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    private String alarmId;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 告警名称
     */
    private String alarmName;

    /**
     * 告警级别
     */
    private String alarmLevel;

    /**
     * 告警描述
     */
    private String description;

    /**
     * 源IP地址
     */
    private String srcIp;

    /**
     * 目标IP地址
     */
    private String dstIp;

    /**
     * 源端口
     */
    private Integer srcPort;

    /**
     * 目标端口
     */
    private Integer dstPort;

    /**
     * 协议类型
     */
    private String protocol;

    /**
     * 检测置信度
     */
    private Double confidence;

    /**
     * 检测器类型
     */
    private DetectorType detectorType;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 匹配的特征
     */
    private String matchedSignature;

    /**
     * 告警时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 威胁类型
     */
    private String threatType;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 扩展信息
     */
    private Map<Object, Object> extensions;

    /**
     * 告警状态
     */
    private String status;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 处理备注
     */
    private String handleNote;

    /**
     * 是否已通知
     */
    private Boolean notified;

    /**
     * 通知时间
     */
    private LocalDateTime notifyTime;

    /**
     * 获取检测器显示名称
     *
     * @return 检测器显示名称，如果detectorType为null则返回null
     */
    public String getDetectorDisplayName() {
        return detectorType != null ? detectorType.getDisplayName() : null;
    }
}
