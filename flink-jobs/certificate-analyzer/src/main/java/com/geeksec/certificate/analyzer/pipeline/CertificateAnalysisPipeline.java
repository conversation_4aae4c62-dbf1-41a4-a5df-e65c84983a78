package com.geeksec.certificate.analyzer.pipeline;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SideOutputDataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import java.util.Objects;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.model.alarm.AlarmEvent;
import com.geeksec.certificate.analyzer.operator.analysis.AlarmTransformer;
import com.geeksec.certificate.analyzer.operator.analysis.CertificateOidAnalyzer;
import com.geeksec.certificate.analyzer.operator.analysis.security.CertificateSecurityAnalyzer;
import com.geeksec.certificate.analyzer.operator.analysis.threat.CertificateThreatDetector;
import com.geeksec.certificate.analyzer.operator.analysis.signature.CertificateSignatureValidator;
import com.geeksec.certificate.analyzer.operator.analysis.scoring.CertificateRiskScorer;
import com.geeksec.certificate.analyzer.operator.common.outputtags.PreprocessingOutputTags;
import com.geeksec.certificate.analyzer.operator.enrichment.CertificateMetadataExtractor;
import com.geeksec.certificate.analyzer.operator.preprocessing.deduplication.CertificateDeduplicationOperator;
import com.geeksec.certificate.analyzer.operator.validation.CertificateTrustValidator;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书分析流水线
 * 负责构建统一的、基于信任状态的证书分析数据流处理管道
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateAnalysisPipeline {

    /**
     * 构建证书分析流水线
     *
     * @param certificateStream 输入的证书数据流
     * @param parameterTool 配置参数
     * @return 流水线处理结果
     */
    public static PipelineResult build(DataStream<X509Certificate> certificateStream, ParameterTool parameterTool) {
        log.info("构建证书分析流水线");

        // 1. 预处理 - 将流分为正常流和错误流
        SingleOutputStreamOperator<X509Certificate> preprocessedStream = addPreprocessing(certificateStream);

        // 2. 统一证书处理流程（包含告警事件生成）
        PipelineResult result = addUnifiedCertificateProcessingWithAlarms(preprocessedStream, parameterTool);

        return result;
    }

    /**
     * 添加预处理步骤
     */
    private static SingleOutputStreamOperator<X509Certificate> addPreprocessing(DataStream<X509Certificate> certificateStream) {
        log.info("添加证书预处理步骤");

        return certificateStream
                .process(new ProcessFunction<X509Certificate, X509Certificate>() {
                    @Override
                    public void processElement(X509Certificate certificate, Context context, Collector<X509Certificate> out) throws Exception {
                        try {
                            // 基本验证
                            if (certificate != null && certificate.getDerSha1() != null && !certificate.getDerSha1().isEmpty()) {
                                // 输出到正常流
                                context.output(PreprocessingOutputTags.NORMAL_CERTIFICATE, certificate);
                                out.collect(certificate);
                            } else {
                                // 输出到错误流
                                context.output(PreprocessingOutputTags.CORRUPTED_CERTIFICATE, certificate);
                                log.warn("发现无效证书，已路由到错误处理流程");
                            }
                        } catch (Exception e) {
                            log.error("证书预处理失败", e);
                            context.output(PreprocessingOutputTags.CORRUPTED_CERTIFICATE, certificate);
                        }
                    }
                })
                .name("证书预处理")
                .setParallelism(4);
    }

    /**
     * 添加统一的证书处理流程（包含告警事件生成）
     */
    private static PipelineResult addUnifiedCertificateProcessingWithAlarms(SingleOutputStreamOperator<X509Certificate> preprocessedStream, ParameterTool parameterTool) {
        log.info("添加统一证书处理流程");

        // 获取正常证书流
        DataStream<X509Certificate> mainStream = preprocessedStream.getSideOutput(PreprocessingOutputTags.NORMAL_CERTIFICATE);

        // 1. 证书去重
        SingleOutputStreamOperator<X509Certificate> dedupStream = CertificateDeduplicationOperator.deduplicate(mainStream);

        // 2. 证书信任状态验证
        SingleOutputStreamOperator<X509Certificate> validatedStream = dedupStream
                .map(new CertificateTrustValidator())
                .name("证书信任状态验证")
                .setParallelism(4);

        // 3. 证书签名验证（多级验签）
        SingleOutputStreamOperator<X509Certificate> signatureValidatedStream = validatedStream
                .map(new CertificateSignatureValidator())
                .name("证书签名验证")
                .setParallelism(4);

        // 3. 证书地理与组织信息提取
        SingleOutputStreamOperator<X509Certificate> enrichedStream = signatureValidatedStream
                .map(new CertificateMetadataExtractor())
                .name("证书地理与组织信息提取")
                .setParallelism(4);

        // 4. 证书安全特征分析（暂时跳过，直接使用enrichedStream）
        SingleOutputStreamOperator<X509Certificate> securityAnalyzedStream = enrichedStream;

        // 6. 证书OID分析
        SingleOutputStreamOperator<X509Certificate> oidAnalyzedStream = securityAnalyzedStream
                .map(new CertificateOidAnalyzer())
                .name("证书OID分析")
                .setParallelism(4);

        // 7. 证书威胁检测
        SingleOutputStreamOperator<X509Certificate> threatDetectedStream = oidAnalyzedStream
                .map(new CertificateThreatDetector())
                .name("证书威胁检测")
                .setParallelism(4);

        // 8. 证书风险评分
        SingleOutputStreamOperator<X509Certificate> scoredStream = threatDetectedStream
                .map(new CertificateRiskScorer())
                .name("证书风险评分")
                .setParallelism(4);

        // 9. 生成告警事件（基于证书标签）
        DataStream<AlarmEvent> alarmEventStream = scoredStream
                .map(new AlarmTransformer())
                .filter(Objects::nonNull)
                .name("证书告警事件生成")
                .setParallelism(2);

        log.info("证书处理流水线构建完成，包含告警事件生成");

        // 获取错误流
        SideOutputDataStream<X509Certificate> errorStream = preprocessedStream
                .getSideOutput(PreprocessingOutputTags.CORRUPTED_CERTIFICATE);

        // 返回包含告警事件流的结果
        return new PipelineResult(scoredStream, alarmEventStream, errorStream, parameterTool);
    }

    /**
     * 流水线处理结果
     */
    @Data
    public static class PipelineResult {
        private final DataStream<X509Certificate> processedStream;
        private final DataStream<AlarmEvent> alarmEventStream;
        private final SideOutputDataStream<X509Certificate> errorStream;
        private final ParameterTool parameterTool;

        public PipelineResult(DataStream<X509Certificate> processedStream,
                              DataStream<AlarmEvent> alarmEventStream,
                              SideOutputDataStream<X509Certificate> errorStream,
                              ParameterTool parameterTool) {
            this.processedStream = processedStream;
            this.alarmEventStream = alarmEventStream;
            this.errorStream = errorStream;
            this.parameterTool = parameterTool;
        }
    }
}
