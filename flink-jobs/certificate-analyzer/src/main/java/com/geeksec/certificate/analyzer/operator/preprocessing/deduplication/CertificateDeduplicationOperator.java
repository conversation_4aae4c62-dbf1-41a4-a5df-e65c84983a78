package com.geeksec.certificate.analyzer.operator.preprocessing.deduplication;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.operator.preprocessing.deduplication.strategy.DeduplicationStrategy;
import com.geeksec.certificate.analyzer.operator.preprocessing.deduplication.strategy.DeduplicationStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;

/**
 * 证书去重算子
 * <p>
 * 统一的证书去重入口，支持多种去重策略。
 * 使用策略模式，可以根据配置选择不同的去重实现。
 * <p>
 * 支持的去重策略：
 * - 时间窗口去重（默认）
 * - 布隆过滤器去重
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateDeduplicationOperator {

    /**
     * 使用默认策略对证书数据流进行去重处理
     *
     * @param certificateStream 输入的证书数据流
     * @return 去重后的证书数据流
     */
    public static SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream) {
        DeduplicationStrategy strategy = DeduplicationStrategyFactory.createDefault();
        return strategy.deduplicate(certificateStream);
    }

    /**
     * 使用指定策略对证书数据流进行去重处理
     *
     * @param certificateStream 输入的证书数据流
     * @param strategyType     去重策略类型
     * @return 去重后的证书数据流
     */
    public static SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream,
            DeduplicationStrategyFactory.StrategyType strategyType) {
        DeduplicationStrategy strategy = DeduplicationStrategyFactory.createStrategy(strategyType);
        return strategy.deduplicate(certificateStream);
    }

    /**
     * 使用指定策略和配置对证书数据流进行去重处理
     *
     * @param certificateStream 输入的证书数据流
     * @param strategyType     去重策略类型
     * @param config           去重配置
     * @return 去重后的证书数据流
     */
    public static SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream,
            DeduplicationStrategyFactory.StrategyType strategyType,
            DeduplicationStrategy.DeduplicationConfig config) {
        DeduplicationStrategy strategy = DeduplicationStrategyFactory.createStrategy(strategyType, config);
        return strategy.deduplicate(certificateStream, config);
    }

    /**
     * 私有构造函数，防止实例化
     */
    private CertificateDeduplicationOperator() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
}
