package com.geeksec.nta.traffic.etl.common;

import lombok.Getter;

/**
 * 消息类型枚举
 * 定义了系统中所有支持的消息类型
 *
 * <AUTHOR>
 */
@Getter
public enum MessageType {
    /** 会话连接信息消息类型 */
    SESSION(30),
    /** DNS消息类型 */
    DNS(4),
    /** HTTP消息类型 */
    HTTP(80),
    /** SSL消息类型 */
    SSL(29),
    /** RLOGIN消息类型 */
    RLOGIN(100),
    /** TELNET消息类型 */
    TELNET(101),
    /** SSH消息类型 */
    SSH(28),
    /** RDP消息类型 */
    RDP(639),
    /** VNC消息类型 */
    VNC(104),
    /** XDMCP消息类型 */
    XDMCP(105),
    /** NTP消息类型 */
    NTP(106),
    /** ICMP消息类型 */
    ICMP(108),
    /** S7协议消息类型 */
    S7(109),
    /** Modbus协议消息类型 */
    MODBUS(110),
    /** IEC104协议消息类型 */
    IEC104(111),
    /** EIP协议消息类型 */
    EIP(112),
    /** OPC协议消息类型 */
    OPC(113),
    /** ESP协议消息类型 */
    ESP(114),
    /** L2TP协议消息类型 */
    L2TP(115);

    /** 消息类型代码 */
    private final int code;

    /**
     * 构造函数
     *
     * @param code 消息类型代码
     */
    MessageType(int code) {
        this.code = code;
    }

    /**
     * 根据消息类型代码获取对应的枚举值
     *
     * @param code 消息类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static MessageType fromCode(int code) {
        for (MessageType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }


}
