package com.geeksec.session.threat.detector.state.redis;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 检测器状态管理器
 * 负责检测器运行状态、性能指标、配置信息的Redis存储和管理
 * 
 * <AUTHOR>
 */
@Slf4j
public class DetectorStateManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Redis状态管理器
     */
    private final RedisStateManager redisStateManager;
    
    /**
     * 检测器状态过期时间（秒）- 1小时
     */
    private static final int DETECTOR_STATE_TTL = 3600;
    
    /**
     * 检测器配置过期时间（秒）- 24小时
     */
    private static final int DETECTOR_CONFIG_TTL = 24 * 3600;
    
    /**
     * 检测器性能指标过期时间（秒）- 6小时
     */
    private static final int DETECTOR_METRICS_TTL = 6 * 3600;
    
    /**
     * 构造函数
     */
    public DetectorStateManager() {
        this.redisStateManager = RedisStateManager.getInstance();
    }
    
    /**
     * 更新检测器状态
     * 
     * @param detectorName 检测器名称
     * @param state 检测器状态
     */
    public void updateDetectorState(String detectorName, DetectorState state) {
        if (detectorName == null || state == null) {
            return;
        }
        
        try {
            String stateKey = buildDetectorStateKey(detectorName);
            state.setLastUpdateTime(LocalDateTime.now());
            
            redisStateManager.setObject(stateKey, state, DETECTOR_STATE_TTL);
            
            // 更新活跃检测器集合
            String activeDetectorsKey = RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "active_detectors";
            redisStateManager.sadd(activeDetectorsKey, detectorName);
            redisStateManager.expire(activeDetectorsKey, DETECTOR_STATE_TTL);
            
            log.debug("检测器状态已更新: {} -> {}", detectorName, state.getStatus());
            
        } catch (Exception e) {
            log.error("更新检测器状态失败: {}", detectorName, e);
        }
    }
    
    /**
     * 获取检测器状态
     * 
     * @param detectorName 检测器名称
     * @return 检测器状态
     */
    public DetectorState getDetectorState(String detectorName) {
        if (detectorName == null) {
            return null;
        }
        
        try {
            String stateKey = buildDetectorStateKey(detectorName);
            return redisStateManager.getObject(stateKey, DetectorState.class);
        } catch (Exception e) {
            log.error("获取检测器状态失败: {}", detectorName, e);
            return null;
        }
    }
    
    /**
     * 获取所有活跃检测器
     * 
     * @return 活跃检测器名称列表
     */
    public List<String> getActiveDetectors() {
        try {
            String activeDetectorsKey = RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "active_detectors";
            Set<String> detectorNames = redisStateManager.smembers(activeDetectorsKey);
            return new ArrayList<>(detectorNames);
        } catch (Exception e) {
            log.error("获取活跃检测器列表失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 更新检测器配置
     * 
     * @param detectorName 检测器名称
     * @param config 检测器配置
     */
    public void updateDetectorConfig(String detectorName, DetectorConfig config) {
        if (detectorName == null || config == null) {
            return;
        }
        
        try {
            String configKey = buildDetectorConfigKey(detectorName);
            config.setLastUpdateTime(LocalDateTime.now());
            
            redisStateManager.setObject(configKey, config, DETECTOR_CONFIG_TTL);
            
            log.debug("检测器配置已更新: {}", detectorName);
            
        } catch (Exception e) {
            log.error("更新检测器配置失败: {}", detectorName, e);
        }
    }
    
    /**
     * 获取检测器配置
     * 
     * @param detectorName 检测器名称
     * @return 检测器配置
     */
    public DetectorConfig getDetectorConfig(String detectorName) {
        if (detectorName == null) {
            return null;
        }
        
        try {
            String configKey = buildDetectorConfigKey(detectorName);
            return redisStateManager.getObject(configKey, DetectorConfig.class);
        } catch (Exception e) {
            log.error("获取检测器配置失败: {}", detectorName, e);
            return null;
        }
    }
    
    /**
     * 更新检测器性能指标
     * 
     * @param detectorName 检测器名称
     * @param metrics 性能指标
     */
    public void updateDetectorMetrics(String detectorName, DetectorMetrics metrics) {
        if (detectorName == null || metrics == null) {
            return;
        }
        
        try {
            String metricsKey = buildDetectorMetricsKey(detectorName);
            metrics.setTimestamp(LocalDateTime.now());
            
            redisStateManager.setObject(metricsKey, metrics, DETECTOR_METRICS_TTL);
            
            // 更新性能指标历史（保留最近24小时的数据）
            updateMetricsHistory(detectorName, metrics);
            
            log.debug("检测器性能指标已更新: {}", detectorName);
            
        } catch (Exception e) {
            log.error("更新检测器性能指标失败: {}", detectorName, e);
        }
    }
    
    /**
     * 获取检测器性能指标
     * 
     * @param detectorName 检测器名称
     * @return 性能指标
     */
    public DetectorMetrics getDetectorMetrics(String detectorName) {
        if (detectorName == null) {
            return null;
        }
        
        try {
            String metricsKey = buildDetectorMetricsKey(detectorName);
            return redisStateManager.getObject(metricsKey, DetectorMetrics.class);
        } catch (Exception e) {
            log.error("获取检测器性能指标失败: {}", detectorName, e);
            return null;
        }
    }
    
    /**
     * 获取检测器性能指标历史
     * 
     * @param detectorName 检测器名称
     * @param hours 历史小时数
     * @return 性能指标历史列表
     */
    public List<DetectorMetrics> getDetectorMetricsHistory(String detectorName, int hours) {
        if (detectorName == null) {
            return new ArrayList<>();
        }
        
        try {
            List<DetectorMetrics> history = new ArrayList<>();
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(hours);
            
            LocalDateTime current = startTime;
            while (!current.isAfter(endTime)) {
                String historyKey = buildDetectorMetricsHistoryKey(detectorName, current);
                DetectorMetrics metrics = redisStateManager.getObject(historyKey, DetectorMetrics.class);
                if (metrics != null) {
                    history.add(metrics);
                }
                current = current.plusHours(1);
            }
            
            return history;
            
        } catch (Exception e) {
            log.error("获取检测器性能指标历史失败: {}", detectorName, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 设置检测器开关状态
     * 
     * @param detectorName 检测器名称
     * @param enabled 是否启用
     */
    public void setDetectorEnabled(String detectorName, boolean enabled) {
        if (detectorName == null) {
            return;
        }
        
        try {
            String switchKey = buildDetectorSwitchKey(detectorName);
            redisStateManager.setString(switchKey, String.valueOf(enabled), DETECTOR_CONFIG_TTL);
            
            log.info("检测器开关状态已设置: {} -> {}", detectorName, enabled);
            
        } catch (Exception e) {
            log.error("设置检测器开关状态失败: {}", detectorName, e);
        }
    }
    
    /**
     * 获取检测器开关状态
     * 
     * @param detectorName 检测器名称
     * @return 是否启用
     */
    public boolean isDetectorEnabled(String detectorName) {
        if (detectorName == null) {
            return true; // 默认启用
        }
        
        try {
            String switchKey = buildDetectorSwitchKey(detectorName);
            String enabled = redisStateManager.getString(switchKey);
            return enabled == null || Boolean.parseBoolean(enabled);
        } catch (Exception e) {
            log.error("获取检测器开关状态失败: {}", detectorName, e);
            return true; // 默认启用
        }
    }
    
    /**
     * 获取所有检测器的状态概览
     * 
     * @return 检测器状态概览
     */
    public DetectorOverview getDetectorOverview() {
        try {
            List<String> activeDetectors = getActiveDetectors();
            Map<String, DetectorState> detectorStates = new HashMap<>();
            Map<String, DetectorMetrics> detectorMetrics = new HashMap<>();
            
            int runningCount = 0;
            int stoppedCount = 0;
            int errorCount = 0;
            
            for (String detectorName : activeDetectors) {
                DetectorState state = getDetectorState(detectorName);
                if (state != null) {
                    detectorStates.put(detectorName, state);
                    
                    switch (state.getStatus()) {
                        case RUNNING:
                            runningCount++;
                            break;
                        case STOPPED:
                            stoppedCount++;
                            break;
                        case ERROR:
                            errorCount++;
                            break;
                    }
                }
                
                DetectorMetrics metrics = getDetectorMetrics(detectorName);
                if (metrics != null) {
                    detectorMetrics.put(detectorName, metrics);
                }
            }
            
            return DetectorOverview.builder()
                    .totalDetectors(activeDetectors.size())
                    .runningDetectors(runningCount)
                    .stoppedDetectors(stoppedCount)
                    .errorDetectors(errorCount)
                    .detectorStates(detectorStates)
                    .detectorMetrics(detectorMetrics)
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取检测器状态概览失败", e);
            return DetectorOverview.builder()
                    .totalDetectors(0)
                    .runningDetectors(0)
                    .stoppedDetectors(0)
                    .errorDetectors(0)
                    .detectorStates(new HashMap<>())
                    .detectorMetrics(new HashMap<>())
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 清理过期的检测器数据
     * 
     * @param maxAgeHours 最大年龄（小时）
     * @return 清理的数据数量
     */
    public long cleanupExpiredDetectorData(int maxAgeHours) {
        try {
            long deletedCount = 0;
            
            // 清理过期的状态数据
            String statePattern = RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "state:*";
            deletedCount += redisStateManager.cleanupExpiredData(statePattern, 100);
            
            // 清理过期的性能指标数据
            String metricsPattern = RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "metrics:*";
            deletedCount += redisStateManager.cleanupExpiredData(metricsPattern, 100);
            
            // 清理过期的历史数据
            String historyPattern = RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "history:*";
            deletedCount += redisStateManager.cleanupExpiredData(historyPattern, 100);
            
            log.info("清理过期检测器数据完成: 清理数量={}", deletedCount);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("清理过期检测器数据失败", e);
            return 0;
        }
    }
    
    /**
     * 更新性能指标历史
     * 
     * @param detectorName 检测器名称
     * @param metrics 性能指标
     */
    private void updateMetricsHistory(String detectorName, DetectorMetrics metrics) {
        try {
            String historyKey = buildDetectorMetricsHistoryKey(detectorName, metrics.getTimestamp());
            redisStateManager.setObject(historyKey, metrics, 24 * 3600); // 保留24小时
        } catch (Exception e) {
            log.error("更新检测器性能指标历史失败: {}", detectorName, e);
        }
    }
    
    /**
     * 构建检测器状态键
     * 
     * @param detectorName 检测器名称
     * @return 状态键
     */
    private String buildDetectorStateKey(String detectorName) {
        return RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "state:" + detectorName;
    }
    
    /**
     * 构建检测器配置键
     * 
     * @param detectorName 检测器名称
     * @return 配置键
     */
    private String buildDetectorConfigKey(String detectorName) {
        return RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "config:" + detectorName;
    }
    
    /**
     * 构建检测器性能指标键
     * 
     * @param detectorName 检测器名称
     * @return 性能指标键
     */
    private String buildDetectorMetricsKey(String detectorName) {
        return RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "metrics:" + detectorName;
    }
    
    /**
     * 构建检测器性能指标历史键
     * 
     * @param detectorName 检测器名称
     * @param timestamp 时间戳
     * @return 性能指标历史键
     */
    private String buildDetectorMetricsHistoryKey(String detectorName, LocalDateTime timestamp) {
        String hourKey = timestamp.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
        return RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "history:" + detectorName + ":" + hourKey;
    }
    
    /**
     * 构建检测器开关键
     * 
     * @param detectorName 检测器名称
     * @return 开关键
     */
    private String buildDetectorSwitchKey(String detectorName) {
        return RedisStateManager.KEY_PREFIX_DETECTOR_STATE + "switch:" + detectorName;
    }
    
    /**
     * 检测器状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectorState implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 检测器名称
         */
        private String detectorName;
        
        /**
         * 运行状态
         */
        private DetectorStatus status;
        
        /**
         * 状态描述
         */
        private String statusDescription;
        
        /**
         * 启动时间
         */
        private LocalDateTime startTime;
        
        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdateTime;
        
        /**
         * 错误信息
         */
        private String errorMessage;
        
        /**
         * 扩展属性
         */
        private Map<String, Object> extensions;
        
        /**
         * 检测器状态枚举
         */
        public enum DetectorStatus {
            RUNNING("运行中"),
            STOPPED("已停止"),
            ERROR("错误"),
            STARTING("启动中"),
            STOPPING("停止中");
            
            private final String description;
            
            DetectorStatus(String description) {
                this.description = description;
            }
            
            public String getDescription() { return description; }
        }
    }
    
    /**
     * 检测器配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectorConfig implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 检测器名称
         */
        private String detectorName;
        
        /**
         * 是否启用
         */
        private boolean enabled;
        
        /**
         * 配置参数
         */
        private Map<String, Object> parameters;
        
        /**
         * 配置版本
         */
        private String configVersion;
        
        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdateTime;
        
        /**
         * 配置描述
         */
        private String description;
    }
    
    /**
     * 检测器性能指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectorMetrics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 检测器名称
         */
        private String detectorName;
        
        /**
         * 处理数量
         */
        private long processedCount;
        
        /**
         * 检测数量
         */
        private long detectedCount;
        
        /**
         * 错误数量
         */
        private long errorCount;
        
        /**
         * 处理速率（每秒）
         */
        private double processingRate;
        
        /**
         * 检测率
         */
        private double detectionRate;
        
        /**
         * 错误率
         */
        private double errorRate;
        
        /**
         * 平均处理时间（毫秒）
         */
        private double avgProcessingTime;
        
        /**
         * 内存使用量（MB）
         */
        private double memoryUsage;
        
        /**
         * CPU使用率（%）
         */
        private double cpuUsage;
        
        /**
         * 时间戳
         */
        private LocalDateTime timestamp;
        
        /**
         * 扩展指标
         */
        private Map<String, Object> customMetrics;
    }
    
    /**
     * 检测器概览
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectorOverview implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 总检测器数量
         */
        private int totalDetectors;
        
        /**
         * 运行中的检测器数量
         */
        private int runningDetectors;
        
        /**
         * 已停止的检测器数量
         */
        private int stoppedDetectors;
        
        /**
         * 错误的检测器数量
         */
        private int errorDetectors;
        
        /**
         * 检测器状态映射
         */
        private Map<String, DetectorState> detectorStates;
        
        /**
         * 检测器性能指标映射
         */
        private Map<String, DetectorMetrics> detectorMetrics;
        
        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdateTime;
    }
}
