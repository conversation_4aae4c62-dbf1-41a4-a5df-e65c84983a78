package com.geeksec.nta.alarm.dto.suppression;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 告警抑制规则检查请求 DTO
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "告警抑制规则检查请求")
public class SuppressionCheckRequest {

    @Schema(description = "受害者IP")
    private String victim;

    @Schema(description = "攻击者IP")
    private String attacker;

    @Schema(description = "告警标签")
    private String label;

    @Schema(description = "攻击链列表")
    private List<String> attackChainList;
}