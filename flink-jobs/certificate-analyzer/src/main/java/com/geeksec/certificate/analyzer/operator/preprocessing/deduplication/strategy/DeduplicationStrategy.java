package com.geeksec.certificate.analyzer.operator.preprocessing.deduplication.strategy;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;

/**
 * 证书去重策略接口
 * <p>
 * 定义了证书去重的统一接口，支持不同的去重策略实现。
 * 各种去重策略可以实现此接口来提供不同的去重逻辑。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface DeduplicationStrategy {
    
    /**
     * 策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 对证书数据流进行去重处理
     * 
     * @param certificateStream 输入的证书数据流
     * @return 去重后的证书数据流
     */
    SingleOutputStreamOperator<X509Certificate> deduplicate(DataStream<X509Certificate> certificateStream);
    
    /**
     * 对证书数据流进行去重处理（带配置参数）
     * 
     * @param certificateStream 输入的证书数据流
     * @param config 去重配置参数
     * @return 去重后的证书数据流
     */
    default SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream, 
            DeduplicationConfig config) {
        return deduplicate(certificateStream);
    }
    
    /**
     * 去重配置参数
     */
    class DeduplicationConfig {
        private long windowSizeMs = 5000L;
        private int parallelism = 4;
        private boolean enableBloomFilter = false;
        
        public long getWindowSizeMs() {
            return windowSizeMs;
        }
        
        public DeduplicationConfig setWindowSizeMs(long windowSizeMs) {
            this.windowSizeMs = windowSizeMs;
            return this;
        }
        
        public int getParallelism() {
            return parallelism;
        }
        
        public DeduplicationConfig setParallelism(int parallelism) {
            this.parallelism = parallelism;
            return this;
        }
        
        public boolean isEnableBloomFilter() {
            return enableBloomFilter;
        }
        
        public DeduplicationConfig setEnableBloomFilter(boolean enableBloomFilter) {
            this.enableBloomFilter = enableBloomFilter;
            return this;
        }
    }
}
