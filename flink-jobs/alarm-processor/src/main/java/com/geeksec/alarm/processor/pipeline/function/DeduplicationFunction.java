package com.geeksec.alarm.processor.pipeline.function;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.util.Collector;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 告警去重功能
 * 基于时间窗口和内容哈希进行告警去重
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DeduplicationFunction extends RichFlatMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    // 去重缓存
    private transient Cache<String, DeduplicationRecord> deduplicationCache;
    
    // 指标计数器
    private transient Counter totalAlarms;
    private transient Counter duplicatedAlarms;
    private transient Counter uniqueAlarms;
    private transient Counter cacheHits;
    private transient Counter cacheMisses;
    
    public DeduplicationFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化去重缓存
        initializeCache();
        
        // 初始化指标
        initializeMetrics();
        
        log.info("告警去重功能初始化完成，模式: {}, 时间窗口: {}ms, 缓存大小: {}", 
                config.getDeduplicationMode(), 
                config.getDeduplicationTimeWindowMs(), 
                config.getDeduplicationMaxCacheSize());
    }
    
    /**
     * 初始化缓存
     */
    private void initializeCache() {
        this.deduplicationCache = Caffeine.newBuilder()
                .maximumSize(config.getDeduplicationMaxCacheSize())
                .expireAfterWrite(config.getCacheExpirationMs(), TimeUnit.MILLISECONDS)
                .recordStats()
                .build();
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-deduplication");
        
        totalAlarms = metricGroup.counter("total_alarms");
        duplicatedAlarms = metricGroup.counter("duplicated_alarms");
        uniqueAlarms = metricGroup.counter("unique_alarms");
        cacheHits = metricGroup.counter("cache_hits");
        cacheMisses = metricGroup.counter("cache_misses");
        
        // 缓存大小指标
        metricGroup.gauge("cache_size", (Gauge<Long>) () -> deduplicationCache.estimatedSize());
        metricGroup.gauge("cache_hit_rate", (Gauge<Double>) () -> deduplicationCache.stats().hitRate());
    }
    
    @Override
    public void flatMap(Alarm alarm, Collector<Alarm> out) throws Exception {
        totalAlarms.inc();
        
        try {
            String dedupKey = alarm.getDeduplicationKey();
            LocalDateTime currentTime = alarm.getEventTimestamp();
            
            // 检查缓存中是否存在相同的告警
            DeduplicationRecord existingRecord = deduplicationCache.getIfPresent(dedupKey);
            
            if (existingRecord != null) {
                cacheHits.inc();
                
                // 检查是否在时间窗口内
                if (isWithinTimeWindow(existingRecord.getLastOccurrence(), currentTime)) {
                    // 是重复告警，更新记录但不输出
                    duplicatedAlarms.inc();
                    updateDeduplicationRecord(existingRecord, alarm);
                    deduplicationCache.put(dedupKey, existingRecord);
                    
                    log.debug("检测到重复告警，已去重: {}, 累计次数: {}", 
                            alarm.getAlarmId(), existingRecord.getOccurrenceCount());
                    return;
                }
            } else {
                cacheMisses.inc();
            }
            
            // 不是重复告警，创建新记录并输出
            uniqueAlarms.inc();
            
            DeduplicationRecord newRecord = DeduplicationRecord.builder()
                    .dedupKey(dedupKey)
                    .firstOccurrence(currentTime)
                    .lastOccurrence(currentTime)
                    .occurrenceCount(1)
                    .build();
            
            deduplicationCache.put(dedupKey, newRecord);
            
            // 更新告警的去重信息
            updateAlarmDeduplicationInfo(alarm, newRecord);
            
            // 标记为已去重
            alarm.getProcessingStatus().setDeduplicated(true);
            
            out.collect(alarm);
            
            log.debug("告警去重检查通过: {}", alarm.getAlarmId());
            
        } catch (Exception e) {
            log.error("告警去重处理失败: {}", e.getMessage(), e);
            // 发生异常时直接输出告警，避免数据丢失
            out.collect(alarm);
        }
    }
    
    /**
     * 检查是否在时间窗口内
     */
    private boolean isWithinTimeWindow(LocalDateTime lastOccurrence, LocalDateTime currentTime) {
        Duration duration = Duration.between(lastOccurrence, currentTime);
        return duration.toMillis() <= config.getDeduplicationTimeWindowMs();
    }
    
    /**
     * 更新去重记录
     */
    private void updateDeduplicationRecord(DeduplicationRecord record, Alarm alarm) {
        record.setLastOccurrence(alarm.getEventTimestamp());
        record.setOccurrenceCount(record.getOccurrenceCount() + 1);
    }
    
    /**
     * 更新告警的去重信息
     */
    private void updateAlarmDeduplicationInfo(Alarm alarm, DeduplicationRecord record) {
        Alarm.DeduplicationInfo deduplicationInfo = Alarm.DeduplicationInfo.builder()
                .dedupKey(record.getDedupKey())
                .firstOccurrence(record.getFirstOccurrence())
                .lastOccurrence(record.getLastOccurrence())
                .occurrenceCount(record.getOccurrenceCount())
                .build();
        
        alarm.setDeduplicationInfo(deduplicationInfo);
    }
    
    @Override
    public void close() throws Exception {
        if (deduplicationCache != null) {
            log.info("告警去重功能关闭，去重统计: 总计={}, 重复={}, 唯一={}, 缓存命中率={:.2f}%", 
                    totalAlarms.getCount(), 
                    duplicatedAlarms.getCount(), 
                    uniqueAlarms.getCount(),
                    deduplicationCache.stats().hitRate() * 100);
            
            deduplicationCache.invalidateAll();
        }
        super.close();
    }
    
    /**
     * 去重记录
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class DeduplicationRecord {
        private String dedupKey;
        private LocalDateTime firstOccurrence;
        private LocalDateTime lastOccurrence;
        private int occurrenceCount;
    }
}
