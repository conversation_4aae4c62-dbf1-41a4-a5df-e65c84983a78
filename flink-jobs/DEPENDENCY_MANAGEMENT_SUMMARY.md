# Flink Jobs 依赖管理统一总结

## 🎯 目标

统一管理 flink-jobs 各模块的依赖，包括：
1. 在父 pom.xml 中统一定义版本号和 properties
2. 在父 pom.xml 中统一管理 dependencyManagement
3. 清理各子模块中的重复依赖和版本号
4. 统一插件管理

## ✅ 已完成的工作

### 1. 父 pom.xml 增强

#### Properties 统一管理
```xml
<!-- 版本管理统一在父 pom.xml 中定义 -->
<properties>
    <!-- 核心框架版本 -->
    <flink.version>1.20.1</flink.version>
    <kafka.clients.version>3.9.0</kafka.clients.version>
    
    <!-- 工具类库 -->
    <commons.lang3.version>3.17.0</commons.lang3.version>
    <commons.io.version>2.18.0</commons.io.version>
    <commons.codec.version>1.17.1</commons.codec.version>
    <guava.version>33.3.1-jre</guava.version>
    
    <!-- JSON 处理 -->
    <jackson.version>2.18.2</jackson.version>
    <fastjson.version>1.2.83</fastjson.version>
    <fastjson2.version>2.0.53</fastjson2.version>
    
    <!-- 数据库相关 -->
    <mysql.connector.version>8.0.40</mysql.connector.version>
    <postgresql.version>42.7.4</postgresql.version>
    <jedis.version>5.2.0</jedis.version>
    <elasticsearch.version>7.17.26</elasticsearch.version>
    
    <!-- 测试相关 -->
    <junit.version>5.11.3</junit.version>
    <mockito.version>5.14.2</mockito.version>
    <assertj.version>3.26.3</assertj.version>
    
    <!-- 构建插件版本 -->
    <maven.shade.plugin.version>3.5.0</maven.shade.plugin.version>
    <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
    <surefire.plugin.version>3.0.0-M9</surefire.plugin.version>
</properties>
```

#### DependencyManagement 完善
添加了完整的依赖管理，包括：

**核心依赖**：
- Flink 核心组件（provided scope）
- Kafka 客户端
- Jackson JSON 处理
- 工具类库（Commons、Guava）

**数据库和存储**：
- MySQL、PostgreSQL 驱动
- Redis 客户端（Jedis）
- Elasticsearch 客户端
- MinIO 客户端

**测试依赖**：
- JUnit Jupiter
- Mockito
- AssertJ
- Hamcrest

**其他工具**：
- BouncyCastle 加密库
- HTTP 客户端（OkHttp、HttpClient5）
- 缓存（Caffeine）
- 序列化（Kryo）

#### PluginManagement 统一
```xml
<pluginManagement>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>${maven.compiler.plugin.version}</version>
            <configuration>
                <source>17</source>
                <target>17</target>
                <encoding>UTF-8</encoding>
            </configuration>
        </plugin>
        
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-shade-plugin</artifactId>
            <version>${maven.shade.plugin.version}</version>
            <!-- 统一的 shade 配置 -->
        </plugin>
        
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${surefire.plugin.version}</version>
            <!-- 统一的测试配置 -->
        </plugin>
    </plugins>
</pluginManagement>
```

### 2. 子模块清理（已完成：alarm-processor）

#### 依赖版本清理
- ✅ 移除所有硬编码的版本号
- ✅ 依赖父 pom 的 dependencyManagement
- ✅ 保留必要的 scope 配置

#### 插件版本清理
- ✅ 移除插件版本号
- ✅ 使用父 pom 的 pluginManagement
- ✅ 保留模块特定的配置

#### 示例对比

**清理前**：
```xml
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>${jackson.version}</version>  <!-- 版本号冗余 -->
</dependency>

<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.11.0</version>  <!-- 版本号冗余 -->
</plugin>
```

**清理后**：
```xml
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <!-- 版本由父 pom 管理 -->
</dependency>

<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-shade-plugin</artifactId>
    <!-- 版本和基础配置由父 pom 管理 -->
</plugin>
```

## 🔄 待完成的工作

### 需要清理的子模块
1. **certificate-analyzer** - 需要清理依赖版本
2. **common** (flink-common) - 需要清理依赖版本
3. **session-threat-detector** - 需要清理依赖版本
4. **traffic-etl-processor** - 需要清理依赖版本
5. **alarm-notification** - 需要清理依赖版本
6. **alarm-cdc-sync** - 需要清理依赖版本
7. **cert-labels-cdc-sync** - 需要清理依赖版本
8. **session-labels-cdc-sync** - 需要清理依赖版本

### 清理步骤（每个模块）
1. **移除版本号**：删除所有 `<version>` 标签
2. **保留 scope**：保留必要的 `<scope>` 配置
3. **清理插件**：移除插件版本号，使用父 pom 管理
4. **移除重复依赖**：删除父 pom 已管理的重复依赖

## 📋 清理检查清单

### 依赖清理
- [ ] 移除所有硬编码版本号
- [ ] 确保所有依赖在父 pom 的 dependencyManagement 中定义
- [ ] 保留必要的 scope（test、provided、runtime）
- [ ] 移除重复的依赖声明

### 插件清理
- [ ] 移除插件版本号
- [ ] 使用父 pom 的 pluginManagement
- [ ] 保留模块特定的配置
- [ ] 确保主类配置正确

### 验证步骤
- [ ] 编译成功：`mvn compile`
- [ ] 测试通过：`mvn test`
- [ ] 打包成功：`mvn package`
- [ ] 依赖树检查：`mvn dependency:tree`

## 🎯 预期收益

### 1. 维护性提升
- **统一版本管理**：所有版本在一个地方定义
- **减少冲突**：避免子模块间版本不一致
- **简化升级**：只需在父 pom 中更新版本

### 2. 一致性保证
- **统一构建配置**：所有模块使用相同的编译和打包配置
- **统一测试配置**：保证测试环境一致性
- **统一代码规范**：通过插件配置强制执行

### 3. 开发效率
- **减少配置重复**：子模块 pom.xml 更简洁
- **快速添加依赖**：只需在父 pom 中添加一次
- **自动版本管理**：新模块自动继承版本配置

## 🔧 使用指南

### 添加新依赖
1. 在父 pom.xml 的 `<properties>` 中定义版本
2. 在父 pom.xml 的 `<dependencyManagement>` 中添加依赖
3. 在子模块中引用（不需要版本号）

### 升级依赖版本
1. 只需在父 pom.xml 的 `<properties>` 中更新版本号
2. 所有子模块自动使用新版本

### 添加新模块
1. 继承父 pom
2. 只声明需要的依赖（不需要版本号）
3. 使用父 pom 的插件管理

## ⚠️ 注意事项

1. **网络问题**：当前存在 artifactory 连接问题，可能影响依赖解析
2. **版本兼容性**：升级版本时需要注意兼容性
3. **Scope 配置**：确保正确设置依赖的 scope
4. **插件配置**：某些模块可能需要特殊的插件配置

## 📊 统计信息

- **父 pom 管理的版本数**：约 80+ 个
- **父 pom 管理的依赖数**：约 100+ 个
- **已清理的模块数**：1/9 (alarm-processor)
- **待清理的模块数**：8/9

通过这次依赖管理统一，项目的可维护性和一致性将得到显著提升。
