package com.geeksec.session.threat.detector.detection;

import com.geeksec.session.threat.detector.control.DetectorSwitchManager;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 威胁检测引擎
 * 统一管理所有威胁检测器，协调检测流程
 *
 * <AUTHOR>
 */
@Slf4j
public class DetectionEngine extends RichFlatMapFunction<NetworkEvent, DetectionResult> {

    private static final long serialVersionUID = 1L;

    /**
     * 注册的检测器列表
     */
    private final List<ThreatDetector> detectors = new ArrayList<>();

    /**
     * 检测器性能统计
     */
    private transient ConcurrentMap<String, DetectorStats> detectorStats;

    /**
     * 检测器开关管理器
     */
    private transient DetectorSwitchManager switchManager;

    /**
     * 注册威胁检测器
     *
     * @param detector 威胁检测器
     */
    public void registerDetector(ThreatDetector detector) {
        if (detector != null) {
            detectors.add(detector);
            log.info("注册威胁检测器: {} (类型: {})", 
                    detector.getDetectorType().getDisplayName(), detector.getDetectorType());
        }
    }

    /**
     * 批量注册威胁检测器
     *
     * @param detectorList 检测器列表
     */
    public void registerDetectors(List<ThreatDetector> detectorList) {
        if (detectorList != null) {
            detectorList.forEach(this::registerDetector);
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化统计信息
        detectorStats = new ConcurrentHashMap<>(detectors.size());

        // 初始化检测器开关管理器
        switchManager = DetectorSwitchManager.getInstance();
        switchManager.initialize();

        // 按优先级排序检测器
        detectors.sort(Comparator.comparingInt(ThreatDetector::getPriority));

        log.info("威胁检测引擎初始化完成，共注册 {} 个检测器", detectors.size());

        // 打印检测器信息
        for (ThreatDetector detector : detectors) {
            log.info("检测器: {} - 类型: {} - 优先级: {} - 状态: {}",
                    detector.getDetectorType().getDisplayName(),
                    detector.getDetectorType().name(),
                    detector.getPriority(),
                    detector.isEnabled() ? "启用" : "禁用");
        }

        // 打印检测器开关状态
        switchManager.printCurrentStatus();
    }

    @Override
    public void flatMap(NetworkEvent event, Collector<DetectionResult> out) throws Exception {
        if (event == null) {
            return;
        }

        // 遍历所有检测器进行检测
        for (ThreatDetector detector : detectors) {
            // 检查检测器本身是否启用
            if (!detector.isEnabled()) {
                continue;
            }

            // 检查检测器开关管理器中的状态
            if (!isDetectorEnabled(detector)) {
                if (log.isDebugEnabled()) {
                    log.debug("检测器 {} 被开关管理器禁用，跳过检测", detector.getDetectorType().getDisplayName());
                }
                continue;
            }

            try {
                long startTime = System.currentTimeMillis();

                // 执行检测
                List<DetectionResult> results = detector.detect(event);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                // 更新统计信息
                updateDetectorStats(detector.getDetectorType().name(), duration, results.size());

                // 输出检测结果
                if (results != null && !results.isEmpty()) {
                    for (DetectionResult result : results) {
                        // 确保检测结果包含必要信息
                        enrichDetectionResult(result, event);
                        out.collect(result);

                        log.info("检测到威胁: 检测器={}, 威胁类型={}, 源IP={}, 目标IP={}, 置信度={}",
                                result.getDetectorType().getDisplayName(),
                                result.getThreatType(),
                                result.getSrcIp(),
                                result.getDstIp(),
                                result.getConfidence());
                    }
                }

            } catch (Exception e) {
                log.error("检测器 {} 执行异常: {}", detector.getDetectorType().getDisplayName(), e.getMessage(), e);
                // 记录异常统计
                updateDetectorStats(detector.getDetectorType().name(), 0, -1);
            }
        }
    }

    /**
     * 检查检测器是否被开关管理器启用
     *
     * @param detector 威胁检测器
     * @return 是否启用
     */
    private boolean isDetectorEnabled(ThreatDetector detector) {
        if (switchManager == null) {
            log.warn("检测器开关管理器未初始化，默认启用检测器: {}", detector.getDetectorType().getDisplayName());
            return true;
        }

        // 通过检测器名称查找对应的检测器类型
        return switchManager.isDetectorEnabled(detector.getDetectorType().name());
    }

    /**
     * 丰富检测结果信息
     *
     * @param result 检测结果
     * @param event 原始事件
     */
    private void enrichDetectionResult(DetectionResult result, NetworkEvent event) {
        if (result.getSessionId() == null) {
            result.setSessionId(event.getSessionId());
        }
        if (result.getSrcIp() == null) {
            result.setSrcIp(event.getSrcIp());
        }
        if (result.getDstIp() == null) {
            result.setDstIp(event.getDstIp());
        }
        if (result.getSrcPort() == null) {
            result.setSrcPort(event.getSrcPort());
        }
        if (result.getDstPort() == null) {
            result.setDstPort(event.getDstPort());
        }
        if (result.getProtocol() == null) {
            result.setProtocol(event.getProtocol());
        }
        if (result.getDetectionTime() == null) {
            result.setDetectionTime(event.getTimestamp());
        }
    }

    /**
     * 更新检测器统计信息
     *
     * @param detectorName 检测器名称
     * @param duration 执行时间
     * @param resultCount 结果数量，-1表示异常
     */
    private void updateDetectorStats(String detectorName, long duration, int resultCount) {
        detectorStats.compute(detectorName, (name, stats) -> {
            if (stats == null) {
                stats = new DetectorStats();
            }
            stats.updateStats(duration, resultCount);
            return stats;
        });
    }

    /**
     * 获取检测器统计信息
     *
     * @return 统计信息
     */
    public ConcurrentMap<String, DetectorStats> getDetectorStats() {
        return detectorStats;
    }

    /**
     * 获取检测器开关管理器
     *
     * @return 检测器开关管理器
     */
    public DetectorSwitchManager getSwitchManager() {
        return switchManager;
    }

    /**
     * 获取启用的检测器数量
     *
     * @return 启用的检测器数量
     */
    public int getEnabledDetectorCount() {
        if (switchManager == null) {
            return (int) detectors.stream().filter(ThreatDetector::isEnabled).count();
        }

        return (int) detectors.stream()
                .filter(ThreatDetector::isEnabled)
                .filter(this::isDetectorEnabled)
                .count();
    }

    /**
     * 获取所有注册的检测器数量
     *
     * @return 检测器总数
     */
    public int getTotalDetectorCount() {
        return detectors.size();
    }

    /**
     * 打印检测器状态摘要
     */
    public void printDetectorSummary() {
        log.info("=== 威胁检测引擎状态摘要 ===");
        log.info("注册检测器总数: {}", getTotalDetectorCount());
        log.info("启用检测器数量: {}", getEnabledDetectorCount());

        if (switchManager != null) {
            log.info("开关管理器状态: {}", switchManager.getStatistics());
        }

        if (detectorStats != null && !detectorStats.isEmpty()) {
            log.info("检测器性能统计:");
            detectorStats.forEach((name, stats) -> {
                log.info("  {}: 执行次数={}, 平均耗时={:.2f}ms, 检测率={:.2f}%, 错误率={:.2f}%",
                        name, stats.getTotalExecutions(), stats.getAverageExecutionTime(),
                        stats.getDetectionRate() * 100, stats.getErrorRate() * 100);
            });
        }
        log.info("=== 状态摘要完成 ===");
    }

    /**
     * 检测器统计信息
     */
    public static class DetectorStats {
        private long totalExecutions = 0;
        private long totalDuration = 0;
        private long totalDetections = 0;
        private long errorCount = 0;

        public void updateStats(long duration, int resultCount) {
            totalExecutions++;
            totalDuration += duration;
            if (resultCount >= 0) {
                totalDetections += resultCount;
            } else {
                errorCount++;
            }
        }

        public double getAverageExecutionTime() {
            return totalExecutions > 0 ? (double) totalDuration / totalExecutions : 0;
        }

        public double getDetectionRate() {
            return totalExecutions > 0 ? (double) totalDetections / totalExecutions : 0;
        }

        public double getErrorRate() {
            return totalExecutions > 0 ? (double) errorCount / totalExecutions : 0;
        }

        // Getters
        public long getTotalExecutions() { return totalExecutions; }
        public long getTotalDuration() { return totalDuration; }
        public long getTotalDetections() { return totalDetections; }
        public long getErrorCount() { return errorCount; }
    }
}
