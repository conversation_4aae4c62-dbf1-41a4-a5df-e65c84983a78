package com.geeksec.certificate.analyzer.sink;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.pipeline.CertificateProcessingPipeline;
import com.geeksec.certificate.analyzer.sink.minio.MinioFileSinkFactory;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书输出管理器
 * 负责配置和管理所有证书输出组件
 *
 * <AUTHOR>
 */
@Slf4j
public final class CertificateOutputManager {

    private CertificateOutputManager() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }

    /**
     * 配置所有输出组件
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    public static void configureAllOutputs(CertificateProcessingPipeline.PipelineResult pipelineResult,
                                         ParameterTool config) {
        log.info("配置所有输出组件");

        DataStream<X509Certificate> processedStream = pipelineResult.getProcessedStream();
        DataStream<X509Certificate> errorStream = pipelineResult.getErrorStream();

        // 配置正常证书输出
        configureNormalCertificateOutputs(processedStream, config);

        // 配置错误证书输出
        configureErrorCertificateOutputs(errorStream, config);

        log.info("所有输出组件配置完成");
    }

    /**
     * 配置正常证书输出
     *
     * @param certificateStream 正常证书数据流
     * @param config 配置参数
     */
    private static void configureNormalCertificateOutputs(DataStream<X509Certificate> certificateStream, 
                                                        ParameterTool config) {
        log.info("配置正常证书输出");

        // MinIO文件存储输出（总是启用）
        certificateStream
                .addSink(MinioFileSinkFactory.createCertificateSink())
                .name("证书MinIO存储")
                .setParallelism(CertificateAnalyzerConfig.getAnalyzerParallelism());
        log.info("已添加证书MinIO存储输出");

        // Doris数据库输出（如果启用）
        if (CertificateAnalyzerConfig.isDorisEnabled()) {
            configureDorisOutput(certificateStream, config);
        }

        // Nebula图数据库输出（如果启用）
        if (CertificateAnalyzerConfig.isNebulaEnabled()) {
            configureNebulaOutput(certificateStream, config);
        }

        // PostgreSQL输出（如果启用）
        if (CertificateAnalyzerConfig.isPostgreSQLEnabled()) {
            configurePostgreSQLOutput(certificateStream, config);
        }

        // Redis缓存输出（如果启用）
        // TODO: 添加Redis配置检查方法
        // if (CertificateAnalyzerConfig.isRedisEnabled()) {
        //     configureRedisOutput(certificateStream, config);
        // }
    }

    /**
     * 配置错误证书输出
     *
     * @param errorStream 错误证书数据流
     * @param config 配置参数
     */
    private static void configureErrorCertificateOutputs(DataStream<X509Certificate> errorStream, 
                                                        ParameterTool config) {
        log.info("配置错误证书输出");

        // 错误证书也存储到MinIO，但使用不同的路径
        errorStream
                .addSink(MinioFileSinkFactory.createErrorCertificateSink())
                .name("错误证书MinIO存储")
                .setParallelism(2);
        log.info("已添加错误证书MinIO存储输出");
    }

    /**
     * 配置Doris输出
     */
    private static void configureDorisOutput(DataStream<X509Certificate> certificateStream, 
                                           ParameterTool config) {
        log.info("配置Doris输出");
        
        // TODO: 实现Doris输出
        // certificateStream
        //         .addSink(new DorisCertificateSink())
        //         .name("证书Doris输出")
        //         .setParallelism(CertificateAnalyzerConfig.getDorisParallelism());
        
        log.warn("Doris输出功能待实现");
    }

    /**
     * 配置Nebula输出
     */
    private static void configureNebulaOutput(DataStream<X509Certificate> certificateStream, 
                                            ParameterTool config) {
        log.info("配置Nebula输出");
        
        // TODO: 实现Nebula输出
        // certificateStream
        //         .addSink(new NebulaCertificateSink())
        //         .name("证书关系图Nebula输出")
        //         .setParallelism(CertificateAnalyzerConfig.getNebulaParallelism());
        
        log.warn("Nebula输出功能待实现");
    }

    /**
     * 配置PostgreSQL输出
     */
    private static void configurePostgreSQLOutput(DataStream<X509Certificate> certificateStream, 
                                                ParameterTool config) {
        log.info("配置PostgreSQL输出");
        
        // TODO: 实现PostgreSQL输出
        // certificateStream
        //         .addSink(new PostgreSQLCertificateSink())
        //         .name("证书PostgreSQL输出")
        //         .setParallelism(CertificateAnalyzerConfig.getPostgreSQLParallelism());
        
        log.warn("PostgreSQL输出功能待实现");
    }

    /**
     * 配置Redis输出
     */
    private static void configureRedisOutput(DataStream<X509Certificate> certificateStream, 
                                           ParameterTool config) {
        log.info("配置Redis输出");
        
        // TODO: 实现Redis输出
        // certificateStream
        //         .addSink(new RedisCertificateSink())
        //         .name("证书Redis缓存输出")
        //         .setParallelism(CertificateAnalyzerConfig.getRedisParallelism());
        
        log.warn("Redis输出功能待实现");
    }

    /**
     * 添加证书相关的输出（简化版本）
     *
     * @param certificateStream 证书数据流
     */
    public static void addCertificateOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加证书相关输出");

        // 证书存储输出
        certificateStream.addSink(MinioFileSinkFactory.createCertificateSink())
                .name("证书MinIO存储")
                .setParallelism(CertificateAnalyzerConfig.getAnalyzerParallelism());
        log.info("已添加证书MinIO存储输出");

        log.info("证书相关输出添加完成");
    }
}
