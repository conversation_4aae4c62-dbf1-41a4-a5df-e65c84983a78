package com.geeksec.session.threat.detector.formatting;

import com.geeksec.session.threat.detector.formatting.model.AlarmFormattedContent;
import com.geeksec.session.threat.detector.formatting.model.FormattingContext;
import com.geeksec.session.threat.detector.model.output.Alarm;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警格式化测试类
 * 
 * <AUTHOR>
 */
public class AlarmFormattingTest {
    
    private static final Logger log = LoggerFactory.getLogger(AlarmFormattingTest.class);
    
    private AlarmFormatter alarmFormatter;
    
    @BeforeEach
    void setUp() {
        alarmFormatter = new AlarmFormatter();
    }
    
    @Test
    void testBasicFormatting() {
        log.info("测试基础格式化功能");
        
        // 创建测试告警
        Alarm alarm = createTestAlarm("恶意软件", "高危恶意软件威胁", "*************", "************0");
        
        // 执行格式化
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm);
        
        // 验证结果
        assertNotNull(formatted);
        assertEquals(alarm.getAlarmId(), formatted.getAlarmId());
        assertNotNull(formatted.getSummary());
        assertNotNull(formatted.getReasonAnalysis());
        assertNotNull(formatted.getHandlingSuggestions());
        assertNotNull(formatted.getPrincipleExplanation());
        
        log.info("基础格式化测试通过");
        log.info("简短描述: {}", formatted.getSummary().getBriefDescription());
        log.info("详细描述: {}", formatted.getSummary().getDetailedDescription());
    }
    
    @Test
    void testBriefFormatting() {
        log.info("测试简洁格式化");
        
        Alarm alarm = createTestAlarm("网络扫描", "端口扫描活动", "********", "********00");
        FormattingContext context = FormattingContext.createBrief();
        
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm, context);
        
        assertNotNull(formatted);
        assertEquals(AlarmFormattedContent.FormattingStrategy.BRIEF, formatted.getStrategy());
        assertNotNull(formatted.getSummary());
        assertNotNull(formatted.getSummary().getBriefDescription());
        
        // 简洁格式不应包含详细描述
        assertNull(formatted.getSummary().getDetailedDescription());
        
        log.info("简洁格式化测试通过");
        log.info("简短描述: {}", formatted.getSummary().getBriefDescription());
    }
    
    @Test
    void testTechnicalFormatting() {
        log.info("测试技术格式化");
        
        Alarm alarm = createTestAlarm("DNS隧道", "DNS隧道通信检测", "*******", "************");
        FormattingContext context = FormattingContext.createTechnical();
        
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm, context);
        
        assertNotNull(formatted);
        assertEquals(AlarmFormattedContent.FormattingStrategy.TECHNICAL, formatted.getStrategy());
        assertNotNull(formatted.getPrincipleExplanation());
        assertNotNull(formatted.getPrincipleExplanation().getDetectionPrinciple());
        assertNotNull(formatted.getPrincipleExplanation().getTechnicalBackground());
        
        log.info("技术格式化测试通过");
        log.info("检测原理: {}", formatted.getPrincipleExplanation().getDetectionPrinciple());
    }
    
    @Test
    void testReasonAnalysis() {
        log.info("测试原因分析生成");
        
        Alarm alarm = createTestAlarm("Webshell", "Web后门检测", "************", "************");
        alarm.setConfidence(0.95);
        
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm);
        
        assertNotNull(formatted.getReasonAnalysis());
        assertNotNull(formatted.getReasonAnalysis().getDetectionReasons());
        assertFalse(formatted.getReasonAnalysis().getDetectionReasons().isEmpty());
        assertNotNull(formatted.getReasonAnalysis().getTechnicalAnalysis());
        assertNotNull(formatted.getReasonAnalysis().getConfidenceAnalysis());
        
        log.info("原因分析测试通过");
        log.info("检测原因数量: {}", formatted.getReasonAnalysis().getDetectionReasons().size());
        log.info("置信度分析: {}", formatted.getReasonAnalysis().getConfidenceAnalysis());
    }
    
    @Test
    void testHandlingSuggestions() {
        log.info("测试处理建议生成");
        
        Alarm alarm = createTestAlarm("恶意软件", "勒索软件检测", "192.168.1.5", "***********5");
        alarm.setConfidence(0.98);
        
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm);
        
        assertNotNull(formatted.getHandlingSuggestions());
        assertNotNull(formatted.getHandlingSuggestions().getImmediateActions());
        assertFalse(formatted.getHandlingSuggestions().getImmediateActions().isEmpty());
        assertNotNull(formatted.getHandlingSuggestions().getPriorityGuidance());
        
        log.info("处理建议测试通过");
        log.info("立即处理建议数量: {}", formatted.getHandlingSuggestions().getImmediateActions().size());
        
        // 打印处理建议
        for (AlarmFormattedContent.ActionSuggestion action : formatted.getHandlingSuggestions().getImmediateActions()) {
            log.info("建议: {} - {}", action.getTitle(), action.getDescription());
        }
    }
    
    @Test
    void testPrincipleExplanation() {
        log.info("测试原理说明生成");
        
        Alarm alarm = createTestAlarm("加密工具", "恶意加密通信", "***********", "************");
        
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm);
        
        assertNotNull(formatted.getPrincipleExplanation());
        assertNotNull(formatted.getPrincipleExplanation().getDetectionPrinciple());
        assertNotNull(formatted.getPrincipleExplanation().getTechnicalBackground());
        assertNotNull(formatted.getPrincipleExplanation().getAttackPrinciple());
        assertNotNull(formatted.getPrincipleExplanation().getDetectionMethod());
        
        log.info("原理说明测试通过");
        log.info("检测原理: {}", formatted.getPrincipleExplanation().getDetectionPrinciple());
        log.info("技术背景: {}", formatted.getPrincipleExplanation().getTechnicalBackground());
    }
    
    @Test
    void testImpactAnalysis() {
        log.info("测试影响分析生成");
        
        Alarm alarm = createTestAlarm("Webshell", "关键系统后门", "*************", "************0");
        FormattingContext context = FormattingContext.createDefault();
        context.setStrategy(AlarmFormattedContent.FormattingStrategy.DETAILED);
        
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm, context);
        
        assertNotNull(formatted.getImpactAnalysis());
        assertNotNull(formatted.getImpactAnalysis().getBusinessImpact());
        assertNotNull(formatted.getImpactAnalysis().getTechnicalImpact());
        assertNotNull(formatted.getImpactAnalysis().getSecurityImpact());
        
        log.info("影响分析测试通过");
        log.info("业务影响: {}", formatted.getImpactAnalysis().getBusinessImpact());
        log.info("技术影响: {}", formatted.getImpactAnalysis().getTechnicalImpact());
    }
    
    @Test
    void testFormattingStrategies() {
        log.info("测试不同格式化策略");
        
        Alarm alarm = createTestAlarm("网络扫描", "大规模端口扫描", "********", "192.168.1.0/24");
        
        // 测试所有格式化策略
        for (AlarmFormattedContent.FormattingStrategy strategy : AlarmFormattedContent.FormattingStrategy.values()) {
            FormattingContext context = FormattingContext.createDefault();
            context.setStrategy(strategy);
            
            AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm, context);
            
            assertNotNull(formatted);
            assertEquals(strategy, formatted.getStrategy());
            assertNotNull(formatted.getSummary());
            
            log.info("策略 {} 测试通过，摘要: {}", 
                    strategy.getDescription(), formatted.getSummary().getBriefDescription());
        }
    }
    
    @Test
    void testMultipleThreats() {
        log.info("测试多种威胁类型格式化");
        
        String[] threatTypes = {"恶意软件", "网络扫描", "DNS隧道", "Webshell", "加密工具"};
        
        for (String threatType : threatTypes) {
            Alarm alarm = createTestAlarm(threatType, threatType + "检测", "***********", "***********");
            
            AlarmFormattedContent formatted = alarmFormatter.formatAlarm(alarm);
            
            assertNotNull(formatted);
            assertNotNull(formatted.getSummary());
            assertNotNull(formatted.getReasonAnalysis());
            assertNotNull(formatted.getHandlingSuggestions());
            
            log.info("威胁类型 {} 格式化测试通过", threatType);
        }
    }
    
    @Test
    void testFormattingCache() {
        log.info("测试格式化缓存");
        
        Alarm alarm = createTestAlarm("恶意软件", "缓存测试", "***********", "***********");
        
        // 第一次格式化
        long startTime = System.currentTimeMillis();
        AlarmFormattedContent formatted1 = alarmFormatter.formatAlarm(alarm);
        long firstTime = System.currentTimeMillis() - startTime;
        
        // 第二次格式化（应该使用缓存）
        startTime = System.currentTimeMillis();
        AlarmFormattedContent formatted2 = alarmFormatter.formatAlarm(alarm);
        long secondTime = System.currentTimeMillis() - startTime;
        
        assertNotNull(formatted1);
        assertNotNull(formatted2);
        
        // 检查统计信息
        AlarmFormatter.FormattingStatistics stats = alarmFormatter.getStatistics();
        assertTrue(stats.getCacheHits() > 0);
        
        log.info("缓存测试通过");
        log.info("第一次格式化时间: {}ms, 第二次格式化时间: {}ms", firstTime, secondTime);
        log.info("缓存命中率: {:.2f}%", stats.getCacheHitRate() * 100);
    }
    
    @Test
    void testErrorHandling() {
        log.info("测试错误处理");
        
        // 测试空告警
        AlarmFormattedContent formatted = alarmFormatter.formatAlarm(null);
        assertNull(formatted);
        
        // 测试不完整的告警
        Alarm incompleteAlarm = Alarm.builder()
                .alarmId("incomplete-alarm")
                .build();
        
        AlarmFormattedContent formattedIncomplete = alarmFormatter.formatAlarm(incompleteAlarm);
        assertNotNull(formattedIncomplete);
        assertNotNull(formattedIncomplete.getSummary());
        
        log.info("错误处理测试通过");
    }
    
    @Test
    void testFormattingStatistics() {
        log.info("测试格式化统计");
        
        // 执行多次格式化
        for (int i = 0; i < 10; i++) {
            Alarm alarm = createTestAlarm("网络扫描", "测试扫描 " + i, "192.168.1." + i, "*************");
            alarmFormatter.formatAlarm(alarm);
        }
        
        AlarmFormatter.FormattingStatistics stats = alarmFormatter.getStatistics();
        
        assertTrue(stats.getTotalFormatted() >= 10);
        assertTrue(stats.getCacheHitRate() >= 0.0);
        assertTrue(stats.getErrorRate() >= 0.0);
        
        log.info("统计信息测试通过");
        log.info("总格式化数: {}", stats.getTotalFormatted());
        log.info("缓存命中率: {:.2f}%", stats.getCacheHitRate() * 100);
        log.info("错误率: {:.2f}%", stats.getErrorRate() * 100);
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String threatType, String alarmName, String srcIp, String dstIp) {
        return Alarm.builder()
                .alarmId("test-alarm-" + System.nanoTime())
                .alarmName(alarmName)
                .alarmType("高危威胁")
                .threatType(threatType)
                .srcIp(srcIp)
                .dstIp(dstIp)
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .description("这是一个测试告警：" + alarmName)
                .detectorName("TestDetector")
                .confidence(0.85)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
