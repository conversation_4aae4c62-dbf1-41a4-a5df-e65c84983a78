package com.geeksec.certificate.analyzer.util;

import java.nio.charset.StandardCharsets;

import org.junit.jupiter.api.Test;

import com.geeksec.certificate.analyzer.util.cert.CertificateDistanceUtil;

import org.junit.jupiter.api.Assertions;

/**
 * CertificateDistanceUtil 测试类
 * 
 * <AUTHOR>
 */
class CertificateDistanceUtilTest {

    @Test
    void testCalculateMinDistance_SameBytes() {
        byte[] bytes1 = "hello".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "hello".getBytes(StandardCharsets.UTF_8);
        
        int distance = CertificateDistanceUtil.calculateMinDistance(bytes1, bytes2);
        Assertions.assertEquals(0, distance, "相同字节数组的距离应该为0");
    }

    @Test
    void testCalculateMinDistance_DifferentBytes() {
        byte[] bytes1 = "hello".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "world".getBytes(StandardCharsets.UTF_8);
        
        int distance = CertificateDistanceUtil.calculateMinDistance(bytes1, bytes2);
        Assertions.assertTrue(distance > 0, "不同字节数组的距离应该大于0");
    }

    @Test
    void testCalculateMinDistance_EmptyArrays() {
        byte[] empty1 = new byte[0];
        byte[] empty2 = new byte[0];
        
        int distance = CertificateDistanceUtil.calculateMinDistance(empty1, empty2);
        Assertions.assertEquals(0, distance, "两个空数组的距离应该为0");
    }

    @Test
    void testCalculateMinDistance_OneEmpty() {
        byte[] empty = new byte[0];
        byte[] nonEmpty = "hello".getBytes(StandardCharsets.UTF_8);
        
        int distance1 = CertificateDistanceUtil.calculateMinDistance(empty, nonEmpty);
        int distance2 = CertificateDistanceUtil.calculateMinDistance(nonEmpty, empty);
        
        Assertions.assertEquals(nonEmpty.length, distance1, "空数组与非空数组的距离应该等于非空数组的长度");
        Assertions.assertEquals(nonEmpty.length, distance2, "非空数组与空数组的距离应该等于非空数组的长度");
    }

    @Test
    void testCalculateMinDistance_NullInput() {
        byte[] bytes = "hello".getBytes(StandardCharsets.UTF_8);
        
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            CertificateDistanceUtil.calculateMinDistance(null, bytes);
        }, "null输入应该抛出IllegalArgumentException");
        
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            CertificateDistanceUtil.calculateMinDistance(bytes, null);
        }, "null输入应该抛出IllegalArgumentException");
    }

    @Test
    void testCheckDistance_ByteFlip() {
        byte[] bytes1 = "hello".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "hallo".getBytes(StandardCharsets.UTF_8); // 1个字符不同
        
        boolean result = CertificateDistanceUtil.checkDistance(bytes1, bytes2, "ByteFlip");
        Assertions.assertTrue(result, "ByteFlip类型，距离为1，应该在安全范围内");
    }

    @Test
    void testCheckDistance_ByteLoss() {
        byte[] bytes1 = "hello world test".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "hello world".getBytes(StandardCharsets.UTF_8); // 删除了一些字符
        
        boolean result = CertificateDistanceUtil.checkDistance(bytes1, bytes2, "ByteLoss");
        Assertions.assertTrue(result, "ByteLoss类型，距离较小，应该在安全范围内");
    }

    @Test
    void testCheckDistance_ByteRedundancy() {
        byte[] bytes1 = "hello".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "hello world extra".getBytes(StandardCharsets.UTF_8); // 添加了一些字符
        
        boolean result = CertificateDistanceUtil.checkDistance(bytes1, bytes2, "ByteRedundancy");
        Assertions.assertTrue(result, "ByteRedundancy类型，距离较小，应该在安全范围内");
    }

    @Test
    void testCheckDistance_TooLargeDistance() {
        byte[] bytes1 = "hello".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "completely different string".getBytes(StandardCharsets.UTF_8);
        
        boolean result = CertificateDistanceUtil.checkDistance(bytes1, bytes2, "ByteFlip");
        Assertions.assertFalse(result, "距离过大，应该超出安全范围");
    }

    @Test
    void testCheckDistance_NullInput() {
        byte[] bytes = "hello".getBytes(StandardCharsets.UTF_8);
        
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            CertificateDistanceUtil.checkDistance(null, bytes, "ByteFlip");
        }, "null输入应该抛出IllegalArgumentException");
        
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            CertificateDistanceUtil.checkDistance(bytes, null, "ByteFlip");
        }, "null输入应该抛出IllegalArgumentException");
        
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            CertificateDistanceUtil.checkDistance(bytes, bytes, null);
        }, "null错误类型应该抛出IllegalArgumentException");
    }

    @Test
    void testCheckDistance_UnknownErrorType() {
        byte[] bytes1 = "hello".getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = "hallo".getBytes(StandardCharsets.UTF_8);
        
        boolean result = CertificateDistanceUtil.checkDistance(bytes1, bytes2, "UnknownType");
        Assertions.assertTrue(result, "未知错误类型应该使用默认安全距离2");
    }
}
