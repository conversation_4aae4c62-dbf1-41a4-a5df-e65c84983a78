package com.geeksec.nta.traffic.etl.etl.graph.extractor.dns.edge;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.network.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class CnamePointsDomainEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper mapper = new ObjectMapper();
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CNAME_POINTS_TO_DOMAIN_TAG;
    }

    /**
     * CNAME指向目标域名 (DOMAIN -> DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        // 获取源IP和域名
        String dnsAnsStr = value.getField(FieldConstants.FIELD_DNS_ANSWER).toString();
        if (dnsAnsStr.isEmpty()) {
            return edges;
        }

        // 解析DNS查询数据
        try {
            List<Map<String, Object>> dnsAnsMaps = mapper.readValue(dnsAnsStr, new TypeReference<List<Map<String, Object>>>() {});
            for (Map<String, Object> dnsAns : dnsAnsMaps) {
                Integer type = Integer.valueOf(dnsAns.get("type").toString());
                if (type == 5) {
                    String srcName = dnsAns.get("name").toString();
                    String dstName = dnsAns.get("value").toString();
                    srcName = DomainUtils.formatDomain(srcName);
                    dstName = DomainUtils.formatDomain(dstName);
                    edges.add(Row.of(srcName, dstName,
                            0 // rank暂定0
                    ));
                }
            }
        } catch (JsonProcessingException | NumberFormatException e) {
            log.error("解析DNS查询数据JSON失败", e);
        }

        return edges;
    }
}
