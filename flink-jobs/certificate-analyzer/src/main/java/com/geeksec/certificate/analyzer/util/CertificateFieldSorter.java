package com.geeksec.certificate.analyzer.util;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 证书字段排序工具类
 * 专门负责证书字段的排序和OID比较
 *
 * 排序规则：
 * 1. 首先检查字段是否在标准名称列表中，按预定义顺序排序
 * 2. 标准名称优先于OID和未知字符串
 * 3. 对于OID，按数字部分进行比较
 * 4. 对于未知字符串，按字典序比较
 *
 * <AUTHOR>
 * <AUTHOR> (重构优化)
 */
public final class CertificateFieldSorter {

    /**
     * 证书字段名称的标准排序顺序
     */
    private static final List<String> NAME_LIST_ORDER = Arrays.asList(
            CertificateConstants.FIELD_CN, CertificateConstants.FIELD_C, CertificateConstants.FIELD_L, CertificateConstants.FIELD_ST, "STREET_ADDRESS", CertificateConstants.FIELD_O, CertificateConstants.FIELD_OU, "SERIAL_NUMBER",
            "SURNAME", "GIVEN_NAME", "TITLE", "GENERATION_QUALIFIER", "X500_UNIQUE_IDENTIFIER", "DN_QUALIFIER", "PSEUDONYM", "USER_ID",
            "DOMAIN_COMPONENT", "EMAIL_ADDRESS", "JURISDICTION_COUNTRY_NAME", "JURISDICTION_LOCALITY_NAME", "JURISDICTION_STATE_OR_PROVINCE_NAME",
            "BUSINESS_CATEGORY", "POSTAL_ADDRESS", "POSTAL_CODE", "INN", "OGRN", "SNILS", "UNSTRUCTURED_NAME"
    );

    /**
     * 私有构造函数，防止实例化
     */
    private CertificateFieldSorter() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }

    /**
     * 按照自定义顺序对Map进行排序并转换为字符串
     *
     * @param map 需要排序的Map
     * @return 排序后的字符串表示
     */
    public static String sortMapByKey(Map<String, String> map) {
        Comparator<String> customComparator = (o1, o2) -> {
            int index1 = NAME_LIST_ORDER.indexOf(o1);
            int index2 = NAME_LIST_ORDER.indexOf(o2);

            if (index1 != -1 && index2 != -1) {
                return Integer.compare(index1, index2);
            } else if (index1 != -1) {
                return -1;
            } else if (index2 != -1) {
                return 1;
            } else {
                // Both are OIDs or unknown, handle them
                return compareOidsOrUnknown(o1, o2);
            }
        };

        return map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(customComparator))
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining(", "));
    }

    /**
     * 比较两个OID或未知字符串
     *
     * @param oid1 第一个OID或字符串
     * @param oid2 第二个OID或字符串
     * @return 比较结果
     */
    private static int compareOidsOrUnknown(String oid1, String oid2) {
        String[] parts1 = oid1.split("\\.");
        String[] parts2 = oid2.split("\\.");

        // Compare the number of parts
        if (parts1.length != parts2.length) {
            return Integer.compare(parts1.length, parts2.length);
        }

        // Compare each part
        for (int i = 0; i < parts1.length; i++) {
            // If the part is numeric, compare as integers, otherwise compare as strings
            try {
                int partCompare = Integer.compare(Integer.parseInt(parts1[i]), Integer.parseInt(parts2[i]));
                if (partCompare != 0) {
                    return partCompare;
                }
            } catch (NumberFormatException e) {
                return parts1[i].compareTo(parts2[i]);
            }
        }

        // OIDs are equal
        return 0;
    }

    /**
     * 计算排序后字符串的MD5哈希值
     *
     * @param map 需要排序的Map
     * @return 排序后字符串的MD5哈希值
     */
    public static String getMd5ForSortedMap(Map<String, String> map) {
        String sortedString = sortMapByKey(map);
        return DigestUtils.md5Hex(sortedString);
    }
}