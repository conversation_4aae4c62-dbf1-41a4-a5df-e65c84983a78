package com.geeksec.session.threat.detector.model.detection;

import com.geeksec.session.threat.detector.detection.DetectorType;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 威胁检测结果统一数据模型
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DetectionResult implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础检测信息 ==========

    /**
     * 检测器类型
     */
    private DetectorType detectorType;

    /**
     * 威胁类型
     */
    private String threatType;

    /**
     * 威胁名称
     */
    private String threatName;

    /**
     * 威胁级别
     */
    private ThreatLevel threatLevel;

    /**
     * 检测置信度 (0.0 - 1.0)
     */
    private Double confidence;

    /**
     * 检测时间
     */
    private LocalDateTime detectionTime;

    // ========== 事件相关信息 ==========

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 源IP地址
     */
    private String srcIp;

    /**
     * 目标IP地址
     */
    private String dstIp;

    /**
     * 源端口
     */
    private Integer srcPort;

    /**
     * 目标端口
     */
    private Integer dstPort;

    /**
     * 协议类型
     */
    private String protocol;

    // ========== 检测详情 ==========

    /**
     * 检测描述
     */
    private String description;

    /**
     * 检测规则ID
     */
    private String ruleId;

    /**
     * 匹配的特征
     */
    private String matchedSignature;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    // ========== 标签信息 ==========

    /**
     * 会话标签
     */
    private String sessionLabel;

    /**
     * 资产标签
     */
    private String assetLabel;

    /**
     * 标签值
     */
    private String labelValue;

    /**
     * 威胁级别枚举
     */
    public enum ThreatLevel {
        LOW("低", 1),
        MEDIUM("中", 2),
        HIGH("高", 3),
        CRITICAL("严重", 4);

        private final String displayName;
        private final int level;

        ThreatLevel(String displayName, int level) {
            this.displayName = displayName;
            this.level = level;
        }

        public String getDisplayName() {
            return displayName;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 获取检测器显示名称
     *
     * @return 检测器显示名称，如果detectorType为null则返回null
     */
    public String getDetectorDisplayName() {
        return detectorType != null ? detectorType.getDisplayName() : null;
    }

    public static DetectionResultBuilder builder() {
        return new DetectionResultBuilder();
    }
}
