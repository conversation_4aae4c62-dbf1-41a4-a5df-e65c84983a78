{{- if and .Values.infrastructure.kafka.enabled .Values.infrastructure.kafka.operator.enabled -}}
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: {{ .Values.infrastructure.kafka.credentials.username }}
  namespace: {{ .Values.global.namespace }}
  labels:
    strimzi.io/cluster: {{ .Values.infrastructure.kafka.operator.kafka.name }}
    {{- include "nta.labels" . | nindent 4 }}
spec:
  authentication:
    type: scram-sha-512
    password:
      valueFrom:
        secretKeyRef:
          name: kafka-user-initial-password
          key: password
  authorization:
    type: simple
    acls:
      # 允许在特定主题上进行读写操作
      {{- range $topic := list "cert-topic" "traffic-topic" "json_meta" "meta" "alarmOutput" "certfile" "model_state_changed" }}
      - resource:
          type: topic
          name: {{ $topic }}
          patternType: literal
        operation: Read
      - resource:
          type: topic
          name: {{ $topic }}
          patternType: literal
        operation: Write
      {{- end }}
      # 允许在特定消费者组上进行操作
      {{- range $group := list "meta-nebula-01" "meta-model" "meta_ES_LMDB" "certfile-01" "session-threat-detector" "cert-analyzer" }}
      - resource:
          type: group
          name: {{ $group }}
          patternType: literal
        operation: Read
      {{- end }}
      # 允许集群操作（仅限必要的操作）
      - resource:
          type: cluster
          name: "kafka-cluster"
          patternType: literal
        operation: Create
      # 允许事务操作（仅限应用程序需要的事务ID）
      - resource:
          type: transactionalId
          name: "nta-tx-"
          patternType: prefix
        operation: Write
{{- end -}}
