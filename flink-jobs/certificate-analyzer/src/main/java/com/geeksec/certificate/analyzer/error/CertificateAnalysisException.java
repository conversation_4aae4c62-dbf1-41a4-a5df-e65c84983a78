package com.geeksec.certificate.analyzer.error;

/**
 * 证书分析异常基类
 * 与threat-detector的异常处理保持一致
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class CertificateAnalysisException extends Exception {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public CertificateAnalysisException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public CertificateAnalysisException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 异常原因
     */
    public CertificateAnalysisException(Throwable cause) {
        super(cause);
    }
}
