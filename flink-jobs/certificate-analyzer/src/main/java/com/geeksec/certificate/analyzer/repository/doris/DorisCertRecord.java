package com.geeksec.certificate.analyzer.repository.doris;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 代表从 Doris dim_cert 表查询到的证书数据的不可变记录。
 * 与dim_cert表结构保持一致
 *
 * <AUTHOR>
 */
public record DorisCertRecord(
    // 主键和基础哈希字段
    String derSha1,
    String derMd5,
    String derSha256,
    String pemMd5,
    String pemSha256,
    String pemSha1,
    
    // 证书基本信息
    String version,
    String serialNumber,
    String format,
    
    // 关联ID
    String issuerId,
    String subjectId,
    
    // 时间相关
    LocalDateTime notBefore,
    LocalDateTime notAfter,
    Long duration,
    LocalDateTime importTime,
    
    // 名称和标识
    String commonName,
    List<String> subjectAltNames,
    List<String> issuerAltNames,
    
    // 公钥信息
    String publicKey,
    String publicKeyAlgorithm,
    String publicKeyLength,
    String publicKeyParameter,
    String spkiSha256,
    String publicKeyAlgOid,
    String publicKeyParamOid,
    
    // 签名信息
    String signatureAlgorithm,
    String signatureAlgName,
    String signatureAlgOid,
    
    // 密钥用途和扩展
    String keyUsage,
    List<String> extendedKeyUsage,
    String basicConstraints,
    String authorityKeyIdentifier,
    String subjectKeyIdentifier,
    List<String> crlDistributionPoints,
    List<String> authorityInfoAccess,
    List<String> subjectInfoAccess,
    List<String> certPolicies,
    
    // 扩展信息
    Map<String, Object> extensions,
    
    // 证书链信息
    String parentCertId,
    List<String> certChainIds,
    
    // 业务分类字段
    Integer source,
    String userType,
    String businessType,
    String caType,
    String industryType,
    String subjectArea,
    String issuerArea,
    
    // 状态和标记
    Boolean isWhitelisted,
    Boolean isBlacklisted,
    Boolean isParsedSuccessfully,
    Boolean isCorrupted,
    Integer certOccurrenceCount,
    String processingMethod,
    String parseStatus,
    
    // 任务和批次信息
    String taskId,
    String batchId,
    List<String> userIdList,
    
    // 评分字段
    Integer threatScore,
    Integer trustScore,
    String threatLevel,
    
    // 标签和关联信息
    List<Integer> labels,
    List<String> forwardChunkHashes,
    List<String> reverseChunkHashes,
    String correctedAsn1Sha1,
    
    // 证书内嵌信息
    List<String> certDomains,
    List<String> certIps,
    
    // 特殊字段
    List<String> uncommonOids,
    String organization,
    
    // 时间字段
    LocalDateTime firstSeen,
    LocalDateTime lastSeen,
    LocalDateTime createTime,
    LocalDateTime updateTime,
    String remark
) {}
