package com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class ConnectMacEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.MAC_CONNECTS_TO_MAC_TAG;
    }

    /**
     * 链路层连接发起方 (源MAC -> 目的MAC)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取源MAC和目的MAC
        String sMac = value.getField(FieldConstants.FIELD_SMAC).toString();
        String dMac = value.getField(FieldConstants.FIELD_DMAC).toString();
        return List.of(
                Row.of(sMac,
                        dMac,
                        0 // rank暂定0
                ));
    }
}
