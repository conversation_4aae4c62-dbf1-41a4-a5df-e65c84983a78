package com.geeksec.alarm.processor.pipeline.function;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 攻击链分析功能
 * 分析告警间的关联关系，识别攻击链
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AttackChainAnalysisFunction extends KeyedProcessFunction<String, Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    /** 攻击链状态 */
    private transient ValueState<AttackChainState> attackChainState;
    
    /** 攻击链缓存 */
    private transient Cache<String, AttackChainRecord> attackChainCache;
    
    /** 指标计数器 */
    private transient Counter totalAlarms;
    private transient Counter chainedAlarms;
    private transient Counter isolatedAlarms;
    private transient Counter newChains;
    
    public AttackChainAnalysisFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        ValueStateDescriptor<AttackChainState> stateDescriptor = new ValueStateDescriptor<>(
                "attack-chain-state",
                TypeInformation.of(AttackChainState.class)
        );
        attackChainState = getRuntimeContext().getState(stateDescriptor);
        
        // 初始化缓存
        initializeCache();
        
        // 初始化指标
        initializeMetrics();
        
        log.info("攻击链分析功能初始化完成，关联窗口: {}ms, 最小事件数: {}", 
                config.getAttackChainCorrelationWindowMs(), 
                config.getMinEventsForChain());
    }
    
    /**
     * 初始化缓存
     */
    private void initializeCache() {
        this.attackChainCache = Caffeine.newBuilder()
                .maximumSize(config.getAttackChainMaxCacheSize())
                .expireAfterWrite(config.getAttackChainCorrelationWindowMs() * 2, TimeUnit.MILLISECONDS)
                .recordStats()
                .build();
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("attack-chain-analysis");
        
        totalAlarms = metricGroup.counter("total_alarms");
        chainedAlarms = metricGroup.counter("chained_alarms");
        isolatedAlarms = metricGroup.counter("isolated_alarms");
        newChains = metricGroup.counter("new_chains");
        
        // 缓存指标
        metricGroup.gauge("cache_size", (Gauge<Long>) () -> attackChainCache.estimatedSize());
        metricGroup.gauge("cache_hit_rate", (Gauge<Double>) () -> attackChainCache.stats().hitRate());
    }
    
    @Override
    public void processElement(Alarm alarm, Context ctx, Collector<Alarm> out) throws Exception {
        totalAlarms.inc();
        
        try {
            String attackChainKey = alarm.getAttackChainKey();
            LocalDateTime currentTime = alarm.getEventTimestamp();
            
            // 获取当前攻击链状态
            AttackChainState currentState = attackChainState.value();
            if (currentState == null) {
                currentState = new AttackChainState();
            }
            
            // 检查是否可以关联到现有攻击链
            AttackChainRecord existingChain = findExistingChain(attackChainKey, currentTime);
            
            if (existingChain != null) {
                // 关联到现有攻击链
                chainedAlarms.inc();
                addToExistingChain(alarm, existingChain, currentState);
                log.debug("告警关联到现有攻击链: {} -> {}", alarm.getAlarmId(), existingChain.getChainId());
            } else {
                // 创建新的攻击链或作为孤立告警
                if (shouldCreateNewChain(alarm, currentState)) {
                    newChains.inc();
                    createNewChain(alarm, currentState);
                    log.debug("创建新攻击链: {}", alarm.getAlarmId());
                } else {
                    isolatedAlarms.inc();
                    handleIsolatedAlarm(alarm);
                    log.debug("处理孤立告警: {}", alarm.getAlarmId());
                }
            }
            
            // 更新状态
            attackChainState.update(currentState);
            
            // 设置清理定时器
            long cleanupTime = ctx.timerService().currentProcessingTime() + config.getAttackChainCorrelationWindowMs();
            ctx.timerService().registerProcessingTimeTimer(cleanupTime);
            
            // 标记为已分析
            alarm.getProcessingStatus().setAttackChainAnalyzed(true);
            
            out.collect(alarm);
            
        } catch (Exception e) {
            log.error("攻击链分析失败: {}", e.getMessage(), e);
            
            // 添加错误信息
            if (alarm.getProcessingStatus().getProcessingErrors() == null) {
                alarm.getProcessingStatus().setProcessingErrors(new ArrayList<>());
            }
            alarm.getProcessingStatus().getProcessingErrors().add("攻击链分析失败: " + e.getMessage());
            
            out.collect(alarm);
        }
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Alarm> out) throws Exception {
        // 清理过期的攻击链记录
        cleanupExpiredChains();
    }
    
    /**
     * 查找现有攻击链
     */
    private AttackChainRecord findExistingChain(String attackChainKey, LocalDateTime currentTime) {
        AttackChainRecord record = attackChainCache.getIfPresent(attackChainKey);
        
        if (record != null) {
            // 检查时间窗口
            Duration duration = Duration.between(record.getLastEventTime(), currentTime);
            if (duration.toMillis() <= config.getAttackChainCorrelationWindowMs()) {
                return record;
            } else {
                // 过期，从缓存中移除
                attackChainCache.invalidate(attackChainKey);
            }
        }
        
        return null;
    }
    
    /**
     * 添加到现有攻击链
     */
    private void addToExistingChain(Alarm alarm, AttackChainRecord chainRecord, AttackChainState state) {
        // 更新攻击链记录
        chainRecord.getRelatedAlarms().add(alarm.getAlarmId());
        chainRecord.setLastEventTime(alarm.getEventTimestamp());
        chainRecord.setEventCount(chainRecord.getEventCount() + 1);
        
        // 添加攻击事件
        Alarm.AttackEvent attackEvent = Alarm.AttackEvent.builder()
                .timestamp(alarm.getEventTimestamp())
                .eventType(alarm.getAlarmType())
                .description(alarm.getDescription())
                .build();
        chainRecord.getAttackTimeline().add(attackEvent);
        
        // 计算关联分数
        double correlationScore = calculateCorrelationScore(chainRecord);
        chainRecord.setCorrelationScore(correlationScore);
        
        // 更新缓存
        attackChainCache.put(alarm.getAttackChainKey(), chainRecord);
        
        // 设置告警的攻击链信息
        Alarm.AttackChainInfo attackChainInfo = Alarm.AttackChainInfo.builder()
                .chainId(chainRecord.getChainId())
                .chainStage(determineChainStage(chainRecord, alarm))
                .relatedAlarms(new ArrayList<>(chainRecord.getRelatedAlarms()))
                .attackTimeline(new ArrayList<>(chainRecord.getAttackTimeline()))
                .correlationScore(correlationScore)
                .build();
        
        alarm.setAttackChainInfo(attackChainInfo);
    }
    
    /**
     * 判断是否应该创建新攻击链
     */
    private boolean shouldCreateNewChain(Alarm alarm, AttackChainState state) {
        // 高优先级告警更容易形成攻击链
        if (alarm.isHighPriority()) {
            return true;
        }
        
        // 检查是否有足够的相关事件
        return state.getRecentAlarms().size() >= config.getMinEventsForChain() - 1;
    }
    
    /**
     * 创建新攻击链
     */
    private void createNewChain(Alarm alarm, AttackChainState state) {
        String chainId = generateChainId();
        
        AttackChainRecord chainRecord = AttackChainRecord.builder()
                .chainId(chainId)
                .firstEventTime(alarm.getEventTimestamp())
                .lastEventTime(alarm.getEventTimestamp())
                .eventCount(1)
                .relatedAlarms(new ArrayList<>())
                .attackTimeline(new ArrayList<>())
                .correlationScore(0.8) // 新链的初始分数
                .build();
        
        chainRecord.getRelatedAlarms().add(alarm.getAlarmId());
        
        Alarm.AttackEvent attackEvent = Alarm.AttackEvent.builder()
                .timestamp(alarm.getEventTimestamp())
                .eventType(alarm.getAlarmType())
                .description(alarm.getDescription())
                .build();
        chainRecord.getAttackTimeline().add(attackEvent);
        
        // 添加到缓存
        attackChainCache.put(alarm.getAttackChainKey(), chainRecord);
        
        // 设置告警的攻击链信息
        Alarm.AttackChainInfo attackChainInfo = Alarm.AttackChainInfo.builder()
                .chainId(chainId)
                .chainStage("初始阶段")
                .relatedAlarms(new ArrayList<>(chainRecord.getRelatedAlarms()))
                .attackTimeline(new ArrayList<>(chainRecord.getAttackTimeline()))
                .correlationScore(chainRecord.getCorrelationScore())
                .build();
        
        alarm.setAttackChainInfo(attackChainInfo);
    }
    
    /**
     * 处理孤立告警
     */
    private void handleIsolatedAlarm(Alarm alarm) {
        // 孤立告警不设置攻击链信息，但可以记录为潜在的攻击链起点
        log.debug("告警暂时作为孤立事件处理: {}", alarm.getAlarmId());
    }
    
    /**
     * 生成攻击链ID
     */
    private String generateChainId() {
        return "chain_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 计算关联分数
     */
    private double calculateCorrelationScore(AttackChainRecord chainRecord) {
        // 基于事件数量、时间间隔、告警类型等因素计算分数
        double baseScore = 0.5;
        
        // 事件数量因子
        double eventFactor = Math.min(chainRecord.getEventCount() * 0.1, 0.3);
        
        // 时间连续性因子
        double timeFactor = 0.2; // 简化计算
        
        return Math.min(baseScore + eventFactor + timeFactor, 1.0);
    }
    
    /**
     * 确定攻击链阶段
     */
    private String determineChainStage(AttackChainRecord chainRecord, Alarm alarm) {
        int eventCount = chainRecord.getEventCount();
        
        if (eventCount <= 2) {
            return "初始阶段";
        } else if (eventCount <= 5) {
            return "发展阶段";
        } else {
            return "活跃阶段";
        }
    }
    
    /**
     * 清理过期的攻击链
     */
    private void cleanupExpiredChains() {
        attackChainCache.cleanUp();
        log.debug("清理过期攻击链记录完成");
    }
    
    @Override
    public void close() throws Exception {
        if (attackChainCache != null) {
            log.info("攻击链分析功能关闭，分析统计: 总计={}, 关联={}, 孤立={}, 新链={}", 
                    totalAlarms.getCount(), 
                    chainedAlarms.getCount(), 
                    isolatedAlarms.getCount(),
                    newChains.getCount());
            
            attackChainCache.invalidateAll();
        }
        super.close();
    }

    /**
     * 攻击链状态
     */
    @lombok.Data
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class AttackChainState {
        private List<String> recentAlarms = new ArrayList<>();
        private LocalDateTime lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 攻击链记录
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class AttackChainRecord {
        private String chainId;
        private LocalDateTime firstEventTime;
        private LocalDateTime lastEventTime;
        private int eventCount;
        private List<String> relatedAlarms;
        private List<Alarm.AttackEvent> attackTimeline;
        private double correlationScore;
    }
}
