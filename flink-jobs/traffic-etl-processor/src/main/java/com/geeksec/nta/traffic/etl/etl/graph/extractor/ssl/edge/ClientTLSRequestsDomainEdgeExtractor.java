package com.geeksec.nta.traffic.etl.etl.graph.extractor.ssl.edge;

import com.geeksec.common.network.DomainUtils;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class ClientTLSRequestsDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CLIENT_TLS_REQUESTS_DOMAIN_TAG;
    }

    /**
     * TLS客户端访问域名 (源IP -> SNI DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();

        if (DomainUtils.isValidDomain(sni) && !sni.equals(dIP)){
            sni = DomainUtils.formatDomain(sni);
            return List.of(Row.of(sIP, sni,
                    0 // rank暂定0
            ));
        }

        return Collections.emptyList();
    }
}
