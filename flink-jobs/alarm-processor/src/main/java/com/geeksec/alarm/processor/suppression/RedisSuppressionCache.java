package com.geeksec.alarm.processor.suppression;

import com.geeksec.alarm.processor.cache.RedisAlarmCacheManager;
import com.geeksec.alarm.processor.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;
import java.io.Serializable;

/**
 * Redis抑制规则缓存
 * 使用统一的RedisAlarmCacheManager进行Redis连接管理
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class RedisSuppressionCache implements Serializable, Closeable {

    private static final long serialVersionUID = 1L;

    private final RedisConfig redisConfig;
    private transient RedisAlarmCacheManager cacheManager;

    public RedisSuppressionCache(RedisConfig redisConfig) {
        this.redisConfig = redisConfig;
        initializeCacheManager();
    }

    /**
     * 初始化缓存管理器
     */
    private void initializeCacheManager() {
        if (cacheManager == null) {
            cacheManager = new RedisAlarmCacheManager(redisConfig);
            log.info("Redis抑制规则缓存初始化成功");
        }
    }
    
    /**
     * 添加抑制规则到Redis
     */
    public void addSuppressionRule(String victim, String attacker, String label) {
        if (cacheManager == null) {
            initializeCacheManager();
        }

        boolean success = cacheManager.addSuppressionRule(victim, attacker, label, redisConfig.getCacheExpireSeconds());
        if (success) {
            log.debug("添加抑制规则到Redis: {}|{}|{}", victim, attacker, label);
        } else {
            log.warn("添加抑制规则到Redis失败: {}|{}|{}", victim, attacker, label);
        }
    }

    /**
     * 从Redis移除抑制规则
     */
    public void removeSuppressionRule(String victim, String attacker, String label) {
        if (cacheManager == null) {
            initializeCacheManager();
        }

        boolean success = cacheManager.removeSuppressionRule(victim, attacker, label);
        if (success) {
            log.debug("从Redis移除抑制规则: {}|{}|{}", victim, attacker, label);
        } else {
            log.debug("抑制规则不存在，无需移除: {}|{}|{}", victim, attacker, label);
        }
    }

    /**
     * 检查抑制规则是否存在
     */
    public boolean shouldSuppress(String victim, String attacker, String label) {
        if (cacheManager == null) {
            initializeCacheManager();
        }

        return cacheManager.shouldSuppressAlarm(victim, attacker, label);
    }
    
    /**
     * 批量加载抑制规则到Redis
     */
    public void batchLoadSuppressionRules(java.util.List<java.util.Map<String, Object>> rules) {
        if (rules == null || rules.isEmpty()) {
            return;
        }

        if (cacheManager == null) {
            initializeCacheManager();
        }

        try {
            int successCount = 0;
            for (java.util.Map<String, Object> rule : rules) {
                String victim = (String) rule.get("victim");
                String attacker = (String) rule.get("attacker");
                String label = (String) rule.get("label");

                if (victim != null && attacker != null && label != null) {
                    boolean success = cacheManager.addSuppressionRule(victim, attacker, label, redisConfig.getCacheExpireSeconds());
                    if (success) {
                        successCount++;
                    }
                }
            }
            log.info("批量加载 {}/{} 条抑制规则到Redis", successCount, rules.size());
        } catch (Exception e) {
            log.error("批量加载抑制规则到Redis失败", e);
        }
    }

    /**
     * 清空所有抑制规则
     * 注意：这是一个简化实现，实际生产环境中建议使用更精确的清理方式
     */
    public void clearAllSuppressionRules() {
        log.warn("clearAllSuppressionRules方法需要根据具体需求实现，当前为空实现");
        // 由于RedisAlarmCacheManager没有提供批量清理功能，这里暂时留空
        // 实际使用中可以根据需要扩展RedisAlarmCacheManager的功能
    }

    /**
     * 获取缓存中的抑制规则数量
     * 注意：这是一个简化实现，实际生产环境中建议使用更精确的统计方式
     */
    public long getCacheSize() {
        log.warn("getCacheSize方法需要根据具体需求实现，当前返回0");
        // 由于RedisAlarmCacheManager没有提供统计功能，这里暂时返回0
        // 实际使用中可以根据需要扩展RedisAlarmCacheManager的功能
        return 0;
    }

    @Override
    public void close() {
        try {
            if (cacheManager != null) {
                cacheManager.close();
                log.info("Redis抑制规则缓存已关闭");
            }
        } catch (Exception e) {
            log.error("关闭Redis抑制规则缓存失败", e);
        }
    }
}
