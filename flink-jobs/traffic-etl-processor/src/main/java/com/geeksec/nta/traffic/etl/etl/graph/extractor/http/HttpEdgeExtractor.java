package com.geeksec.nta.traffic.etl.etl.graph.extractor.http;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import lombok.extern.slf4j.Slf4j;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 会话边提取器
 * 从网络会话中提取边关系数据，包括IP绑定关系、连接关系和应用关系
 *
 * <AUTHOR> Team
 */
@Slf4j
public class HttpEdgeExtractor extends ProcessFunction<Row, Row> {
    private static final ObjectMapper mapper = new ObjectMapper();
    public static UserAgentAnalyzer analyzer = UserAgentAnalyzer
            .newBuilder()
            .withCache(10000) // 设置缓存大小
            .build();

    /**
     * 从会话信息中生成各类边关系
     *
     * @param value 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void processElement(Row value, Context ctx, Collector<Row> collector) throws Exception {
        try {
            HttpEdgeExtractorFactory.getEdgeExtractors().forEach(edgeExtractor -> {
                List<Row> edges = edgeExtractor.extractEdge(value);
                for (Row edge : edges){
                    if (edge != null){
                        ctx.output(edgeExtractor.getOutputTag(), edge);
                    }
                }
            });
            collector.collect(value);
        } catch (Exception e) {
            log.error("DNSEdgeExtractor process error: {}", e.getMessage());
        }
    }

    /**
     * 获取UA信息
     * @param httpClient
     * @return
     */
    public static String extractUAKey(Map<String, Object> httpClient) {
        // 获取UA信息
        String userAgent = httpClient.get("User-Agent").toString();
        if (StringUtil.isNullOrEmpty(userAgent)){
            return null;
        }

        // 解析UA信息
        UserAgent agent = analyzer.parse(userAgent);
        String osName = agent.getValue("OperatingSystemName"); // 操作系统
        String deviceName = agent.getValue("DeviceName"); // 设备名称
        String applicationName = agent.getValue("AgentName"); // 浏览器/应用名称
        String userAgentKey = applicationName + "_" + osName + "_" + deviceName;
        return userAgentKey;
    }

    /**
     * 获取UA解析对象
     * @param httpClient
     * @return
     */
    public static UserAgent extractUA(Map<String, Object> httpClient) {
        // 获取UA信息
        String userAgent = httpClient.get("User-Agent").toString();
        if (StringUtil.isNullOrEmpty(userAgent)){
            return null;
        }

        // 解析UA信息
        return analyzer.parse(userAgent);
    }

}
