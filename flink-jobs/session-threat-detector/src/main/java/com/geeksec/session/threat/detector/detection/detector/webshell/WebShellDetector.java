package com.geeksec.session.threat.detector.detection.detector.webshell;

import com.geeksec.session.threat.detector.detection.DetectorType;
import com.geeksec.session.threat.detector.detection.ThreatDetector;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.HttpInfo;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;

/**
 * WebShell检测器
 * 检测各种WebShell工具的HTTP通信特征
 *
 * <AUTHOR>
 */
@Slf4j
public class WebShellDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // WebShell检测常量
    private static final String BEHINDER_RESPONSE_START = "->|";
    private static final String BEHINDER_RESPONSE_END = "|<-";
    private static final String WEEVELY_SIGNATURE = "0f1b6a831c3";
    private static final int WEEVELY_MIN_LENGTH = 28;
    private static final int WEEVELY_START_POS = 17;
    private static final int WEEVELY_END_POS = 28;
    private static final String WEBSHELL_FILE_PATTERN = ".*\\.(php|asp|aspx|jsp|jspx)\\?.*";

    // WebShell工具特征库 - 基于负载的特征
    private static final Map<String, WebShellSignature> WEBSHELL_PAYLOAD_SIGNATURES = new HashMap<>();

    // WebShell工具特征库 - 基于Cookie的特征
    private static final Map<String, WebShellSignature> WEBSHELL_COOKIE_SIGNATURES = new HashMap<>();

    // WebShell工具特征库 - 基于请求头的特征
    private static final Map<String, WebShellSignature> WEBSHELL_HEADER_SIGNATURES = new HashMap<>();

    // WebShell工具特征库 - 基于响应的特征
    private static final Map<String, WebShellSignature> WEBSHELL_RESPONSE_SIGNATURES = new HashMap<>();

    // 可疑的WebShell特征模式
    private static final String[] SUSPICIOUS_PATTERNS = {
            "cmd=", "system(", "exec(", "passthru(", "shell_exec(",
            "eval(", "base64_", "assert(", "preg_replace(", "create_function(",
            "call_user_func(", "call_user_func_array(", "array_map(", "ob_start(",
            "phpinfo", "wscript.shell", "shell.application", "posix_getpwuid",
            "posix_geteuid", "DIRECTORY_SEPARATOR", "ob_gzip", "WebRoot",
            "APPL_PHYSICAL_PATH", "isset", "BaSE64%5FdEcOdE", "stripcslashes",
            "filesize", "HexAsciiConvert"
    };

    // 已知WebShell工具的User-Agent特征
    private static final Set<String> WEBSHELL_USER_AGENTS = new HashSet<>();

    static {
        initializeWebShellSignatures();
        initializeUserAgents();
    }

    private static void initializeWebShellSignatures() {
        initializePayloadSignatures();
        initializeCookieSignatures();
        initializeHeaderSignatures();
        initializeResponseSignatures();

        log.info("初始化WebShell特征库完成 - 负载特征: {}, Cookie特征: {}, 请求头特征: {}, 响应特征: {}",
                WEBSHELL_PAYLOAD_SIGNATURES.size(),
                WEBSHELL_COOKIE_SIGNATURES.size(),
                WEBSHELL_HEADER_SIGNATURES.size(),
                WEBSHELL_RESPONSE_SIGNATURES.size());
    }

    /**
     * 初始化基于负载的WebShell特征
     */
    private static void initializePayloadSignatures() {
        // 中国菜刀
        WEBSHELL_PAYLOAD_SIGNATURES.put("中国菜刀", WebShellSignature.createPayloadSignature(
                "中国菜刀",
                List.of("cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B", "posix_getpwuid", "posix_geteuid", "system"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段内容中包含cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B特征字段，第二段内容包含posix_getpwuid、posix_geteuid、system特定内容。"
        ));

        // 蚁剑
        WEBSHELL_PAYLOAD_SIGNATURES.put("蚁剑", WebShellSignature.createPayloadSignature(
                "蚁剑",
                List.of("function%20HexAsciiConvert(hex%3AString)", "HexAsciiConvert"),
                "hex",
                "请求包中的请求体负载内容包含function%20HexAsciiConvert(hex%3AString)特定内容。"
        ));

        // 蚁剑PHP版本
        WEBSHELL_PAYLOAD_SIGNATURES.put("蚁剑PHP", WebShellSignature.createPayloadSignature(
                "蚁剑PHP",
                List.of("function HexAsciiConvert", "hex2bin", "eval"),
                "hex",
                "请求包中的请求体负载内容包含function HexAsciiConvert特定内容，针对PHP版本的蚁剑。"
        ));

        // Altman
        WEBSHELL_PAYLOAD_SIGNATURES.put("Altman", WebShellSignature.createPayloadSignature(
                "Altman",
                List.of("@ini_set", "display_errors", "$r.", "DIRECTORY_SEPARATOR"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段参数包含@ini_set、display_errors特征字段，第二段内容包含$r.、DIRECTORY_SEPARATOR特定内容。"
        ));

        // Webshell-Sniper
        WEBSHELL_PAYLOAD_SIGNATURES.put("Webshell-Sniper", WebShellSignature.createPayloadSignature(
                "Webshell-Sniper",
                List.of("@ob_start", "ob_gzip"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段内容包含@ob_start、ob_gzip特征字段。"
        ));

        // WebshellManager
        WEBSHELL_PAYLOAD_SIGNATURES.put("WebshellManager", WebShellSignature.createPayloadSignature(
                "WebshellManager",
                List.of("eval", "\\001", "WebRoot"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段参数存在eval、\\001特征字段，第二段内容中包含WebRoot特定内容。"
        ));

        // w8ay (与WebshellManager相同)
        WEBSHELL_PAYLOAD_SIGNATURES.put("w8ay", WebShellSignature.createPayloadSignature(
                "w8ay",
                List.of("eval", "\\001", "WebRoot"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段参数存在eval、\\001特征字段，第二段内容中包含WebRoot特定内容。"
        ));

        // Xise
        WEBSHELL_PAYLOAD_SIGNATURES.put("Xise", WebShellSignature.createPayloadSignature(
                "Xise",
                List.of("xise=%40eval%2F%2A%15%99%D0%21%03%19s%20%0B%CB%A8%DD%E3%A3%C5%C4", "APPL_PHYSICAL_PATH", "isset", "BaSE64%5FdEcOdE"),
                "hex",
                "请求包中的请求体解码后根据&字符分割，第一段内容中包含xise=%40eval%2F%2A%15%99%D0%21%03%19s%20%0B%CB%A8%DD%E3%A3%C5%C4特征字段，第二段内容中包含APPL_PHYSICAL_PATH、isset特定内容，且第三段内容为BaSE64%5FdEcOdE固定内容。"
        ));

        // cknife
        WEBSHELL_PAYLOAD_SIGNATURES.put("cknife", WebShellSignature.createPayloadSignature(
                "cknife",
                List.of("@eval.(base64_decode)", "eval", "base64_decode"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段内容中包含@eval.(base64_decode)特征内容。"
        ));

        // WebKnife
        WEBSHELL_PAYLOAD_SIGNATURES.put("WebKnife", WebShellSignature.createPayloadSignature(
                "WebKnife",
                List.of("eval(base64_decode('ZWNobyBmaWxlTGlzd", "fileList"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段内容中包含eval(base64_decode('ZWNobyBmaWxlTGlzd特征内容，且第二段内容中包含fileList特征字段。"
        ));

        // K8飞刀
        WEBSHELL_PAYLOAD_SIGNATURES.put("K8飞刀", WebShellSignature.createPayloadSignature(
                "K8飞刀",
                List.of("eval", "base64", "posix_getpwuid", "posix_geteuid"),
                "base64",
                "请求包中的请求url中携带特殊请求参数t，内容为时间戳，且请求体解码后根据&字符分割，第一段内容中存在eval、base64特征字段，第二段内容中包含posix_getpwuid、posix_geteuid特定内容。"
        ));

        // 小李飞刀
        WEBSHELL_PAYLOAD_SIGNATURES.put("小李飞刀", WebShellSignature.createPayloadSignature(
                "小李飞刀",
                List.of("echo"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段内容中以echo固定内容开头。"
        ));

        // 开山斧
        WEBSHELL_PAYLOAD_SIGNATURES.put("开山斧", WebShellSignature.createPayloadSignature(
                "开山斧",
                List.of("eval", "base64", "$usr", "substr($D,0,1)"),
                "base64",
                "请求包中的请求体解码后根据&字符分割，第一段内容中包含eval、base64特征字段，第二段内容中包含$usr、substr($D,0,1)特定内容。"
        ));
    }

    /**
     * 初始化基于Cookie的WebShell特征
     */
    private static void initializeCookieSignatures() {
        // WeBacoo
        WEBSHELL_COOKIE_SIGNATURES.put("WeBacoo", WebShellSignature.createCookieSignature(
                "WeBacoo",
                List.of("cm=", "cn=", "cp=", "aXBjb25maWc="),
                "请求包中请求头携带的cookie内容格式为cm=;cn=;cp=,其中cm=aXBjb25maWc=; 为Shell命令执行的Base64编码。"
        ));

        // b374k
        WEBSHELL_COOKIE_SIGNATURES.put("b374k", WebShellSignature.createCookieSignature(
                "b374k",
                List.of("s_self", "cwd", "pas"),
                "请求包中的请求头cookie内容中包含s_self、cwd、pas特定参数。"
        ));

        // 哥斯拉 - 基于Cookie结尾特征
        WEBSHELL_COOKIE_SIGNATURES.put("哥斯拉", WebShellSignature.createCookieSignature(
                "哥斯拉",
                List.of(";"),
                "请求包中的请求头cookie中以特殊字符;结尾，配合响应体特征进行检测。"
        ));
    }

    /**
     * 初始化基于请求头的WebShell特征
     */
    private static void initializeHeaderSignatures() {
        // Hatchet
        WEBSHELL_HEADER_SIGNATURES.put("Hatchet", WebShellSignature.createHeaderSignature(
                "Hatchet",
                List.of("Accept-Language: en-us", "Content-Type: application/x-www-form-urlencoded", "stripcslashes", "filesize"),
                "请求包中的请求体Accept-Language字段为en-us特定值、Content-Type字段为application/x-www-form-urlencoded特定值，且请求体解码后根据&字符分割，第一段内容中包含stripcslashes特征字段，第二段内容中包含filesize特定内容。"
        ));

        // 天蝎
        WEBSHELL_HEADER_SIGNATURES.put("天蝎", WebShellSignature.createHeaderSignature(
                "天蝎",
                List.of("Content-Type: application/octet-stream", "UUMRBkpMSQFBVFkbUVZGXAYEPQddW1oAUh0SaWt9TFsDegQAVW5CBgR/"),
                "请求包中的请求头中Content-Type字段为固定值application/octet-stream，且该会话中请求体负载以固定内容UUMRBkpMSQFBVFkbUVZGXAYEPQddW1oAUh0SaWt9TFsDegQAVW5CBgR/开头。"
        ));

        // SharPyShell
        WEBSHELL_HEADER_SIGNATURES.put("SharPyShell", WebShellSignature.createHeaderSignature(
                "SharPyShell",
                List.of("Content-Type: multipart/form-data; boundary=8d8ef8552fca8671052f3044faf663a0",
                       "Content-Disposition: form-data; name=\"data\"",
                       "Cache-Control: private"),
                "请求包中的请求头中Content-Type字段为固定内容multipart/form-data; boundary=8d8ef8552fca8671052f3044faf663a0、Content-Disposition字段为固定内容form-data; name=\"data\"、响应头中Cache-Control字段为固定内容private。"
        ));
    }

    /**
     * 初始化基于响应的WebShell特征
     */
    private static void initializeResponseSignatures() {
        // QuasiBot
        WEBSHELL_RESPONSE_SIGNATURES.put("QuasiBot", WebShellSignature.createResponseSignature(
                "QuasiBot",
                List.of("<!--{:|uid=1002(www) gid=1002(www) groups=1002(www)", "_", "___"),
                "请求包中的请求url中携带特殊请求参数为_以及___特殊字段，且响应体负载解码后包含<!--{:|uid=1002(www) gid=1002(www) groups=1002(www)特定内容。"
        ));

        // 哥斯拉 - 基于响应体结尾特征
        WEBSHELL_RESPONSE_SIGNATURES.put("哥斯拉", WebShellSignature.createResponseSignature(
                "哥斯拉",
                List.of("6C37", "b4c4e1f6ddd2a488"),
                "响应体以6C37或b4c4e1f6ddd2a488固定内容结尾，配合Cookie特征进行检测。"
        ));

        // jspmaster
        WEBSHELL_RESPONSE_SIGNATURES.put("jspmaster", WebShellSignature.createResponseSignature(
                "jspmaster",
                List.of("\n\n\n\n\n\n\n\n\n"),
                "请求包中的响应体中由特殊字符9个换行符组成。"
        ));

        // Weevely
        WEBSHELL_RESPONSE_SIGNATURES.put("Weevely", WebShellSignature.createResponseSignature(
                "Weevely",
                List.of("0f1b6a831c3", "99e269772661"),
                "请求包中的请求体负载中第17-28位字符与响应体负载中第17-28位字符为相同特征内容0f1b6a831c3，请求体负载中倒数第17-28位字符与响应体负载中最后12位字符为相同特征内容99e269772661。"
        ));

        // 冰蝎 - 基于响应体特征
        WEBSHELL_RESPONSE_SIGNATURES.put("冰蝎", WebShellSignature.createResponseSignature(
                "冰蝎",
                List.of("->|", "|<-"),
                "响应体包含->|和|<-特殊标识符，用于标识冰蝎WebShell的响应。"
        ));
    }

    private static void initializeUserAgents() {
        WEBSHELL_USER_AGENTS.add("webshell");
        WEBSHELL_USER_AGENTS.add("hack");
        WEBSHELL_USER_AGENTS.add("exploit");
        WEBSHELL_USER_AGENTS.add("chopper");
        WEBSHELL_USER_AGENTS.add("knife");
        WEBSHELL_USER_AGENTS.add("altman");
        
        log.info("初始化WebShell User-Agent特征库，共 {} 个特征", WEBSHELL_USER_AGENTS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.WEBSHELL;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 只处理HTTP事件
            if (event.getEventType() != NetworkEvent.EventType.HTTP || event.getHttpInfo() == null) {
                return results;
            }

            HttpInfo httpInfo = event.getHttpInfo();

            // 1. 检测基于负载的WebShell特征
            DetectionResult payloadResult = detectPayloadWebShell(event, httpInfo);
            if (payloadResult != null) {
                results.add(payloadResult);
            }

            // 2. 检测基于URL的WebShell特征
            DetectionResult urlResult = detectUrlWebShell(event, httpInfo);
            if (urlResult != null) {
                results.add(urlResult);
            }

            // 3. 检测基于User-Agent的WebShell特征
            DetectionResult userAgentResult = detectUserAgentWebShell(event, httpInfo);
            if (userAgentResult != null) {
                results.add(userAgentResult);
            }

            // 4. 检测基于Cookie的WebShell特征
            DetectionResult cookieResult = detectCookieWebShell(event, httpInfo);
            if (cookieResult != null) {
                results.add(cookieResult);
            }

            // 5. 检测基于请求头的WebShell特征
            DetectionResult headerResult = detectHeaderWebShell(event, httpInfo);
            if (headerResult != null) {
                results.add(headerResult);
            }

            // 6. 检测基于响应体的WebShell特征
            DetectionResult responseResult = detectResponseWebShell(event, httpInfo);
            if (responseResult != null) {
                results.add(responseResult);
            }

        } catch (Exception e) {
            log.error("WebShell检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测基于负载的WebShell特征
     */
    private DetectionResult detectPayloadWebShell(NetworkEvent event, HttpInfo httpInfo) {
        String requestBody = httpInfo.getRequestBody();
        if (requestBody == null || requestBody.isEmpty()) {
            return null;
        }

        // 检查已知WebShell工具特征
        for (Map.Entry<String, WebShellSignature> entry : WEBSHELL_PAYLOAD_SIGNATURES.entrySet()) {
            WebShellSignature signature = entry.getValue();
            if (signature.matches(requestBody)) {
                String matchedPattern = signature.getMatchedPattern(requestBody);
                return createDetectionResult(event, "WEBSHELL_" + signature.getToolName().toUpperCase(),
                        signature.getToolName() + " WebShell工具",
                        DetectionResult.ThreatLevel.HIGH, signature.getConfidence(),
                        signature.getDescription() + " 匹配特征: " + matchedPattern);
            }
        }

        // 检查可疑模式
        for (String pattern : SUSPICIOUS_PATTERNS) {
            if (requestBody.toLowerCase().contains(pattern)) {
                return createDetectionResult(event, "WEBSHELL_SUSPICIOUS_PAYLOAD",
                        "可疑WebShell负载",
                        DetectionResult.ThreatLevel.MEDIUM, 0.7,
                        "可疑模式: " + pattern);
            }
        }

        return null;
    }

    /**
     * 检测基于URL的WebShell特征
     */
    private DetectionResult detectUrlWebShell(NetworkEvent event, HttpInfo httpInfo) {
        String uri = httpInfo.getUri();
        if (uri == null) {
            return null;
        }

        String lowerUri = uri.toLowerCase();
        String query = uri.contains("?") ? uri.substring(uri.indexOf('?') + 1) : "";

        // 检查URI中的可疑模式
        for (String pattern : SUSPICIOUS_PATTERNS) {
            if (lowerUri.contains(pattern) || query.contains(pattern)) {
                return createDetectionResult(event, "WEBSHELL_SUSPICIOUS_URL", 
                        "可疑WebShell URL",
                        DetectionResult.ThreatLevel.MEDIUM, 0.6,
                        "可疑URL模式: " + pattern);
            }
        }

        // 检查特定的WebShell文件扩展名和路径
        boolean isWebShellFile = lowerUri.matches(WEBSHELL_FILE_PATTERN);
        boolean hasSuspiciousParams = lowerUri.contains("cmd") || lowerUri.contains("shell") || lowerUri.contains("exec");

        if (isWebShellFile && hasSuspiciousParams) {
            return createDetectionResult(event, "WEBSHELL_SUSPICIOUS_FILE",
                    "可疑WebShell文件访问",
                    DetectionResult.ThreatLevel.MEDIUM, 0.7,
                    "可疑文件路径: " + uri);
        }

        return null;
    }

    /**
     * 检测基于User-Agent的WebShell特征
     */
    private DetectionResult detectUserAgentWebShell(NetworkEvent event, HttpInfo httpInfo) {
        String userAgent = httpInfo.getUserAgent();
        if (userAgent == null) {
            return null;
        }

        String lowerUserAgent = userAgent.toLowerCase();

        for (String suspiciousUserAgent : WEBSHELL_USER_AGENTS) {
            if (lowerUserAgent.contains(suspiciousUserAgent)) {
                return createDetectionResult(event, "WEBSHELL_SUSPICIOUS_UA",
                        "可疑WebShell User-Agent",
                        DetectionResult.ThreatLevel.MEDIUM, 0.6,
                        "可疑User-Agent: " + userAgent);
            }
        }

        return null;
    }

    /**
     * 检测基于Cookie的WebShell特征
     */
    private DetectionResult detectCookieWebShell(NetworkEvent event, HttpInfo httpInfo) {
        Map<String, String> requestHeaders = httpInfo.getRequestHeaders();
        if (requestHeaders == null) {
            return null;
        }

        String cookie = requestHeaders.get("Cookie");
        if (cookie == null || cookie.isEmpty()) {
            return null;
        }

        // 特殊处理哥斯拉检测 - 需要结合Cookie和响应体特征
        if (cookie.endsWith(";")) {
            String responseBody = httpInfo.getResponseBody();
            if (responseBody != null && (responseBody.endsWith("6C37") || responseBody.contains("b4c4e1f6ddd2a488"))) {
                return createDetectionResult(event, "WEBSHELL_GODZILLA",
                        "哥斯拉 WebShell工具",
                        DetectionResult.ThreatLevel.HIGH, 0.95,
                        "哥斯拉特征匹配: Cookie以;结尾且响应体包含特定结尾标识");
            }
        }

        // 检查已知WebShell工具Cookie特征
        for (Map.Entry<String, WebShellSignature> entry : WEBSHELL_COOKIE_SIGNATURES.entrySet()) {
            WebShellSignature signature = entry.getValue();
            if (signature.matches(cookie)) {
                String matchedPattern = signature.getMatchedPattern(cookie);
                return createDetectionResult(event, "WEBSHELL_" + signature.getToolName().toUpperCase(),
                        signature.getToolName() + " WebShell工具",
                        DetectionResult.ThreatLevel.HIGH, signature.getConfidence(),
                        signature.getDescription() + " 匹配特征: " + matchedPattern);
            }
        }

        return null;
    }

    /**
     * 检测基于请求头的WebShell特征
     */
    private DetectionResult detectHeaderWebShell(NetworkEvent event, HttpInfo httpInfo) {
        Map<String, String> requestHeaders = httpInfo.getRequestHeaders();
        Map<String, String> responseHeaders = httpInfo.getResponseHeaders();

        if (requestHeaders == null && responseHeaders == null) {
            return null;
        }

        // 构建请求头信息字符串用于匹配
        StringBuilder headerInfo = new StringBuilder();

        if (requestHeaders != null) {
            String contentType = requestHeaders.get("Content-Type");
            String acceptLanguage = requestHeaders.get("Accept-Language");
            String contentDisposition = requestHeaders.get("Content-Disposition");

            if (contentType != null) {
                headerInfo.append("Content-Type: ").append(contentType).append(" ");
            }
            if (acceptLanguage != null) {
                headerInfo.append("Accept-Language: ").append(acceptLanguage).append(" ");
            }
            if (contentDisposition != null) {
                headerInfo.append("Content-Disposition: ").append(contentDisposition).append(" ");
            }
        }

        if (responseHeaders != null) {
            String cacheControl = responseHeaders.get("Cache-Control");
            if (cacheControl != null) {
                headerInfo.append("Cache-Control: ").append(cacheControl).append(" ");
            }
        }

        String headerString = headerInfo.toString();
        if (headerString.isEmpty()) {
            return null;
        }

        // 检查已知WebShell工具请求头特征
        for (Map.Entry<String, WebShellSignature> entry : WEBSHELL_HEADER_SIGNATURES.entrySet()) {
            WebShellSignature signature = entry.getValue();
            if (signature.matches(headerString)) {
                String matchedPattern = signature.getMatchedPattern(headerString);
                return createDetectionResult(event, "WEBSHELL_" + signature.getToolName().toUpperCase(),
                        signature.getToolName() + " WebShell工具",
                        DetectionResult.ThreatLevel.HIGH, signature.getConfidence(),
                        signature.getDescription() + " 匹配特征: " + matchedPattern);
            }
        }

        return null;
    }

    /**
     * 检测基于响应体的WebShell特征
     */
    private DetectionResult detectResponseWebShell(NetworkEvent event, HttpInfo httpInfo) {
        String responseBody = httpInfo.getResponseBody();
        if (responseBody == null || responseBody.isEmpty()) {
            return null;
        }

        // 检查已知WebShell工具响应特征
        for (Map.Entry<String, WebShellSignature> entry : WEBSHELL_RESPONSE_SIGNATURES.entrySet()) {
            WebShellSignature signature = entry.getValue();
            if (signature.matches(responseBody)) {
                String matchedPattern = signature.getMatchedPattern(responseBody);
                return createDetectionResult(event, "WEBSHELL_" + signature.getToolName().toUpperCase(),
                        signature.getToolName() + " WebShell工具",
                        DetectionResult.ThreatLevel.HIGH, signature.getConfidence(),
                        signature.getDescription() + " 匹配特征: " + matchedPattern);
            }
        }

        // 检查Behinder特征（特殊处理）
        if (responseBody.contains(BEHINDER_RESPONSE_START) && responseBody.contains(BEHINDER_RESPONSE_END)) {
            return createDetectionResult(event, "WEBSHELL_BEHINDER",
                    "Behinder WebShell工具",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "Behinder响应特征匹配: " + BEHINDER_RESPONSE_START + " 和 " + BEHINDER_RESPONSE_END + " 标识符");
        }

        // 检查Weevely特征（特殊处理）
        String requestBody = httpInfo.getRequestBody();
        if (requestBody != null && requestBody.length() > WEEVELY_MIN_LENGTH && responseBody.length() > WEEVELY_MIN_LENGTH) {
            String requestSubstring = requestBody.length() > WEEVELY_MIN_LENGTH ?
                    requestBody.substring(WEEVELY_START_POS, WEEVELY_END_POS) : "";
            String responseSubstring = responseBody.length() > WEEVELY_MIN_LENGTH ?
                    responseBody.substring(WEEVELY_START_POS, WEEVELY_END_POS) : "";
            if (requestSubstring.equals(responseSubstring) && WEEVELY_SIGNATURE.equals(requestSubstring)) {
                return createDetectionResult(event, "WEBSHELL_WEEVELY",
                        "Weevely WebShell工具",
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        "Weevely响应特征匹配: 请求体和响应体第17-28位字符相同为" + WEEVELY_SIGNATURE);
            }
        }

        return null;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType, 
                                                String threatName, DetectionResult.ThreatLevel level, 
                                                double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("WEBSHELL_ACCESS")
                .assetLabel("COMPROMISED_WEB_SERVER")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        // 中等优先级
        return 20;
    }
}
