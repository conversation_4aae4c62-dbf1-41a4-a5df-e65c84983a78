package com.geeksec.session.threat.detector.detection.detector.remotecontrol;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.geeksec.session.threat.detector.detection.DetectorType;
import com.geeksec.session.threat.detector.detection.ThreatDetector;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import com.geeksec.session.threat.detector.model.input.TcpInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 远程控制工具检测器
 * 检测已知远程控制工具和可疑远程控制行为
 *
 * <AUTHOR>
 */
@Slf4j
public class RemoteControlToolDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    /**
     * 可疑端口范围起始值
     */
    private static final int SUSPICIOUS_PORT_START = 8000;

    /**
     * 可疑端口范围结束值
     */
    private static final int SUSPICIOUS_PORT_END = 9999;

    /**
     * 远程控制连接最小持续时间（毫秒）
     */
    private static final long MIN_REMOTE_CONTROL_DURATION = 30000L;

    /**
     * 已知远程控制工具端口映射
     */
    private static final Map<Integer, String> KNOWN_REMOTE_CONTROL_PORTS = new HashMap<>();

    /**
     * 已知远程控制工具特征
     */
    private static final Map<String, Set<String>> KNOWN_PROTOCOL_SIGNATURES = new HashMap<>();

    /**
     * 可疑端口范围 - 用于检测可疑远程控制行为
     */
    private static final Set<Integer> SUSPICIOUS_PORT_RANGES = new HashSet<>();

    static {
        initializeKnownRemoteControlPorts();
        initializeKnownProtocolSignatures();
        initializeSuspiciousPortRanges();
    }

    private static void initializeKnownRemoteControlPorts() {
        // 已知远程控制工具端口
        KNOWN_REMOTE_CONTROL_PORTS.put(3389, "RDP");
        KNOWN_REMOTE_CONTROL_PORTS.put(5900, "VNC");
        KNOWN_REMOTE_CONTROL_PORTS.put(5901, "VNC");
        KNOWN_REMOTE_CONTROL_PORTS.put(5902, "VNC");
        KNOWN_REMOTE_CONTROL_PORTS.put(22, "SSH");
        KNOWN_REMOTE_CONTROL_PORTS.put(23, "TELNET");
        KNOWN_REMOTE_CONTROL_PORTS.put(5631, "PCANYWHERE");
        KNOWN_REMOTE_CONTROL_PORTS.put(5632, "PCANYWHERE");
        KNOWN_REMOTE_CONTROL_PORTS.put(1494, "CITRIX");
        KNOWN_REMOTE_CONTROL_PORTS.put(2598, "CITRIX");
        KNOWN_REMOTE_CONTROL_PORTS.put(6129, "DAMEWARE");
        KNOWN_REMOTE_CONTROL_PORTS.put(4899, "RADMIN");
        KNOWN_REMOTE_CONTROL_PORTS.put(7070, "REALVNC");
        KNOWN_REMOTE_CONTROL_PORTS.put(8080, "TEAMVIEWER");
        KNOWN_REMOTE_CONTROL_PORTS.put(5938, "TEAMVIEWER");

        log.info("初始化已知远程控制工具端口映射，共 {} 个工具", KNOWN_REMOTE_CONTROL_PORTS.size());
    }

    private static void initializeKnownProtocolSignatures() {
        // RDP协议特征
        Set<String> rdpSignatures = new HashSet<>();
        rdpSignatures.add("mstshash");
        rdpSignatures.add("rdp");
        rdpSignatures.add("terminal");
        KNOWN_PROTOCOL_SIGNATURES.put("RDP", rdpSignatures);

        // VNC协议特征
        Set<String> vncSignatures = new HashSet<>();
        vncSignatures.add("RFB");
        vncSignatures.add("vnc");
        KNOWN_PROTOCOL_SIGNATURES.put("VNC", vncSignatures);

        // SSH协议特征
        Set<String> sshSignatures = new HashSet<>();
        sshSignatures.add("SSH-");
        sshSignatures.add("OpenSSH");
        KNOWN_PROTOCOL_SIGNATURES.put("SSH", sshSignatures);

        // TeamViewer协议特征
        Set<String> teamviewerSignatures = new HashSet<>();
        teamviewerSignatures.add("TeamViewer");
        teamviewerSignatures.add("TV_");
        KNOWN_PROTOCOL_SIGNATURES.put("TEAMVIEWER", teamviewerSignatures);

        log.info("初始化已知工具特征库，共 {} 个工具", KNOWN_PROTOCOL_SIGNATURES.size());
    }

    private static void initializeSuspiciousPortRanges() {
        // 常见的可疑端口范围，用于检测可疑远程控制行为
        // 高端口范围
        for (int port = SUSPICIOUS_PORT_START; port <= SUSPICIOUS_PORT_END; port++) {
            SUSPICIOUS_PORT_RANGES.add(port);
        }
        // 其他可疑端口
        SUSPICIOUS_PORT_RANGES.add(4444);
        SUSPICIOUS_PORT_RANGES.add(5555);
        SUSPICIOUS_PORT_RANGES.add(6666);
        SUSPICIOUS_PORT_RANGES.add(7777);

        log.info("初始化可疑端口范围，共 {} 个端口", SUSPICIOUS_PORT_RANGES.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.REMOTE_CONTROL;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 只处理TCP事件
            if (event.getEventType() != NetworkEvent.EventType.TCP || event.getTcpInfo() == null) {
                return results;
            }

            // 检测已知远程控制工具
            DetectionResult knownResult = detectKnownRemoteControl(event);
            if (knownResult != null) {
                results.add(knownResult);
            }

            // 检测可疑远程控制行为
            DetectionResult suspiciousResult = detectSuspiciousRemoteControl(event);
            if (suspiciousResult != null) {
                results.add(suspiciousResult);
            }

        } catch (Exception e) {
            log.error("远程控制工具检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测已知远程控制工具
     */
    private DetectionResult detectKnownRemoteControl(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        if (dstPort == null) {
            return null;
        }

        // 检查是否为已知的远程控制工具端口
        String protocol = KNOWN_REMOTE_CONTROL_PORTS.get(dstPort);
        if (protocol != null) {
            // 进一步检查载荷特征以确认协议
            if (hasKnownProtocolSignature(event, protocol)) {
                return createDetectionResult(event, "KNOWN_REMOTE_CONTROL_DETECTED",
                        "已知远程控制工具 - " + protocol,
                        DetectionResult.ThreatLevel.MEDIUM, 0.8,
                        String.format("检测到已知远程控制工具: %s，端口: %d", protocol, dstPort));
            } else {
                return createDetectionResult(event, "KNOWN_REMOTE_CONTROL_PORT_DETECTED",
                        "已知远程控制工具端口 - " + protocol,
                        DetectionResult.ThreatLevel.LOW, 0.6,
                        String.format("检测到已知远程控制工具端口: %s，端口: %d", protocol, dstPort));
            }
        }

        return null;
    }

    /**
     * 检测可疑远程控制行为
     */
    private DetectionResult detectSuspiciousRemoteControl(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        if (dstPort == null) {
            return null;
        }

        // 检查是否为可疑端口范围
        if (SUSPICIOUS_PORT_RANGES.contains(dstPort)) {
            // 检查是否有远程控制特征
            if (hasRemoteControlCharacteristics(event)) {
                return createDetectionResult(event, "SUSPICIOUS_REMOTE_CONTROL_DETECTED",
                        "可疑远程控制行为",
                        DetectionResult.ThreatLevel.HIGH, 0.8,
                        String.format("检测到可疑远程控制行为，端口: %d", dstPort));
            }
        }

        // 检查非标准端口上的已知工具特征
        if (!KNOWN_REMOTE_CONTROL_PORTS.containsKey(dstPort)) {
            String detectedProtocol = detectProtocolBySignature(event);
            if (detectedProtocol != null) {
                return createDetectionResult(event, "SUSPICIOUS_REMOTE_CONTROL_NONSTANDARD_PORT",
                        "可疑端口远程控制",
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        String.format("在非标准端口%d检测到%s工具特征", dstPort, detectedProtocol));
            }
        }

        return null;
    }

    /**
     * 检查是否有已知工具特征
     */
    private boolean hasKnownProtocolSignature(NetworkEvent event, String protocolName) {
        Set<String> signatures = KNOWN_PROTOCOL_SIGNATURES.get(protocolName);
        if (signatures == null) {
            return false;
        }

        TcpInfo tcpInfo = event.getTcpInfo();
        if (tcpInfo == null || tcpInfo.getPayload() == null) {
            return false;
        }

        String payload = new String(tcpInfo.getPayload());
        for (String signature : signatures) {
            if (payload.contains(signature)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过特征检测远控工具
     */
    private String detectProtocolBySignature(NetworkEvent event) {
        TcpInfo tcpInfo = event.getTcpInfo();
        if (tcpInfo == null || tcpInfo.getPayload() == null) {
            return null;
        }

        String payload = new String(tcpInfo.getPayload());
        for (Map.Entry<String, Set<String>> entry : KNOWN_PROTOCOL_SIGNATURES.entrySet()) {
            String protocolName = entry.getKey();
            Set<String> signatures = entry.getValue();

            for (String signature : signatures) {
                if (payload.contains(signature)) {
                    return protocolName;
                }
            }
        }
        return null;
    }

    /**
     * 检查是否具有远程控制特征
     */
    private boolean hasRemoteControlCharacteristics(NetworkEvent event) {
        TcpInfo tcpInfo = event.getTcpInfo();
        if (tcpInfo == null) {
            return false;
        }

        // 检查连接持续时间（远程控制通常有较长的连接时间）
        Long duration = tcpInfo.getConnectionDuration();
        // 连接时间超过30秒认为可能是远程控制
        if (duration != null && duration > MIN_REMOTE_CONTROL_DURATION) {
            return true;
        }

        // 检查载荷特征
        if (tcpInfo.getPayload() != null) {
            String payload = new String(tcpInfo.getPayload());
            // 检查常见的远程控制关键词
            String[] remoteControlKeywords = {
                    "remote", "control", "desktop", "screen", "mouse", "keyboard",
                    "vnc", "rdp", "ssh", "telnet", "shell", "cmd", "terminal"
            };

            String lowerPayload = payload.toLowerCase();
            for (String keyword : remoteControlKeywords) {
                if (lowerPayload.contains(keyword)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String threatType,
            String threatName, DetectionResult.ThreatLevel level,
            double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .detectorType(getDetectorType())
                .threatType(threatType)
                .threatName(threatName)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .sessionId(event.getSessionId())
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(LocalDateTime.now())
                .sessionLabel("REMOTE_CONTROL")
                .assetLabel("REMOTE_TARGET")
                .labelValue(threatName)
                .build();
    }

    @Override
    public int getPriority() {
        // 中等优先级
        return 40;
    }
}
