# 网络流量分析平台 (NTA Platform) 3.0

## 项目概述

网络流量分析平台 3.0 是一个基于云原生微服务架构的高性能网络安全分析系统，采用 Apache Flink 流处理技术和现代化技术栈，实现了高可用、可扩展的网络安全分析能力。平台通过实时流量分析和离线数据分析，提供全面的网络安全态势感知和威胁检测能力。

## 系统架构

该项目采用云原生微服务架构，基于 Kubernetes 和 Istio 服务网格，由以下主要组件构成：

### 目录结构

```
nta_3.0/
├── services/                    # 微服务组件
│   ├── common/                 # 公共模块和工具类
│   ├── auth/                   # 认证与授权服务
│   ├── analysis/               # 分析服务
│   ├── graph/                  # 图谱服务
│   ├── search/                 # 搜索服务
│   ├── notification/           # 通知服务
│   ├── task/                   # 任务管理服务
│   ├── config/                 # 配置管理服务
│   └── system/                 # 系统管理服务
│
├── flink-jobs/                 # Flink 流处理作业
│   ├── common/                 # 共享核心库
│   ├── traffic-etl-processor/    # 流量ETL处理器
│   ├── certificate-analyzer/   # 证书分析器
│   ├── alarm-processor/        # 告警处理器
│   └── threat-detector/        # 威胁检测器
│
├── frontend/                   # Vue.js 前端应用
│   ├── src/                    # 源代码
│   ├── public/                 # 静态资源
│   └── dist/                   # 构建输出
│
├── deployment/                 # 部署配置
│   ├── helm/                   # Helm 图表
│   │   ├── templates/          # Kubernetes 模板
│   │   ├── values/             # 环境配置
│   │   └── files/              # 配置文件和 SQL 脚本
│   └── docker/                 # Docker 配置
│
└── docs/                       # 项目文档
    ├── flink-operator-guide.md
    └── strimzi-kafka-operator-guide.md
```

### 微服务组件

1. **公共模块 (Common)**
   - 共享工具类和配置
   - 统一异常处理
   - 通用数据模型
   - 配置常量定义

2. **认证与授权服务 (Auth Service)**
   - 用户认证和授权
   - JWT 令牌管理
   - 用户和角色管理
   - 权限控制和安全策略

3. **分析服务 (Analysis Service)**
   - 网络流量数据分析
   - 元数据管理和处理
   - 会话分析和统计
   - 标签管理和分类

4. **图谱服务 (Graph Service)**
   - 基于 Nebula Graph 的网络关系图谱
   - 图数据查询和分析
   - 实体关联分析
   - 图谱可视化支持

5. **搜索服务 (Search Service)**
   - 基于 Elasticsearch 的全文搜索
   - 数据索引和查询优化
   - 高级搜索功能
   - 搜索结果聚合

6. **通知服务 (Notification Service)**
   - WebSocket 实时推送
   - 系统通知管理
   - 告警推送机制
   - 消息队列集成

7. **任务管理服务 (Task Service)**
   - 异步任务创建和管理
   - 任务状态跟踪
   - 任务执行调度
   - 任务结果处理和存储

8. **配置管理服务 (Config Service)**
   - 系统配置集中管理
   - 动态配置更新
   - 环境配置隔离
   - 配置版本控制

9. **系统管理服务 (System Service)**
   - 系统状态监控
   - 资源使用统计
   - 系统操作日志
   - 健康检查管理

### Flink 流处理作业

基于 **Apache Flink 1.20.1** 构建的实时流处理作业，通过 **Flink Kubernetes Operator** 进行生命周期管理：

1. **Flink Common (共享核心库)**
   - 通用工具类和配置
   - Protobuf 消息定义
   - 序列化和反序列化工具
   - 公共数据结构

2. **数据仓库处理器 (Data Warehouse Processor)**
   - 实时流量数据 ETL 处理
   - 多协议数据解析（DNS、HTTP、SSL、SSH 等）
   - 数据清洗和标准化
   - 数据写入 Doris 数据仓库

3. **证书分析器 (Certificate Analyzer)**
   - SSL/TLS 证书实时分析
   - 证书有效性验证
   - 证书链完整性检查
   - 证书异常检测

4. **图谱构建器 (Graph Builder)**
   - 实时构建网络连接关系图
   - 实体关系分析和建模
   - 图数据写入 Nebula Graph
   - 关系图谱更新

5. **威胁检测器 (Threat Detector)**
   - 实时网络威胁检测
   - 异常流量模式识别
   - 攻击行为分析
   - 威胁告警生成

### 核心技术栈

#### 后端技术
- **Java 17**: 编程语言
- **Spring Boot 3.0.7**: 微服务框架
- **MyBatis Flex 1.10.9**: ORM 框架
- **Apache Flink 1.20.1**: 流处理引擎
- **Knife4j 4.5.0**: API 文档生成

#### 数据存储
- **MySQL 8.0**: 关系型数据库
- **Redis**: 缓存和会话存储
- **Elasticsearch 7.17**: 搜索引擎
- **Nebula Graph 3.8.0**: 图数据库
- **Apache Doris**: 数据仓库
- **MinIO**: 对象存储

#### 消息队列
- **Apache Kafka**: 消息队列和流处理
- **Strimzi Kafka Operator**: Kafka 集群管理

#### 容器化和编排
- **Docker**: 容器化技术
- **Kubernetes 1.19+**: 容器编排平台
- **Helm 3**: 包管理工具
- **Istio 1.24**: 服务网格

#### 监控和运维
- **Prometheus**: 监控指标收集
- **Grafana**: 监控数据可视化
- **Flink Kubernetes Operator 1.11.0**: Flink 作业管理

#### 前端技术
- **Vue.js**: 前端框架
- **Element UI**: UI 组件库
- **ECharts**: 数据可视化
- **Nginx**: Web 服务器

## 快速开始

### 环境要求

- **Kubernetes**: 1.19+
- **Helm**: 3.0+
- **Docker**: 19+
- **Maven**: 3.6+
- **JDK**: 17
- **操作系统**: Linux/macOS

### 构建步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd nta_3.0
   ```

2. **编译项目**
   ```bash
   mvn clean package -DskipTests
   ```

3. **构建 Docker 镜像**
   ```bash
   mvn docker:build
   ```

### 部署步骤

#### 方式一：一键部署（推荐）

1. **执行一键部署脚本**
   ```bash
   cd deployment/helm
   ./deploy-with-dependencies.sh
   ```

#### 方式二：手动部署

1. **安装 Helm（如果尚未安装）**
   ```bash
   curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
   ```

2. **添加必要的 Helm 仓库**
   ```bash
   # Flink Kubernetes Operator
   helm repo add flink-operator-repo https://downloads.apache.org/flink/flink-kubernetes-operator-1.11.0/

   # Strimzi Kafka Operator
   helm repo add strimzi https://strimzi.io/charts/

   # Elastic Cloud on Kubernetes
   helm repo add elastic https://helm.elastic.co

   # Bitnami Charts
   helm repo add bitnami https://charts.bitnami.com/bitnami

   # 更新仓库
   helm repo update
   ```

3. **部署 NTA 平台**
   ```bash
   helm install nta ./deployment/helm -n nta --create-namespace
   ```

### 验证部署

1. **检查 Pod 状态**
   ```bash
   kubectl get pods -n nta
   ```

2. **检查服务状态**
   ```bash
   kubectl get svc -n nta
   ```

3. **访问前端界面**
   ```bash
   # 获取前端服务地址
   kubectl get svc nta-frontend -n nta
   ```

## 服务说明

### 微服务端口和路径

| 服务名称 | 端口 | 路径 | 描述 |
|---------|------|------|------|
| 认证与授权服务 | 8081 | `/auth` | 用户认证、授权和权限管理 |
| 分析服务 | 8082 | `/analyze`, `/metadata`, `/aggr`, `/tag` | 数据分析和元数据管理 |
| 图谱服务 | 8083 | `/atlas` | 网络关系图谱管理 |
| 搜索服务 | 8084 | `/search` | 全文搜索和数据查询 |
| 任务管理服务 | 8085 | `/task` | 异步任务管理 |
| 系统管理服务 | 8086 | `/system` | 系统监控和管理 |
| 配置管理服务 | 8087 | `/config` | 配置管理和同步 |
| 通知服务 | 8088 | `/notification` | 实时通知和推送 |

### API 文档

所有服务都集成了 Knife4j API 文档，可通过以下地址访问：

```
http://<service-host>:<port>/doc.html
```

例如：
- 认证服务 API 文档：`http://localhost:8081/doc.html`
- 分析服务 API 文档：`http://localhost:8082/doc.html`

## 开发指南

### 添加新服务

1. 在services目录下创建新的服务模块
```bash
mkdir -p services/new-service/src/main/java/com/geeksec/newservice
```

2. 添加pom.xml文件
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta-platform</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>new-service</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <name>new-service</name>
    <description>New Service</description>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

3. 创建主应用类
```java
package com.geeksec.newservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class NewServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(NewServiceApplication.class, args);
    }
}
```

4. 添加配置文件
```yaml
# application.yml
spring:
  application:
    name: new-service
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/nta?useUnicode=true&characterEncoding=utf8&useSSL=false
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
```

5. 在根pom.xml中添加新模块
```xml
<modules>
    <module>common</module>
    <module>services/auth</module>
    <module>services/analysis</module>
    <module>services/graph</module>
    <module>services/search</module>
    <module>services/notification</module>
    <module>services/task</module>
    <module>services/config</module>
    <module>services/system</module>
    <module>services/security</module>
    <module>services/new-service</module>
    <module>flink-jobs</module>
</modules>
```

6. 更新 Helm 图表
```bash
# 在 deployment/helm/nta/templates 目录下添加新服务的配置
```

### 添加新的Flink作业

1. 在flink-jobs目录下创建新的作业模块
2. 在flink-jobs/pom.xml中添加新模块
3. 实现流处理逻辑
4. 更新 Helm 图表中的 Flink 作业配置

## 监控与运维

### 健康检查

所有微服务都提供了 Spring Boot Actuator 健康检查端点：

```http
GET /actuator/health
```

### 监控指标

所有服务都提供了 Prometheus 格式的监控指标：

```http
GET /actuator/metrics
GET /actuator/prometheus
```

### 日志管理

- **日志级别**: 支持动态调整日志级别
- **日志格式**: 结构化 JSON 格式，便于日志聚合分析
- **日志收集**: 通过 Kubernetes 日志收集器统一收集

### Flink 作业监控

- **Flink Web UI**: 通过 Kubernetes Service 暴露 Flink JobManager Web UI
- **作业状态**: 通过 Flink Kubernetes Operator 监控作业状态
- **Checkpoint**: 自动管理 Checkpoint 和 Savepoint

### 告警配置

基于 Prometheus + Grafana + AlertManager 的监控告警体系：

- **服务可用性告警**: 服务下线、健康检查失败
- **性能告警**: CPU、内存、磁盘使用率过高
- **业务告警**: 数据处理延迟、错误率过高
- **Flink 作业告警**: 作业失败、Checkpoint 失败

## 性能优化

### JVM 调优

推荐的 JVM 参数配置：

```bash
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/tmp/heapdump.hprof
```

### Flink 调优

- **并行度设置**: 根据数据量和集群资源合理设置并行度
- **状态后端**: 使用 RocksDB 状态后端处理大状态
- **Checkpoint 配置**: 合理设置 Checkpoint 间隔和超时时间

## 故障排查

### 常见问题

1. **服务启动失败**
   - 检查配置文件是否正确
   - 检查依赖服务（MySQL、Redis 等）是否正常
   - 查看服务日志获取详细错误信息

2. **Flink 作业失败**
   - 检查 Kafka 连接是否正常
   - 检查数据格式是否符合预期
   - 查看 Flink JobManager 和 TaskManager 日志

3. **数据处理延迟**
   - 检查 Kafka 消费者 Lag
   - 检查 Flink 作业的背压情况
   - 调整并行度和资源配置

### 日志查看

```bash
# 查看服务日志
kubectl logs -f deployment/nta-auth -n nta

# 查看 Flink 作业日志
kubectl logs -f deployment/traffic-etl-processor-jobmanager -n nta

# 查看所有 Pod 状态
kubectl get pods -n nta -o wide
```

## 版本信息

- **当前版本**: 3.0.0-SNAPSHOT
- **Java 版本**: 17
- **Spring Boot 版本**: 3.0.7
- **Flink 版本**: 1.20.1
- **Kubernetes 版本**: 1.19+

## 贡献指南

1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- **项目维护**: GeekSec 团队
- **技术支持**: <EMAIL>
- **问题反馈**: 请通过 GitHub Issues 提交

---

**注意**: 本项目仍在积极开发中，API 和功能可能会发生变化。建议在生产环境使用前进行充分测试。
