package com.geeksec.certificate.analyzer.model.cert;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.enums.CertificateTrustStatus;
import com.geeksec.certificate.analyzer.model.extension.CertificateExtensionData;
import com.geeksec.certificate.analyzer.model.extension.UncommonOID;

import lombok.Data;

/**
 * X.509证书实体类，对应Doris中的dim_cert表
 *
 * <AUTHOR>
 * @since 2024/06/17
 */
@Data
public class X509Certificate {
    // 基础字段
    private byte[] cert;

    // 主题和颁发者信息
    private Map<String, String> subject = null;

    private Map<String, String> issuer = null;

    // 标识符和哈希
    private String derSha1 = null;

    private String derMd5 = null;

    private String derSha256 = null;

    private String pemMd5 = null;

    private String pemSha256 = null;

    private String pemSha1 = null;

    private String spkiSha256 = null;

    private String correctedAsn1Sha1 = null;

    // 基础信息
    private String version = null;

    private String serialNumber = null;

    private String format = null;

    // 关联ID
    private String issuerId = null;

    private String subjectId = null;

    // 时间相关
    private LocalDateTime notBefore = null;

    private LocalDateTime notAfter = null;

    /**
     * 证书有效期长度，单位：毫秒
     */
    private Long duration = 0L;

    private LocalDateTime importTime = null;

    // 名称和标识
    private String commonName = null;

    private List<String> subjectAltNames = new ArrayList<>();

    private List<String> issuerAltNames = new ArrayList<>();

    // 公钥信息
    private String publicKey = null;

    private String publicKeyAlgorithm = null;

    private String publicKeyLength = null;

    private String publicKeyParameter = null;

    private String publicKeyAlgOid = null;

    private String publicKeyParamOid = null;

    // 签名信息
    private String signature = null;

    private String signatureAlgorithm = null;

    private String signatureAlgName = null;

    private String signatureAlgOid = null;

    // 密钥用途和扩展
    private CertificateExtensionData certificateExtensions;

    /**
     * 密钥用途
     */
    private String keyUsage = null;

    /**
     * 扩展密钥用途
     */
    private List<String> extendedKeyUsage = new ArrayList<>();

    /**
     * 基本约束
     */
    private String basicConstraints = null;

    /**
     * 授权密钥标识符
     */
    private String authorityKeyIdentifier = null;

    /**
     * 主题密钥标识符
     */
    private String subjectKeyIdentifier = null;

    /**
     * CRL分发点
     */
    private List<String> crlDistributionPoints = new ArrayList<>();

    /**
     * 授权信息访问
     */
    private List<String> authorityInfoAccess = new ArrayList<>();

    /**
     * 主题信息访问
     */
    private List<String> subjectInfoAccess = new ArrayList<>();

    /**
     * 证书策略
     */
    private List<String> certPolicies = new ArrayList<>();

    // 扩展信息
    private Map<String, Object> extensions = new java.util.HashMap<>();

    // 证书链信息
    private String parentCertId = "";

    private List<String> certChainIds = new ArrayList<>();

    // 业务分类字段
    /**
     * 证书来源
     */
    private Integer source = 0; // 0: COLLECTED, 1: SYSTEM, 2: USER

    /**
     * 证书的信任状态，由 TrustValidator 根据可信CA库进行评估
     */
    private CertificateTrustStatus trustStatus = CertificateTrustStatus.UNKNOWN;

    private String userType = null;

    private String businessType = null;

    private String caType = null;

    private String industryType = null;

    private String subjectArea = null;

    private String issuerArea = null;

    // 分类字段
    private String userCategory = null;

    private String businessCategory = null;

    private String issuerCategory = null;

    private String industryCategory = null;

    // 状态和标记
    private Boolean isWhitelisted = false;

    private Boolean isBlacklisted = false;

    private Boolean isParsedSuccessfully = false;

    private Boolean isCorrupted = false;

    private Integer certOccurrenceCount = 1;

    private String processingMethod = null;

    private String parseStatus = null;

    // 任务和批次信息
    private String taskId = null;

    private String batchId = null;

    private List<String> userIdList = new ArrayList<>();

    // 评分字段
    private int negativeScore = 0;

    private int positiveScore = 0;

    private String threatLevel = null;

    private Set<CertificateLabel> labels = new HashSet<>();

    private List<String> forwardChunkHashes = new ArrayList<>();

    private List<String> reverseChunkHashes = new ArrayList<>();

    // 证书内嵌信息
    private List<String> certDomains = new ArrayList<>();

    private List<String> certIps = new ArrayList<>();

    // 特殊字段
    private List<String> uncommonOids = new ArrayList<>();

    private String organization = "";

    // 时间字段
    private LocalDateTime firstSeen = null;

    private LocalDateTime lastSeen = null;

    private LocalDateTime createTime = null;

    private LocalDateTime updateTime = null;

    private String remark = null;

    /**
     * 构造函数
     *
     * @param cert 证书字节数组
     */
    public X509Certificate(byte[] cert) {
        this.cert = cert;
        this.certificateExtensions = new CertificateExtensionData();
        // 初始化时间字段
        LocalDateTime now = LocalDateTime.now();
        this.firstSeen = now;
        this.lastSeen = now;
        this.createTime = now;
        this.updateTime = now;
    }

    /**
     * 获取颁发者信息
     * @return 颁发者信息映射
     */
    public Map<String, String> getIssuer() {
        return this.issuer;
    }

    /**
     * 设置用户分类
     * @param userCategory 用户分类
     */
    public void setUserCategory(String userCategory) {
        this.userCategory = userCategory;
    }

    /**
     * 设置业务分类
     * @param businessCategory 业务分类
     */
    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }

    /**
     * 设置颁发者分类
     * @param issuerCategory 颁发者分类
     */
    public void setIssuerCategory(String issuerCategory) {
        this.issuerCategory = issuerCategory;
    }

    /**
     * 设置行业分类
     * @param industryCategory 行业分类
     */
    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    /**
     * 获取通用名称
     * @return 通用名称
     */
    public String getCommonName() {
        return this.commonName;
    }

    /**
     * 获取主题信息
     * @return 主题信息映射
     */
    public Map<String, String> getSubject() {
        return this.subject;
    }

    /**
     * 同步扩展信息字段
     * 将CertificateExtensionData中的字段同步到平铺字段中
     */
    public void syncExtensionFields() {
        if (this.certificateExtensions != null) {
            this.keyUsage = this.certificateExtensions.getKeyUsage();
            this.extendedKeyUsage = this.certificateExtensions.getExtendedKeyUsage() != null
                    ? new ArrayList<>(this.certificateExtensions.getExtendedKeyUsage())
                    : new ArrayList<>();
            this.basicConstraints = this.certificateExtensions.getBasicConstraints();
            this.authorityKeyIdentifier = this.certificateExtensions.getAuthorityKeyIdentifier();
            this.subjectKeyIdentifier = this.certificateExtensions.getSubjectKeyIdentifier();
            this.crlDistributionPoints = this.certificateExtensions.getCrlDistributionPoints() != null
                    ? new ArrayList<>(this.certificateExtensions.getCrlDistributionPoints())
                    : new ArrayList<>();
            this.authorityInfoAccess = this.certificateExtensions.getAuthorityInfoAccess() != null
                    ? new ArrayList<>(this.certificateExtensions.getAuthorityInfoAccess())
                    : new ArrayList<>();
            this.subjectInfoAccess = this.certificateExtensions.getSubjectInfoAccess() != null
                    ? new ArrayList<>(this.certificateExtensions.getSubjectInfoAccess())
                    : new ArrayList<>();
            this.certPolicies = this.certificateExtensions.getCertPolicies() != null
                    ? new ArrayList<>(this.certificateExtensions.getCertPolicies())
                    : new ArrayList<>();
        }
    }

    /**
     * 获取扩展信息
     */
    public java.util.HashMap<String, String> getExtension() {
        java.util.HashMap<String, String> extensionMap = new java.util.HashMap<>();
        if (this.certificateExtensions != null) {
            if (this.keyUsage != null) {
                extensionMap.put("keyUsage", this.keyUsage);
            }
            if (this.basicConstraints != null) {
                extensionMap.put("basicConstraints", this.basicConstraints);
            }
            if (this.authorityKeyIdentifier != null) {
                extensionMap.put("authorityKeyIdentifier", this.authorityKeyIdentifier);
            }
            if (this.subjectKeyIdentifier != null) {
                extensionMap.put("subjectKeyIdentifier", this.subjectKeyIdentifier);
            }
            if (this.extendedKeyUsage != null && !this.extendedKeyUsage.isEmpty()) {
                extensionMap.put("extendedKeyUsage", String.join(",", this.extendedKeyUsage));
            }
        }
        return extensionMap;
    }

    /**
     * 设置扩展信息
     */
    public void setExtension(java.util.HashMap<String, String> extension) {
        if (extension != null) {
            this.keyUsage = extension.get("keyUsage");
            this.basicConstraints = extension.get("basicConstraints");
            this.authorityKeyIdentifier = extension.get("authorityKeyIdentifier");
            this.subjectKeyIdentifier = extension.get("subjectKeyIdentifier");

            String extKeyUsage = extension.get("extendedKeyUsage");
            if (extKeyUsage != null && !extKeyUsage.trim().isEmpty()) {
                this.extendedKeyUsage = java.util.Arrays.asList(extKeyUsage.split(","));
            }
        }
    }

    /**
     * 获取不常见OID列表（字符串格式）
     */
    public List<String> getUncommonOids() {
        return this.uncommonOids;
    }

    /**
     * 获取不常见OID列表（UncommonOID对象格式）
     */
    public List<UncommonOID> getUncommonOIDs() {
        // 简单返回空列表，实际应该从uncommonOids字段转换
        return new ArrayList<>();
    }

    /**
     * 设置不常见OID列表
     */
    public void setUncommonOIDs(List<com.geeksec.certificate.analyzer.model.extension.UncommonOID> uncommonOids) {
        // 简单转换为字符串列表
        if (uncommonOids != null) {
            this.uncommonOids = uncommonOids.stream()
                    .map(oid -> oid.getOID())
                    .collect(java.util.stream.Collectors.toList());
        }
    }

    /**
     * 获取证书来源
     * @return 证书来源
     */
    public Integer getSource() {
        return this.source;
    }

    public Long getImportTime() {
        if (this.createTime == null) {
            return System.currentTimeMillis();
        }
        return this.createTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public List<String> getAssociateDomain() {
        // 返回证书关联的域名列表
        // 这里可以从证书的SAN扩展或其他字段中提取域名
        if (this.certDomains != null) {
            return new ArrayList<>(this.certDomains);
        }
        return new ArrayList<>();
    }

    public List<String> getAssociateIP() {
        // 返回证书关联的IP地址列表
        // 这里可以从证书的SAN扩展或其他字段中提取IP地址
        if (this.certIps != null) {
            return new ArrayList<>(this.certIps);
        }
        return new ArrayList<>();
    }

    public List<String> getAssociateURL() {
        // 返回证书关联的URL列表
        // 这里可以从证书的扩展字段中提取URL信息
        List<String> urls = new ArrayList<>();
        // 可以从authorityInfoAccess或其他扩展中提取URL
        if (this.authorityInfoAccess != null) {
            urls.addAll(this.authorityInfoAccess);
        }
        if (this.subjectInfoAccess != null) {
            urls.addAll(this.subjectInfoAccess);
        }
        return urls;
    }

    public String getOrganization() {
        return this.organization;
    }

    public String getDerSha1() {
        return derSha1;
    }
    
    /**
     * 获取证书签名算法
     *
     * @return 签名算法
     */
    public String getSignatureAlgorithm() {
        return signatureAlgorithm;
    }
    
    /**
     * 获取证书版本
     *
     * @return 证书版本
     */
    public Integer getVersion() {
        if (version == null) {
            return null;
        }
        try {
            return Integer.parseInt(version);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public LocalDateTime getNotBefore() {
        return notBefore;
    }

    public LocalDateTime getNotAfter() {
        return notAfter;
    }

    public String getKeyUsage() {
        return keyUsage;
    }

    public List<String> getExtendedKeyUsage() {
        return extendedKeyUsage;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public String getIssuerId() {
        return issuerId;
    }

    public String getSignatureAlgName() {
        return signatureAlgName;
    }

    public CertificateExtensionData getCertificateExtensions() {
        return certificateExtensions;
    }

    /**
     * 获取证书ID（使用SHA1作为唯一标识）
     * @return 证书ID
     */
    public String getCertId() {
        return this.derSha1;
    }

    /**
     * 获取ASN1格式的MD5哈希值
     * @return ASN1 MD5哈希值
     */
    public String getASN1MD5() {
        return this.derMd5;
    }

    /**
     * 获取颁发者MD5哈希值
     * @return 颁发者MD5哈希值
     */
    public String getIssuerMD5() {
        if (this.issuer == null) {
            return null;
        }
        String issuerDn = getIssuerDn();
        if (issuerDn == null) {
            return null;
        }
        return org.apache.commons.codec.digest.DigestUtils.md5Hex(issuerDn);
    }

    /**
     * 获取主题MD5哈希值
     * @return 主题MD5哈希值
     */
    public String getSubjectMD5() {
        if (this.subject == null) {
            return null;
        }
        String subjectDn = getSubjectDn();
        if (subjectDn == null) {
            return null;
        }
        return org.apache.commons.codec.digest.DigestUtils.md5Hex(subjectDn);
    }

    /**
     * 获取序列号
     * @return 序列号
     */
    public String getSerialNumber() {
        return this.serialNumber;
    }

    /**
     * 获取标签集合
     * @return 标签集合
     */
    public Set<CertificateLabel> getLabels() {
        return this.labels;
    }

    /**
     * 获取前向块哈希列表
     * @return 前向块哈希列表
     */
    public List<String> getForwardChunkHashes() {
        return this.forwardChunkHashes;
    }

    /**
     * 设置证书是否格式良好
     * @param wellFormed 是否格式良好
     */
    public void setWellFormed(boolean wellFormed) {
        // 可以根据需要设置相关字段
        this.isParsedSuccessfully = wellFormed;
        this.isCorrupted = !wellFormed;
    }

    /**
     * 设置负面评分（原威胁评分）
     */
    public void setNegativeScore(int negativeScore) {
        // 根据黑名单状态设置负面分数
        if (negativeScore > 0) {
            this.negativeScore = Math.max(this.negativeScore, 60);
        }
    }

    /**
     * 设置正面评分（原可信评分）
     */
    public void setPositiveScore(int positiveScore) {
        if (positiveScore > 0) {
            this.positiveScore = Math.max(this.positiveScore, 80);
        }
    }

    /**
     * 设置威胁评分（兼容性方法）
     * @deprecated 使用 setNegativeScore() 替代
     */
    @Deprecated
    public void setThreatScore(int threatScore) {
        setNegativeScore(threatScore);
    }

    /**
     * 设置可信评分（兼容性方法）
     * @deprecated 使用 setPositiveScore() 替代
     */
    @Deprecated
    public void setTrustScore(int trustScore) {
        setPositiveScore(trustScore);
    }

    /**
     * 获取负面评分（原威胁评分）
     */
    public int getNegativeScore() {
        return negativeScore;
    }

    /**
     * 获取正面评分（原可信评分）
     */
    public int getPositiveScore() {
        return positiveScore;
    }

    /**
     * 获取威胁评分（兼容性方法）
     * @deprecated 使用 getNegativeScore() 替代
     */
    @Deprecated
    public int getThreatScore() {
        return negativeScore;
    }

    /**
     * 获取可信评分（兼容性方法）
     * @deprecated 使用 getPositiveScore() 替代
     */
    @Deprecated
    public int getTrustScore() {
        return positiveScore;
    }

    /**
     * 添加证书标签
     *
     * @param label 证书标签
     */
    public void addLabel(CertificateLabel label) {
        if (this.labels == null) {
            this.labels = new HashSet<>();
        }
        this.labels.add(label);
    }

    /**
     * 获取证书链
     *
     * @return 证书链SHA1列表
     */
    public List<String> getCertificateChain() {
        return this.certChainIds;
    }

    /**
     * 设置证书链
     *
     * @param certificateChain 证书链SHA1列表
     */
    public void setCertificateChain(List<String> certificateChain) {
        this.certChainIds = certificateChain;
    }

    /**
     * 获取主题DN
     *
     * @return 主题DN字符串
     */
    public String getSubjectDn() {
        if (this.subject == null) {
            return null;
        }
        // 构建DN字符串
        StringBuilder dn = new StringBuilder();
        for (Map.Entry<String, String> entry : this.subject.entrySet()) {
            if (dn.length() > 0) {
                dn.append(", ");
            }
            dn.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return dn.toString();
    }

    /**
     * 获取颁发者DN
     *
     * @return 颁发者DN字符串
     */
    public String getIssuerDn() {
        if (this.issuer == null) {
            return null;
        }
        // 构建DN字符串
        StringBuilder dn = new StringBuilder();
        for (Map.Entry<String, String> entry : this.issuer.entrySet()) {
            if (dn.length() > 0) {
                dn.append(", ");
            }
            dn.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return dn.toString();
    }

    /**
     * 获取扩展信息映射
     *
     * @return 扩展信息映射
     */
    public Map<String, String> getExtensions() {
        Map<String, String> extensionMap = new java.util.HashMap<>();
        if (this.certificateExtensions != null) {
            if (this.keyUsage != null) {
                extensionMap.put("keyUsage", this.keyUsage);
            }
            if (this.basicConstraints != null) {
                extensionMap.put("basicConstraints", this.basicConstraints);
            }
            if (this.authorityKeyIdentifier != null) {
                extensionMap.put("authorityKeyIdentifier", this.authorityKeyIdentifier);
            }
            if (this.subjectKeyIdentifier != null) {
                extensionMap.put("subjectKeyIdentifier", this.subjectKeyIdentifier);
            }
            if (this.extendedKeyUsage != null && !this.extendedKeyUsage.isEmpty()) {
                extensionMap.put("extendedKeyUsage", String.join(",", this.extendedKeyUsage));
            }
        }
        return extensionMap;
    }

    /**
     * 获取 NotBefore 时间戳
     *
     * @return NotBefore 时间戳（毫秒）
     */
    public long getNotBeforeTimestamp() {
        if (this.notBefore == null) {
            return 0;
        }
        return this.notBefore.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取 NotAfter 时间戳
     *
     * @return NotAfter 时间戳（毫秒）
     */
    public long getNotAfterTimestamp() {
        if (this.notAfter == null) {
            return 0;
        }
        return this.notAfter.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
