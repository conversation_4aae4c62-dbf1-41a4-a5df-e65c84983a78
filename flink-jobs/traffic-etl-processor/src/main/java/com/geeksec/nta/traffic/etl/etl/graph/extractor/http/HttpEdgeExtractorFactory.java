package com.geeksec.nta.traffic.etl.etl.graph.extractor.http;

import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.http.edge.ClientUsesUAEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.http.edge.UAHasDeviceEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.http.edge.UAHasOSEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.http.edge.UARequestsDomainEdgeExtractor;

import java.util.Arrays;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:00$ 2025/6/17$
 **/
public class HttpEdgeExtractorFactory {
    public static List<BaseEdgeExtractor> getEdgeExtractors() {
        return Arrays.asList(
                new ClientUsesUAEdgeExtractor(),
                new UAHasDeviceEdgeExtractor(),
                new UAHasOSEdgeExtractor(),
                new UARequestsDomainEdgeExtractor()
        );
    }
}
