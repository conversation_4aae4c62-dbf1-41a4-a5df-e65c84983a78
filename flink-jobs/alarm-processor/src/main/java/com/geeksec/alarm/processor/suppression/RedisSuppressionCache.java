package com.geeksec.alarm.processor.suppression;

import com.geeksec.alarm.processor.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.io.Closeable;
import java.io.Serializable;

/**
 * Redis抑制规则缓存
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class RedisSuppressionCache implements Serializable, Closeable {
    
    private static final long serialVersionUID = 1L;
    
    private final RedisConfig redisConfig;
    private transient JedisPool jedisPool;
    
    public RedisSuppressionCache(RedisConfig redisConfig) {
        this.redisConfig = redisConfig;
        initializeJedisPool();
    }
    
    /**
     * 初始化Jedis连接池
     */
    private void initializeJedisPool() {
        if (jedisPool != null) {
            return;
        }
        
        try {
            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(redisConfig.getMaxTotal());
            poolConfig.setMaxIdle(redisConfig.getMaxIdle());
            poolConfig.setMinIdle(redisConfig.getMinIdle());
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestOnReturn(true);
            poolConfig.setTestWhileIdle(true);
            
            if (redisConfig.getPassword() != null && !redisConfig.getPassword().trim().isEmpty()) {
                jedisPool = new JedisPool(poolConfig, 
                        redisConfig.getHost(), 
                        redisConfig.getPort(), 
                        redisConfig.getConnectionTimeout(),
                        redisConfig.getSocketTimeout(),
                        redisConfig.getPassword(), 
                        redisConfig.getDatabase());
            } else {
                jedisPool = new JedisPool(poolConfig, 
                        redisConfig.getHost(), 
                        redisConfig.getPort(), 
                        redisConfig.getConnectionTimeout(),
                        redisConfig.getSocketTimeout(),
                        redisConfig.getDatabase());
            }
            
            log.info("Redis连接池初始化成功: {}:{}/{}", 
                    redisConfig.getHost(), redisConfig.getPort(), redisConfig.getDatabase());
        } catch (Exception e) {
            log.error("Redis连接池初始化失败", e);
            throw new RuntimeException("Redis连接池初始化失败", e);
        }
    }
    
    /**
     * 构建Redis键
     */
    private String buildRedisKey(String victim, String attacker, String label) {
        return redisConfig.getSuppressionKeyPrefix() + victim + "|" + attacker + "|" + label;
    }
    
    /**
     * 添加抑制规则到Redis
     */
    public void addSuppressionRule(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return;
        }
        
        String key = buildRedisKey(victim, attacker, label);
        
        try (Jedis jedis = getJedis()) {
            if (redisConfig.getCacheExpireSeconds() > 0) {
                jedis.setex(key, redisConfig.getCacheExpireSeconds(), "1");
            } else {
                jedis.set(key, "1");
            }
            log.debug("添加抑制规则到Redis: {}", key);
        } catch (Exception e) {
            log.error("添加抑制规则到Redis失败: {}", key, e);
        }
    }
    
    /**
     * 从Redis移除抑制规则
     */
    public void removeSuppressionRule(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return;
        }
        
        String key = buildRedisKey(victim, attacker, label);
        
        try (Jedis jedis = getJedis()) {
            jedis.del(key);
            log.debug("从Redis移除抑制规则: {}", key);
        } catch (Exception e) {
            log.error("从Redis移除抑制规则失败: {}", key, e);
        }
    }
    
    /**
     * 检查抑制规则是否存在
     */
    public boolean shouldSuppress(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }
        
        String key = buildRedisKey(victim, attacker, label);
        
        try (Jedis jedis = getJedis()) {
            return jedis.exists(key);
        } catch (Exception e) {
            log.error("检查抑制规则失败: {}", key, e);
            return false;
        }
    }
    
    /**
     * 批量加载抑制规则到Redis
     */
    public void batchLoadSuppressionRules(java.util.List<java.util.Map<String, Object>> rules) {
        if (rules == null || rules.isEmpty()) {
            return;
        }
        
        try (Jedis jedis = getJedis()) {
            for (java.util.Map<String, Object> rule : rules) {
                String victim = (String) rule.get("victim");
                String attacker = (String) rule.get("attacker");
                String label = (String) rule.get("label");
                
                if (victim != null && attacker != null && label != null) {
                    String key = buildRedisKey(victim, attacker, label);
                    if (redisConfig.getCacheExpireSeconds() > 0) {
                        jedis.setex(key, redisConfig.getCacheExpireSeconds(), "1");
                    } else {
                        jedis.set(key, "1");
                    }
                }
            }
            log.info("批量加载 {} 条抑制规则到Redis", rules.size());
        } catch (Exception e) {
            log.error("批量加载抑制规则到Redis失败", e);
        }
    }
    
    /**
     * 清空所有抑制规则
     */
    public void clearAllSuppressionRules() {
        try (Jedis jedis = getJedis()) {
            String pattern = redisConfig.getSuppressionKeyPrefix() + "*";
            java.util.Set<String> keys = jedis.keys(pattern);
            if (!keys.isEmpty()) {
                jedis.del(keys.toArray(new String[0]));
                log.info("清空 {} 条抑制规则", keys.size());
            }
        } catch (Exception e) {
            log.error("清空抑制规则失败", e);
        }
    }
    
    /**
     * 获取缓存中的抑制规则数量
     */
    public long getCacheSize() {
        try (Jedis jedis = getJedis()) {
            String pattern = redisConfig.getSuppressionKeyPrefix() + "*";
            return jedis.keys(pattern).size();
        } catch (Exception e) {
            log.error("获取缓存大小失败", e);
            return 0;
        }
    }
    
    /**
     * 获取Jedis连接
     */
    private Jedis getJedis() {
        if (jedisPool == null) {
            initializeJedisPool();
        }
        return jedisPool.getResource();
    }
    
    @Override
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            log.info("Redis连接池已关闭");
        }
    }
}
