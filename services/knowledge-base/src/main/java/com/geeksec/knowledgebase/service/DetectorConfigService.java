package com.geeksec.knowledgebase.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 检测器配置服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DetectorConfigService {

    private final Map<String, Map<String, Object>> detectorConfigs = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        log.info("初始化检测器配置服务");
        loadDetectorConfigs();
        log.info("检测器配置服务初始化完成，加载配置: {} 个", detectorConfigs.size());
    }

    /**
     * 根据检测器名称获取配置
     *
     * @param detectorName 检测器名称
     * @return 检测器配置
     */
    @Cacheable(value = "detector-config", key = "#detectorName")
    public Map<String, Object> getDetectorConfig(String detectorName) {
        return detectorConfigs.getOrDefault(detectorName, createDefaultConfig(detectorName));
    }

    /**
     * 获取所有检测器配置
     *
     * @return 所有检测器配置
     */
    public Map<String, Object> getAllDetectorConfigs() {
        return Map.of(
                "configs", detectorConfigs,
                "count", detectorConfigs.size()
        );
    }

    /**
     * 加载检测器配置
     */
    private void loadDetectorConfigs() {
        // 证书相关检测器配置
        detectorConfigs.put("certificate-threat-detector", createCertificateThreatDetectorConfig());
        detectorConfigs.put("certificate-collision-detector", createCertificateCollisionDetectorConfig());
        detectorConfigs.put("blocked-certificate-detector", createBlockedCertificateDetectorConfig());
        
        // 域名相关检测器配置
        detectorConfigs.put("malicious-domain-detector", createMaliciousDomainDetectorConfig());
        detectorConfigs.put("c2-domain-detector", createC2DomainDetectorConfig());
        detectorConfigs.put("mining-domain-detector", createMiningDomainDetectorConfig());
        
        // IP相关检测器配置
        detectorConfigs.put("c2-ip-detector", createC2IpDetectorConfig());
        detectorConfigs.put("ioc-ip-detector", createIocIpDetectorConfig());
        
        // 通用检测器配置
        detectorConfigs.put("geo-location-detector", createGeoLocationDetectorConfig());
    }

    /**
     * 创建证书威胁检测器配置
     */
    private Map<String, Object> createCertificateThreatDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 1);
        config.put("timeout", 5000);
        config.put("description", "证书威胁检测器");
        config.put("parameters", Map.of(
                "checkMaliciousDomains", true,
                "checkC2Threats", true,
                "checkMiningDomains", true,
                "checkFreeCertificates", true
        ));
        return config;
    }

    /**
     * 创建证书碰撞检测器配置
     */
    private Map<String, Object> createCertificateCollisionDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 2);
        config.put("timeout", 3000);
        config.put("description", "证书哈希碰撞检测器");
        config.put("parameters", Map.of(
                "checkMd5Collision", true,
                "checkSha1Collision", false,
                "alertThreshold", 1
        ));
        return config;
    }

    /**
     * 创建黑名单证书检测器配置
     */
    private Map<String, Object> createBlockedCertificateDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 1);
        config.put("timeout", 2000);
        config.put("description", "黑名单证书检测器");
        config.put("parameters", Map.of(
                "useLocalBlacklist", true,
                "useRemoteBlacklist", false,
                "cacheTimeout", 3600
        ));
        return config;
    }

    /**
     * 创建恶意域名检测器配置
     */
    private Map<String, Object> createMaliciousDomainDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 1);
        config.put("timeout", 3000);
        config.put("description", "恶意域名检测器");
        config.put("parameters", Map.of(
                "checkThreatIntelligence", true,
                "checkDomainReputation", true,
                "cacheTimeout", 1800
        ));
        return config;
    }

    /**
     * 创建C2域名检测器配置
     */
    private Map<String, Object> createC2DomainDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 1);
        config.put("timeout", 3000);
        config.put("description", "C2威胁域名检测器");
        config.put("parameters", Map.of(
                "checkC2Feeds", true,
                "checkBotnetDomains", true,
                "cacheTimeout", 1800
        ));
        return config;
    }

    /**
     * 创建挖矿域名检测器配置
     */
    private Map<String, Object> createMiningDomainDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 2);
        config.put("timeout", 2000);
        config.put("description", "挖矿域名检测器");
        config.put("parameters", Map.of(
                "checkMiningPools", true,
                "checkCryptojacking", true,
                "cacheTimeout", 3600
        ));
        return config;
    }

    /**
     * 创建C2 IP检测器配置
     */
    private Map<String, Object> createC2IpDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 1);
        config.put("timeout", 3000);
        config.put("description", "C2威胁IP检测器");
        config.put("parameters", Map.of(
                "checkC2IpFeeds", true,
                "checkBotnetIps", true,
                "cacheTimeout", 1800
        ));
        return config;
    }

    /**
     * 创建IOC IP检测器配置
     */
    private Map<String, Object> createIocIpDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 1);
        config.put("timeout", 3000);
        config.put("description", "IOC IP检测器");
        config.put("parameters", Map.of(
                "checkIocFeeds", true,
                "checkThreatIntelligence", true,
                "cacheTimeout", 1800
        ));
        return config;
    }

    /**
     * 创建地理位置检测器配置
     */
    private Map<String, Object> createGeoLocationDetectorConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("priority", 3);
        config.put("timeout", 2000);
        config.put("description", "地理位置检测器");
        config.put("parameters", Map.of(
                "useMaxMindDb", true,
                "cacheTimeout", 86400,
                "enableAsnLookup", true
        ));
        return config;
    }

    /**
     * 创建默认配置
     */
    private Map<String, Object> createDefaultConfig(String detectorName) {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", false);
        config.put("priority", 10);
        config.put("timeout", 5000);
        config.put("description", "未知检测器: " + detectorName);
        config.put("parameters", Map.of());
        return config;
    }
}
