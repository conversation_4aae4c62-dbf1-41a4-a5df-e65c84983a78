package com.geeksec.alarm.processor.suppression.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警抑制规则变更消息
 * 用于通过Kafka传递抑制规则的增删改操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmSuppressionChangeMessage {

    /**
     * 操作类型：ADD（添加抑制规则）、REMOVE（移除抑制规则）
     */
    private String operation;

    /**
     * 受害者IP
     */
    private String victim;

    /**
     * 攻击者IP
     */
    private String attacker;

    /**
     * 告警标签（抑制规则匹配的标签）
     */
    private String label;

    /**
     * 变更时间戳
     */
    private Long timestamp;

    /**
     * 操作用户（可选）
     */
    private String operator;

    /**
     * 抑制规则项信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmSuppressionItem {
        private String victim;
        private String attacker;
        private String label;
    }
}