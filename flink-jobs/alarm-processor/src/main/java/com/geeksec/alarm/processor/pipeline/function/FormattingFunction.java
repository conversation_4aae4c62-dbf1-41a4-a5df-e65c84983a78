package com.geeksec.alarm.processor.pipeline.function;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 告警格式化功能
 * 对已经过知识库增强和类型特化处理的告警进行最终格式化
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class FormattingFunction extends RichMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    /** 指标计数器 */
    private transient Counter totalAlarms;
    private transient Counter formattedAlarms;
    private transient Counter formattingErrors;
    
    public FormattingFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-formatting");
        
        totalAlarms = metricGroup.counter("total_alarms");
        formattedAlarms = metricGroup.counter("formatted_alarms");
        formattingErrors = metricGroup.counter("formatting_errors");
        
        log.info("告警格式化功能初始化完成");
    }
    
    @Override
    public Alarm map(Alarm alarm) throws Exception {
        totalAlarms.inc();
        
        try {
            performFinalFormatting(alarm);

            // 标记为已格式化
            alarm.getProcessingStatus().setFormatted(true);

            formattedAlarms.inc();
            log.debug("告警格式化完成: {}", alarm.getAlarmId());

            return alarm;
            
        } catch (Exception e) {
            formattingErrors.inc();
            log.error("告警格式化失败: {}", e.getMessage(), e);
            
            // 添加错误信息到处理状态
            if (alarm.getProcessingStatus().getProcessingErrors() == null) {
                alarm.getProcessingStatus().setProcessingErrors(new ArrayList<>());
            }
            alarm.getProcessingStatus().getProcessingErrors().add("格式化失败: " + e.getMessage());
            
            return alarm;
        }
    }

    /**
     * 执行最终格式化（针对已经过特化处理的告警）
     */
    private void performFinalFormatting(Alarm alarm) {
        // 如果原因分析为空，生成基础原因分析
        if (alarm.getReasonAnalysis() == null) {
            alarm.setReasonAnalysis(generateBasicReasonAnalysis(alarm));
        } else {
            // 计算风险评分
            int riskScore = calculateRiskScore(alarm, alarm.getReasonAnalysis().getDetailedReasons());
            alarm.getReasonAnalysis().setRiskScore(riskScore);

            // 生成证据列表
            List<String> evidence = generateEvidence(alarm);
            alarm.getReasonAnalysis().setEvidence(evidence);
        }

        // 处理建议应该由专门的告警处理器生成，这里不再提供通用建议
        if (alarm.getHandlingSuggestions() == null) {
            log.warn("告警 {} 缺少处理建议，请检查对应的告警处理器实现", alarm.getAlarmId());
        }
    }

    /**
     * 生成基础原因分析
     */
    private Alarm.ReasonAnalysis generateBasicReasonAnalysis(Alarm alarm) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        // 根据告警类型生成基础检测原因
        Alarm.DetectionReason primaryReason = Alarm.DetectionReason.builder()
                .reasonType("基础检测")
                .description("基于" + alarm.getDetectorType() + "检测到的异常行为")
                .detectedFeature(alarm.getAlarmType())
                .actualValue(alarm.getDescription())
                .expectedValue("正常行为")
                .importance(5)
                .build();
        
        reasons.add(primaryReason);
        
        // 计算风险评分
        int riskScore = calculateRiskScore(alarm, reasons);
        
        // 生成证据
        List<String> evidence = generateEvidence(alarm);
        
        return Alarm.ReasonAnalysis.builder()
                .primaryReason("检测到" + alarm.getAlarmType() + "类型的异常行为")
                .detailedReasons(reasons)
                .riskScore(riskScore)
                .evidence(evidence)
                .build();
    }
    
    /**
     * 创建检测原因
     */
    private Alarm.DetectionReason createDetectionReason(String reasonType, String description,
                                                       String detectedFeature, String actualValue,
                                                       String expectedValue, int importance) {
        return Alarm.DetectionReason.builder()
                .reasonType(reasonType)
                .description(description)
                .detectedFeature(detectedFeature)
                .actualValue(actualValue)
                .expectedValue(expectedValue)
                .importance(importance)
                .build();
    }
    
    /**
     * 计算风险评分
     */
    private int calculateRiskScore(Alarm alarm, List<Alarm.DetectionReason> reasons) {
        int baseScore = 0;
        
        // 根据告警级别设置基础分数
        switch (alarm.getAlarmLevel()) {
            case LOW:
                baseScore = 30;
                break;
            case MEDIUM:
                baseScore = 50;
                break;
            case HIGH:
                baseScore = 70;
                break;
            case CRITICAL:
                baseScore = 90;
                break;
        }
        
        // 根据置信度调整分数
        if (alarm.getConfidence() != null) {
            baseScore = (int) (baseScore * alarm.getConfidence());
        }
        
        // 根据检测原因的重要性调整分数
        int reasonScore = reasons.stream()
                .mapToInt(Alarm.DetectionReason::getImportance)
                .max()
                .orElse(0);
        
        int finalScore = Math.max(baseScore, reasonScore * 10);
        return Math.min(finalScore, 100); // 确保不超过100
    }
    
    /**
     * 生成证据列表
     */
    private List<String> generateEvidence(Alarm alarm) {
        List<String> evidence = new ArrayList<>();
        
        if (alarm.getSrcIp() != null) {
            evidence.add("源IP地址: " + alarm.getSrcIp());
        }
        
        if (alarm.getDstIp() != null) {
            evidence.add("目标IP地址: " + alarm.getDstIp());
        }
        
        if (alarm.getProtocol() != null) {
            evidence.add("协议类型: " + alarm.getProtocol());
        }
        
        if (alarm.getCertificateInfo() != null) {
            evidence.add("证书哈希: " + alarm.getCertificateInfo().getCertHash());
            if (alarm.getCertificateInfo().getSubjectCn() != null) {
                evidence.add("证书主题: " + alarm.getCertificateInfo().getSubjectCn());
            }
        }
        
        if (alarm.getDetectorType() != null) {
            evidence.add("检测器类型: " + alarm.getDetectorType());
        }
        
        return evidence;
    }
    
    /**
     * 生成处理建议
     */
    private Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm) {
        List<String> immediateActions = new ArrayList<>();
        List<String> investigationSteps = new ArrayList<>();
        List<String> preventionMeasures = new ArrayList<>();
        String priority;
        
        // 根据告警类型和级别生成建议
        if (alarm.isHighPriority()) {
            priority = "高";
            immediateActions.addAll(Arrays.asList(
                    "立即隔离相关主机或网络段",
                    "阻断可疑IP地址的网络连接",
                    "通知安全团队进行紧急响应"
            ));
        } else {
            priority = "中";
            immediateActions.addAll(Arrays.asList(
                    "监控相关网络活动",
                    "收集更多证据信息",
                    "评估影响范围"
            ));
        }
        
        // 通用调查步骤
        investigationSteps.addAll(Arrays.asList(
                "分析相关日志文件",
                "检查网络流量模式",
                "验证告警的准确性",
                "确定攻击时间线",
                "评估数据泄露风险"
        ));
        
        // 预防措施
        preventionMeasures.addAll(Arrays.asList(
                "更新安全策略和规则",
                "加强网络监控",
                "定期更新威胁情报",
                "进行安全意识培训",
                "完善应急响应流程"
        ));

        // 恢复步骤
        List<String> recoverySteps = new ArrayList<>();
        if (alarm.isHighPriority()) {
            recoverySteps.addAll(Arrays.asList(
                    "使用可靠的安全软件进行全面扫描和清除",
                    "及时更新操作系统和应用程序到最新版本，修补安全漏洞",
                    "加强账户密码，避免使用弱密码",
                    "备份重要数据，以防万一",
                    "恢复正常的网络连接和服务"
            ));
        } else {
            recoverySteps.addAll(Arrays.asList(
                    "备份重要数据，以防万一",
                    "及时更新操作系统和应用程序到最新版本",
                    "修补安全漏洞",
                    "恢复正常的网络连接和服务"
            ));
        }

        return Alarm.HandlingSuggestions.builder()
                .immediateActions(immediateActions)
                .investigationSteps(investigationSteps)
                .preventionMeasures(preventionMeasures)
                .recoverySteps(recoverySteps)
                .priority(priority)
                .build();
    }
    
    @Override
    public void close() throws Exception {
        log.info("告警格式化功能关闭，格式化统计: 总计={}, 成功={}, 失败={}", 
                totalAlarms.getCount(), 
                formattedAlarms.getCount(), 
                formattingErrors.getCount());
        super.close();
    }
}
