package com.geeksec.certificate.analyzer.operator.analysis.core;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.operator.analysis.attribute.CertificateAttributeExtractor;
import com.geeksec.certificate.analyzer.operator.analysis.compliance.CertificateComplianceValidator;
import com.geeksec.certificate.analyzer.operator.analysis.scoring.CertificateRiskScorer;
import com.geeksec.certificate.analyzer.operator.analysis.threat.CertificateThreatDetector;
import com.geeksec.certificate.analyzer.operator.analysis.trust.CertificateTrustEvaluator;
import com.geeksec.certificate.analyzer.operator.analysis.security.CertificateSecurityAnalyzer;
import com.geeksec.certificate.analyzer.operator.analysis.classification.CertificateClassifier;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书分析引擎
 * 
 * 职责：
 * 1. 统一协调所有分析组件的执行
 * 2. 管理分析流程和异常处理
 * 3. 提供统一的分析入口点
 * 4. 确保分析组件按正确顺序执行
 * 
 * 分析流程：
 * 1. 属性提取 (CertificateAttributeExtractor)
 * 2. 合规性验证 (CertificateComplianceValidator)
 * 3. 安全分析 (CertificateSecurityAnalyzer)
 * 4. 信任评估 (CertificateTrustEvaluator)
 * 5. 威胁检测 (CertificateThreatDetector)
 * 6. 证书分类 (CertificateClassifier)
 * 7. 风险评分 (CertificateRiskScorer)
 * 
 * 架构优势：
 * - 统一的分析入口，便于管理和监控
 * - 清晰的分析流程，易于理解和维护
 * - 良好的错误隔离，单个组件失败不影响整体流程
 * - 模块化设计，便于扩展和优化
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateAnalysisEngine extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 证书属性提取器 */
    private CertificateAttributeExtractor attributeExtractor;
    
    /** 证书合规性验证器 */
    private CertificateComplianceValidator complianceValidator;

    /** 证书安全分析器 */
    private CertificateSecurityAnalyzer securityAnalyzer;

    /** 证书信任评估器 */
    private CertificateTrustEvaluator trustEvaluator;

    /** 证书威胁检测器 */
    private CertificateThreatDetector threatDetector;

    /** 证书分类器 */
    private CertificateClassifier certificateClassifier;

    /** 证书风险评分器 */
    private CertificateRiskScorer riskScorer;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        log.info("证书分析引擎初始化开始");
        
        // 初始化所有分析组件
        initializeAnalysisComponents(parameters);
        
        log.info("证书分析引擎初始化完成");
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("开始执行证书综合分析，证书ID: {}", certificate.getDerSha1());

        try {
            // 1. 属性提取 - 提取证书的基础属性和元数据
            X509Certificate cert1 = executeWithErrorHandling(
                () -> attributeExtractor.map(certificate),
                "属性提取",
                certificate
            );

            // 2. 合规性验证 - 验证证书是否符合安全标准和行业规范
            X509Certificate cert2 = executeWithErrorHandling(
                () -> complianceValidator.map(cert1),
                "合规性验证",
                cert1
            );

            // 3. 安全分析 - 进行详细的安全性分析
            executeWithErrorHandling(
                () -> { securityAnalyzer.analyzeCertificateSecurity(cert2); return cert2; },
                "安全分析",
                cert2
            );

            // 4. 信任评估 - 评估证书的可信度和信任级别
            X509Certificate cert3 = executeWithErrorHandling(
                () -> trustEvaluator.map(cert2),
                "信任评估",
                cert2
            );

            // 5. 威胁检测 - 检测与证书相关的各种威胁和恶意活动
            X509Certificate cert4 = executeWithErrorHandling(
                () -> threatDetector.map(cert3),
                "威胁检测",
                cert3
            );

            // 6. 证书分类 - 对证书进行多维度分类
            executeWithErrorHandling(
                () -> { certificateClassifier.classifyCertificate(cert4); return cert4; },
                "证书分类",
                cert4
            );

            // 7. 风险评分 - 基于前面的分析结果计算综合风险评分
            X509Certificate finalCert = executeWithErrorHandling(
                () -> riskScorer.map(cert4),
                "风险评分",
                cert4
            );

            log.debug("证书综合分析完成，证书ID: {}, 标签数量: {}",
                finalCert.getDerSha1(),
                finalCert.getLabels() != null ? finalCert.getLabels().size() : 0);

            return finalCert;
            
        } catch (Exception e) {
            log.error("证书分析过程中发生严重错误，证书ID: {}, 错误: {}", 
                certificate.getDerSha1(), e.getMessage(), e);
            // 即使发生错误，也要返回证书对象，确保流程不中断
        }
        
        return certificate;
    }

    /**
     * 初始化所有分析组件
     */
    private void initializeAnalysisComponents(Configuration parameters) throws Exception {
        // 初始化属性提取器
        attributeExtractor = new CertificateAttributeExtractor();
        log.debug("属性提取器初始化完成");

        // 初始化合规性验证器
        complianceValidator = new CertificateComplianceValidator();
        log.debug("合规性验证器初始化完成");

        // 初始化安全分析器
        securityAnalyzer = new CertificateSecurityAnalyzer();
        log.debug("安全分析器初始化完成");

        // 初始化信任评估器
        trustEvaluator = new CertificateTrustEvaluator();
        trustEvaluator.open(parameters);
        log.debug("信任评估器初始化完成");

        // 初始化威胁检测器
        threatDetector = new CertificateThreatDetector();
        threatDetector.open(parameters);
        log.debug("威胁检测器初始化完成");

        // 初始化证书分类器
        certificateClassifier = new CertificateClassifier();
        log.debug("证书分类器初始化完成");

        // 初始化风险评分器
        riskScorer = new CertificateRiskScorer();
        // riskScorer.open(parameters); // 如果需要的话
        log.debug("风险评分器初始化完成");
    }

    /**
     * 带错误处理的执行方法
     * 确保单个组件的失败不会影响整个分析流程
     */
    private X509Certificate executeWithErrorHandling(
            AnalysisFunction function, 
            String componentName, 
            X509Certificate certificate) {
        try {
            return function.execute();
        } catch (Exception e) {
            log.error("{}组件执行失败，证书ID: {}, 错误: {}", 
                componentName, certificate.getDerSha1(), e.getMessage(), e);
            // 返回原证书对象，继续后续分析
            return certificate;
        }
    }

    /**
     * 分析函数接口
     */
    @FunctionalInterface
    private interface AnalysisFunction {
        X509Certificate execute() throws Exception;
    }

    @Override
    public void close() throws Exception {
        log.info("证书分析引擎关闭开始");

        // 关闭需要关闭的分析组件
        if (trustEvaluator != null) {
            try {
                trustEvaluator.close();
                log.debug("信任评估器关闭成功");
            } catch (Exception e) {
                log.error("信任评估器关闭失败: {}", e.getMessage(), e);
            }
        }

        if (threatDetector != null) {
            try {
                threatDetector.close();
                log.debug("威胁检测器关闭成功");
            } catch (Exception e) {
                log.error("威胁检测器关闭失败: {}", e.getMessage(), e);
            }
        }

        super.close();
        log.info("证书分析引擎关闭完成");
    }


}
