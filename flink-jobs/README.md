# NTA Platform 3.0 - Flink 流处理作业

本目录包含 NTA Platform 3.0 的所有 Apache Flink 流处理作业，基于 **Flink 1.20.1** 构建，用于实时处理网络流量数据、证书分析、图谱构建和威胁检测。

## 项目概述

NTA Platform 的 Flink 作业采用模块化设计，每个作业专注于特定的数据处理任务，通过 Kafka 进行数据交换，支持水平扩展和高可用部署。

### 技术栈

- **Apache Flink**: 1.20.1 (流处理引擎)
- **Scala**: 2.12 (二进制版本)
- **Java**: 17 (编程语言)
- **Apache Kafka**: 3.9.0 (消息队列)
- **Apache Doris**: 2.1.7 (数据仓库)
- **Nebula Graph**: 3.8.0 (图数据库)
- **Elasticsearch**: 7.17.14 (搜索引擎)
- **Maven**: 3.6+ (构建工具)

## 模块结构

```
flink-jobs/
├── common/                         # 共享核心库
├── traffic-etl-processor/          # 流量ETL处理器
├── certificate-analyzer/           # 证书分析器
├── alarm-processor/                # 告警处理器
├── threat-detector/                # 威胁检测器
└── pom.xml                        # 父级 Maven 配置
```

## 作业详情

### 1. Flink Common (共享核心库)

**模块**: `common`
**描述**: 提供所有 Flink 作业共享的通用工具类、数据结构和配置

**主要功能**:
- Protobuf 消息定义和序列化工具
- 通用数据结构和枚举类型
- 配置管理和工具类
- 公共的序列化和反序列化器

**关键组件**:
- `SpecProtocolEnum`: 协议类型枚举
- 通用工具类和配置管理
- Protobuf 消息定义

### 2. Traffic ETL Processor (流量ETL处理器)

**模块**: `traffic-etl-processor`
**主类**: `com.geeksec.nta.trafficetl.job.TrafficEtlPipeline`
**描述**: 实时流量数据 ETL 处理，支持多协议数据解析和数据仓库写入

**主要功能**:
- 多协议数据解析 (DNS、HTTP、SSL、SSH 等)
- 实时数据清洗和标准化
- 数据富化和地理位置信息补充
- 数据分层处理 (ODS、DWD、DIM)
- 写入 Apache Doris 数据仓库

**数据流架构**:
```
Kafka Source → 协议解析 → 数据富化 → 分层处理 → Doris Sink
```

**支持的协议**:
- DNS 查询和响应
- HTTP 请求和响应
- SSL/TLS 握手信息
- SSH 连接信息
- 其他网络协议

### 3. Certificate Analyzer (证书分析器)

**模块**: `certificate-analyzer`
**主类**: `com.geeksec.nta.certificate.app.CertificateAnalysisPipeline`
**描述**: SSL/TLS 证书实时分析和安全检测

**主要功能**:
- 证书解析和验证
- 证书链完整性检查
- 恶意证书检测 (免费证书、自签名、伪造证书等)
- 证书标签化和评分
- 证书关联域名分析

**检测模型** (基于机器学习):
- 免费证书检测 (随机森林)
- 代理证书检测 (LightGBM)
- CDN 证书识别
- 哈希碰撞检测
- 证书链异常检测

**输出标签**:
- CA 证书、CDN 证书、免费证书
- 过期证书、伪造证书、自签名证书
- 安全风险评级和威胁标签

### 4. Alarm Processor (告警处理器)

**模块**: `alarm-processor`
**主类**: `com.geeksec.alarmprocessor.job.AlarmProcessorJob`
**描述**: 统一告警处理作业，负责告警去重、格式化、攻击链分析和输出

**主要功能**:
- 多源告警事件统一接收和处理
- 告警去重和聚合
- 告警格式化和标准化
- 攻击链分析和关联
- 多种输出方式支持

**处理流程**:
```
Kafka 告警源 → 去重处理 → 格式化 → 攻击链分析 → 多路输出
```

**输出目标**:
- **Doris**: 告警数据持久化存储
- **Kafka**: 告警通知和下游处理
- **PostgreSQL**: 告警元数据管理

### 5. Threat Detector (威胁检测器)

**模块**: `threat-detector`
**主类**: `com.geeksec.nta.pipeline.ThreatDetectionPipeline`
**描述**: 实时网络威胁检测和安全分析

**主要功能**:
- 恶意流量检测
- 隐蔽信道识别
- Webshell 检测
- C2 通信检测
- 异常行为分析

**检测类型**:
- **恶意工具**: CobaltStrike、Metasploit、各类 Webshell
- **隐蔽信道**: HTTP、DNS、ICMP、SSL 隧道
- **APT 活动**: 高级持续威胁检测
- **异常连接**: 端口扫描、暴力破解

**威胁模型**:
- 基于规则的检测引擎
- 机器学习异常检测
- 行为模式分析
- 威胁情报关联

## 构建和部署

### 环境要求

- **JDK**: 17+
- **Maven**: 3.6+
- **Apache Flink**: 1.20.1
- **内存**: 最少 8GB，推荐 16GB+

### 构建步骤

1. **编译所有模块**
   ```bash
   cd flink-jobs
   mvn clean compile
   ```

2. **运行测试**
   ```bash
   mvn test
   ```

3. **打包应用**
   ```bash
   mvn clean package
   ```

4. **构建 Docker 镜像**
   ```bash
   mvn docker:build
   ```

### 本地运行

1. **启动单个作业**
   ```bash
   # 流量ETL处理器
   java -cp traffic-etl-processor/target/traffic-etl-processor-3.0.0-SNAPSHOT.jar \
        com.geeksec.nta.trafficetl.job.TrafficEtlPipeline

   # 证书分析器
   java -cp certificate-analyzer/target/certificate-analyzer-3.0.0-SNAPSHOT.jar \
        com.geeksec.nta.certificate.app.CertificateAnalysisPipeline
   ```

2. **使用 Flink CLI 提交作业**
   ```bash
   $FLINK_HOME/bin/flink run \
     --class com.geeksec.nta.trafficetl.job.TrafficEtlPipeline \
     traffic-etl-processor/target/traffic-etl-processor-3.0.0-SNAPSHOT.jar
   ```

## 配置说明

### 通用配置

所有作业都支持以下配置参数：

| 参数名 | 默认值 | 描述 |
|--------|--------|------|
| `kafka.bootstrap.servers` | `localhost:9092` | Kafka 集群地址 |
| `kafka.group.id` | `nta-flink-jobs` | 消费者组 ID |
| `flink.parallelism.default` | `4` | 默认并行度 |
| `flink.checkpoint.interval` | `60000` | Checkpoint 间隔 (ms) |
| `flink.state.backend` | `rocksdb` | 状态后端类型 |

### 作业特定配置

#### 数据仓库处理器
```properties
# Doris 连接配置
doris.fenodes=localhost:8030
doris.username=root
doris.password=
doris.database=nta

# 数据处理配置
data.enrichment.enabled=true
geo.database.path=/path/to/GeoLite2-City.mmdb
```

#### 证书分析器
```properties
# 证书处理配置
cert.analysis.models.enabled=true
cert.chain.validation.enabled=true
cert.scoring.enabled=true

# 输出配置
cert.output.elasticsearch.enabled=true
cert.output.minio.enabled=true
```

#### 图谱构建器
```properties
# Nebula Graph 配置
nebula.graph.hosts=localhost:9669
nebula.graph.username=root
nebula.graph.password=nebula
nebula.graph.space=nta_graph

# 图处理配置
graph.vertex.batch.size=1000
graph.edge.batch.size=1000
```

#### 威胁检测器
```properties
# 威胁检测配置
threat.detection.models.enabled=true
threat.detection.rules.enabled=true
threat.detection.ml.enabled=true

# 告警配置
alert.output.elasticsearch.enabled=true
alert.output.kafka.enabled=true
```

## 监控和运维

### 性能监控

1. **Flink Web UI**: 访问 `http://localhost:8081` 查看作业状态
2. **JVM 监控**: 使用 JConsole 或 VisualVM 监控 JVM 性能
3. **自定义指标**: 通过 Flink Metrics 系统暴露业务指标

### 日志管理

```bash
# 查看作业日志
tail -f $FLINK_HOME/log/flink-*-jobmanager-*.log
tail -f $FLINK_HOME/log/flink-*-taskmanager-*.log

# 查看特定作业日志
grep "TrafficEtlPipeline" $FLINK_HOME/log/*.log
```

### 故障排查

1. **作业失败重启**: 检查 Checkpoint 和 Savepoint
2. **背压问题**: 调整并行度和资源配置
3. **内存溢出**: 增加 TaskManager 内存或优化数据结构
4. **数据倾斜**: 重新设计分区策略

## 开发指南

### 添加新的处理逻辑

1. **继承基础类**: 使用 shared-core 中的通用组件
2. **实现接口**: 遵循现有的数据处理接口
3. **配置管理**: 使用统一的配置管理机制
4. **测试覆盖**: 编写单元测试和集成测试

### 代码规范

- 使用 Java 17 语言特性
- 遵循 Google Java Style Guide
- 添加详细的 Javadoc 注释
- 使用 SLF4J 进行日志记录

## 版本信息

- **当前版本**: 3.0.0-SNAPSHOT
- **Flink 版本**: 1.20.1
- **Java 版本**: 17
- **构建工具**: Maven 3.6+

## 依赖管理

### 核心依赖

| 组件 | 版本 | 用途 |
|------|------|------|
| flink-core | 1.20.1 | Flink 核心库 |
| flink-streaming-java | 1.20.1 | 流处理 API |
| flink-connector-kafka | 3.4.0-1.20 | Kafka 连接器 |
| flink-connector-jdbc | 3.3.0-1.20 | JDBC 连接器 |
| flink-connector-elasticsearch7 | 3.1.0-1.20 | Elasticsearch 连接器 |
| flink-doris-connector-1.20 | 25.1.0 | Doris 连接器 |
| nebula-flink-connector | 3.8.0 | Nebula Graph 连接器 |

### 工具库依赖

| 组件 | 版本 | 用途 |
|------|------|------|
| jackson-databind | ******** | JSON 处理 |
| geoip2 | 4.3.0 | 地理位置查询 |
| opennlp-tools | 1.9.4 | 自然语言处理 |
| httpclient | 4.5.10 | HTTP 客户端 |
| logback-classic | 1.2.11 | 日志框架 |

## 部署架构

### Kubernetes 部署

使用 Flink Kubernetes Operator 进行作业管理：

```yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: traffic-etl-processor
spec:
  image: hb.gs.lan/nta/traffic-etl-processor:3.0.0-SNAPSHOT
  flinkVersion: v1_20
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: s3://flink-checkpoints/
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "4096m"
      cpu: 2
  job:
    jarURI: local:///opt/flink/usrlib/traffic-etl-processor.jar
    parallelism: 4
    upgradeMode: savepoint
```

### Docker 部署

每个作业都提供了 Dockerfile：

```dockerfile
FROM flink:1.20.1-scala_2.12-java17

# 复制作业 JAR 文件
COPY target/traffic-etl-processor-3.0.0-SNAPSHOT.jar /opt/flink/usrlib/

# 复制依赖库
COPY target/lib/*.jar /opt/flink/lib/

# 设置环境变量
ENV FLINK_PROPERTIES="jobmanager.rpc.address: jobmanager"

# 暴露端口
EXPOSE 6123 8081
```

## 性能调优

### JVM 参数优化

```bash
# JobManager JVM 参数
-Xms2g -Xmx2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError

# TaskManager JVM 参数
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
```

### Flink 配置优化

```yaml
# 并行度配置
parallelism.default: 4
taskmanager.numberOfTaskSlots: 2

# 内存配置
taskmanager.memory.process.size: 4096m
taskmanager.memory.flink.size: 3072m
taskmanager.memory.managed.fraction: 0.4

# Checkpoint 配置
execution.checkpointing.interval: 60s
execution.checkpointing.timeout: 10min
execution.checkpointing.max-concurrent-checkpoints: 1

# 状态后端配置
state.backend: rocksdb
state.backend.rocksdb.predefined-options: SPINNING_DISK_OPTIMIZED
state.backend.incremental: true
```

### 网络配置优化

```yaml
# 网络缓冲区配置
taskmanager.memory.network.fraction: 0.1
taskmanager.memory.network.min: 64mb
taskmanager.memory.network.max: 1gb

# 网络传输配置
taskmanager.network.netty.num-arenas: 2
taskmanager.network.netty.server.numThreads: 2
taskmanager.network.netty.client.numThreads: 2
```

## 数据流设计

### 数据仓库处理器流程

```mermaid
graph TD
    A[Kafka Source] --> B[协议识别]
    B --> C[DNS 处理]
    B --> D[HTTP 处理]
    B --> E[SSL 处理]
    C --> F[数据富化]
    D --> F
    E --> F
    F --> G[ODS 层写入]
    G --> H[DWD 层处理]
    H --> I[DIM 层处理]
    I --> J[Doris Sink]
```

### 证书分析器流程

```mermaid
graph TD
    A[证书 Kafka Source] --> B[证书解析]
    B --> C[基础验证]
    C --> D[证书链验证]
    D --> E[标签检测]
    E --> F[ML 模型分析]
    F --> G[评分计算]
    G --> H[ES 写入]
    G --> I[MinIO 存储]
```

### 图谱构建器流程

```mermaid
graph TD
    A[多源数据流] --> B[实体提取]
    B --> C[IP 实体]
    B --> D[域名实体]
    B --> E[URL 实体]
    C --> F[关系建模]
    D --> F
    E --> F
    F --> G[图数据转换]
    G --> H[Nebula Graph]
```

## 测试指南

### 单元测试

```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl traffic-etl-processor

# 运行特定测试类
mvn test -Dtest=TrafficEtlPipelineTest
```

### 集成测试

```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
mvn verify -Pintegration-test

# 清理测试环境
docker-compose -f docker-compose.test.yml down
```

### 性能测试

```bash
# 使用 Flink 自带的性能测试工具
$FLINK_HOME/bin/flink run \
  --class org.apache.flink.streaming.tests.DataStreamAllroundTestProgram \
  target/traffic-etl-processor-3.0.0-SNAPSHOT.jar \
  --test.semantics exactly-once \
  --test.sink.checkpoint.enabled true
```

## 故障排查手册

### 常见问题

#### 1. 作业启动失败

**症状**: 作业无法启动或立即失败
**可能原因**:
- 依赖 JAR 文件缺失
- 配置参数错误
- 资源不足

**解决方案**:
```bash
# 检查 JAR 文件
ls -la target/*.jar

# 验证配置
flink run --dry-run target/your-job.jar

# 检查资源使用
kubectl top pods -n flink
```

#### 2. 背压问题

**症状**: 作业处理延迟增加，吞吐量下降
**可能原因**:
- 下游处理能力不足
- 数据倾斜
- 网络瓶颈

**解决方案**:
```bash
# 检查背压指标
curl http://jobmanager:8081/jobs/{job-id}/vertices/{vertex-id}/backpressure

# 调整并行度
flink modify {job-id} --parallelism 8

# 重新分区
dataStream.rebalance().map(...)
```

#### 3. Checkpoint 失败

**症状**: Checkpoint 超时或失败
**可能原因**:
- 状态过大
- 存储性能问题
- 网络问题

**解决方案**:
```yaml
# 调整 Checkpoint 配置
execution.checkpointing.timeout: 20min
execution.checkpointing.max-concurrent-checkpoints: 1
state.backend.incremental: true
```

### 监控指标

#### 关键性能指标

| 指标名称 | 描述 | 正常范围 |
|---------|------|---------|
| `numRecordsInPerSecond` | 每秒输入记录数 | > 1000 |
| `numRecordsOutPerSecond` | 每秒输出记录数 | > 1000 |
| `currentInputWatermark` | 当前水位线 | 接近当前时间 |
| `lastCheckpointDuration` | 上次 Checkpoint 耗时 | < 30s |
| `backpressure` | 背压级别 | LOW |

#### 告警规则

```yaml
# Prometheus 告警规则
groups:
- name: flink-jobs
  rules:
  - alert: FlinkJobDown
    expr: up{job="flink-jobmanager"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Flink job is down"

  - alert: FlinkCheckpointFailed
    expr: flink_jobmanager_job_lastCheckpointDuration > 300000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Flink checkpoint taking too long"
```

## 最佳实践

### 代码开发

1. **状态管理**: 使用 Keyed State 而非 Operator State
2. **序列化**: 优先使用 Avro 或 Protobuf
3. **时间语义**: 明确使用 Event Time 或 Processing Time
4. **容错设计**: 实现幂等操作和状态恢复

### 运维部署

1. **资源规划**: 根据数据量合理配置内存和 CPU
2. **监控告警**: 设置完整的监控和告警体系
3. **版本管理**: 使用 Savepoint 进行无损升级
4. **备份策略**: 定期备份 Checkpoint 和配置文件

## 联系方式

- **技术支持**: <EMAIL>
- **问题反馈**: 请通过 GitHub Issues 提交
- **文档更新**: 请提交 Pull Request

---

**注意**: 本文档基于 NTA Platform 3.0 版本编写，不同版本可能存在差异。部署前请确认版本兼容性。
