package com.geeksec.certificate.analyzer.operator.analysis.signature;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.util.cert.CertificateUtils;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书仓库
 * <p>
 * 负责管理和查询证书库，包括系统证书库和用户证书库。
 * 提供证书查询、验证等功能。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateRepository {

    // 知识库客户端
    private final KnowledgeBaseClient knowledgeBaseClient;

    // 证书缓存 (SHA1 -> 证书对象)
    private final Map<String, X509Certificate> certificateCache = new HashMap<>();

    // 主题DN和密钥ID索引 (SubjectDN+KeyID -> SHA1列表)
    private final Map<String, List<String>> subjectKeyIdIndex = new HashMap<>();

    /**
     * 构造函数
     *
     * @param knowledgeBaseUrl 知识库服务URL
     */
    public CertificateRepository(String knowledgeBaseUrl) {
        this.knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);
        log.info("证书仓库初始化完成，知识库服务地址: {}", knowledgeBaseUrl);
    }

    /**
     * 默认构造函数（用于测试）
     */
    public CertificateRepository() {
        this("http://localhost:8080");
    }
    
    /**
     * 检查证书是否是系统可信CA证书
     *
     * @param sha1 证书SHA1哈希值
     * @return 是否是系统可信CA证书
     */
    public boolean isTrustedCaCertificate(String sha1) {
        if (sha1 == null || sha1.trim().isEmpty()) {
            return false;
        }
        return knowledgeBaseClient.isTrustedCaCertificate(sha1);
    }
    
    /**
     * 根据主题DN和密钥ID查找证书
     * 
     * @param subjectDn 主题DN
     * @param subjectKeyId 主题密钥ID
     * @return 匹配的证书列表
     */
    public List<X509Certificate> findCertificatesBySubjectAndKeyId(String subjectDn, String subjectKeyId) {
        List<X509Certificate> result = new ArrayList<>();
        
        try {
            // 构建索引键
            String indexKey = buildSubjectKeyIdIndexKey(subjectDn, subjectKeyId);
            
            // 查询索引
            List<String> sha1List = subjectKeyIdIndex.get(indexKey);
            if (sha1List != null) {
                for (String sha1 : sha1List) {
                    X509Certificate cert = getCertificateBySha1(sha1);
                    if (cert != null) {
                        result.add(cert);
                    }
                }
            }
            
            // 如果本地缓存没有找到，则查询外部存储
            if (result.isEmpty()) {
                result.addAll(queryExternalCertificateStore(subjectDn, subjectKeyId));
            }
        } catch (Exception e) {
            log.error("查询证书失败", e);
        }
        
        return result;
    }
    
    /**
     * 根据SHA1哈希值获取证书
     * 
     * @param sha1 证书SHA1哈希值
     * @return 证书对象，如果不存在则返回null
     */
    public X509Certificate getCertificateBySha1(String sha1) {
        // 先从缓存中查找
        X509Certificate cert = certificateCache.get(sha1);
        if (cert != null) {
            return cert;
        }
        
        // 如果缓存中没有，则查询外部存储
        try {
            cert = queryExternalCertificateStore(sha1);
            if (cert != null) {
                // 添加到缓存
                addToCache(cert);
            }
        } catch (Exception e) {
            log.error("查询证书失败: {}", sha1, e);
        }
        
        return cert;
    }
    
    /**
     * 添加证书到缓存
     * 
     * @param certificate 证书对象
     */
    public void addToCache(X509Certificate certificate) {
        if (certificate == null || certificate.getDerSha1() == null) {
            return;
        }
        
        String sha1 = certificate.getDerSha1();
        
        // 添加到证书缓存
        certificateCache.put(sha1, certificate);
        
        // 添加到主题DN和密钥ID索引
        String subjectDn = certificate.getSubjectDn();
        String subjectKeyId = CertificateUtils.getSubjectKeyIdentifier(certificate);
        
        if (subjectDn != null && subjectKeyId != null) {
            String indexKey = buildSubjectKeyIdIndexKey(subjectDn, subjectKeyId);
            subjectKeyIdIndex.computeIfAbsent(indexKey, k -> new ArrayList<>()).add(sha1);
        }
    }
    
    /**
     * 构建主题DN和密钥ID的索引键
     * 
     * @param subjectDn 主题DN
     * @param subjectKeyId 主题密钥ID
     * @return 索引键
     */
    private String buildSubjectKeyIdIndexKey(String subjectDn, String subjectKeyId) {
        return subjectDn + "|" + subjectKeyId;
    }
    
    /**
     * 查询外部证书存储
     * 
     * @param sha1 证书SHA1哈希值
     * @return 证书对象，如果不存在则返回null
     */
    private X509Certificate queryExternalCertificateStore(String sha1) {
        // TODO: 实现外部存储查询逻辑，如查询数据库、ES等
        // 这里是一个简化的实现，实际项目中需要根据具体的存储方式进行查询
        log.debug("查询外部证书存储: {}", sha1);
        return null;
    }
    
    /**
     * 查询外部证书存储
     * 
     * @param subjectDn 主题DN
     * @param subjectKeyId 主题密钥ID
     * @return 匹配的证书列表
     */
    private List<X509Certificate> queryExternalCertificateStore(String subjectDn, String subjectKeyId) {
        // TODO: 实现外部存储查询逻辑，如查询数据库、ES等
        // 这里是一个简化的实现，实际项目中需要根据具体的存储方式进行查询
        log.debug("查询外部证书存储: {} - {}", subjectDn, subjectKeyId);
        return new ArrayList<>();
    }
}
