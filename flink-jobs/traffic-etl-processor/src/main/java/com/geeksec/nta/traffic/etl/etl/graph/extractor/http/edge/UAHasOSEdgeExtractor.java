package com.geeksec.nta.traffic.etl.etl.graph.extractor.http.edge;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.http.HttpEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import nl.basjes.parse.useragent.UserAgent;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class UAHasOSEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.UA_HAS_OS_TAG;
    }

    /**
     * UA包含系统信息 (UA -> OS)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        try {
            String httpClientStr = value.getField(FieldConstants.FIELD_HTTP_CLIENT_KV).toString();
            Map<String, Object> httpClient = mapper.readValue(httpClientStr, new TypeReference<Map<String, Object>>() {});
            // 获取UA信息
            UserAgent agent = HttpEdgeExtractor.extractUA(httpClient);
            String osName = agent.getValue("OperatingSystemName"); // 操作系统
            String deviceName = agent.getValue("DeviceName"); // 设备名称
            String applicationName = agent.getValue("AgentName"); // 浏览器/应用名称
            String userAgentKey = applicationName + "_" + osName + "_" + deviceName;
            return List.of(Row.of(userAgentKey, osName,
                    0 // rank 暂定0
            ));
        } catch (JsonProcessingException e) {
            log.error("解析HTTP信息JSON失败", e);
        }
        return Collections.emptyList();
    }
}
