package com.geeksec.session.threat.detector.state.redis;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 威胁检测系统统一状态管理器
 * 整合所有状态管理功能，提供统一的接口和管理能力
 * 
 * <AUTHOR>
 */
@Slf4j
public class ThreatDetectorStateManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Redis状态管理器
     */
    private final RedisStateManager redisStateManager;
    
    /**
     * 攻击链状态管理器
     */
    private final AttackChainStateManager attackChainStateManager;
    
    /**
     * 告警状态管理器
     */
    private final AlarmStateManager alarmStateManager;
    
    /**
     * 检测器状态管理器
     */
    private final DetectorStateManager detectorStateManager;
    
    /**
     * 会话状态管理器
     */
    private final SessionStateManager sessionStateManager;
    
    /**
     * 定时清理任务执行器
     */
    private transient ScheduledExecutorService cleanupExecutor;
    
    /**
     * 是否启用自动清理
     */
    private final boolean autoCleanupEnabled;
    
    /**
     * 清理间隔（小时）
     */
    private final int cleanupIntervalHours;
    
    /**
     * 单例实例
     */
    private static volatile ThreatDetectorStateManager instance = null;
    
    /**
     * 私有构造函数
     * 
     * @param autoCleanupEnabled 是否启用自动清理
     * @param cleanupIntervalHours 清理间隔（小时）
     */
    private ThreatDetectorStateManager(boolean autoCleanupEnabled, int cleanupIntervalHours) {
        this.redisStateManager = RedisStateManager.getInstance();
        this.attackChainStateManager = new AttackChainStateManager();
        this.alarmStateManager = new AlarmStateManager();
        this.detectorStateManager = new DetectorStateManager();
        this.sessionStateManager = new SessionStateManager();
        this.autoCleanupEnabled = autoCleanupEnabled;
        this.cleanupIntervalHours = cleanupIntervalHours;
        
        if (autoCleanupEnabled) {
            initializeCleanupScheduler();
        }
        
        log.info("威胁检测系统状态管理器初始化完成，自动清理: {}, 清理间隔: {}小时", 
                autoCleanupEnabled, cleanupIntervalHours);
    }
    
    /**
     * 获取单例实例
     * 
     * @return 状态管理器实例
     */
    public static ThreatDetectorStateManager getInstance() {
        if (instance == null) {
            synchronized (ThreatDetectorStateManager.class) {
                if (instance == null) {
                    instance = new ThreatDetectorStateManager(true, 6); // 默认6小时清理一次
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取自定义配置的实例
     * 
     * @param autoCleanupEnabled 是否启用自动清理
     * @param cleanupIntervalHours 清理间隔（小时）
     * @return 状态管理器实例
     */
    public static ThreatDetectorStateManager getInstance(boolean autoCleanupEnabled, int cleanupIntervalHours) {
        if (instance == null) {
            synchronized (ThreatDetectorStateManager.class) {
                if (instance == null) {
                    instance = new ThreatDetectorStateManager(autoCleanupEnabled, cleanupIntervalHours);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取Redis状态管理器
     * 
     * @return Redis状态管理器
     */
    public RedisStateManager getRedisStateManager() {
        return redisStateManager;
    }
    
    /**
     * 获取攻击链状态管理器
     * 
     * @return 攻击链状态管理器
     */
    public AttackChainStateManager getAttackChainStateManager() {
        return attackChainStateManager;
    }
    
    /**
     * 获取告警状态管理器
     * 
     * @return 告警状态管理器
     */
    public AlarmStateManager getAlarmStateManager() {
        return alarmStateManager;
    }
    
    /**
     * 获取检测器状态管理器
     * 
     * @return 检测器状态管理器
     */
    public DetectorStateManager getDetectorStateManager() {
        return detectorStateManager;
    }
    
    /**
     * 获取会话状态管理器
     * 
     * @return 会话状态管理器
     */
    public SessionStateManager getSessionStateManager() {
        return sessionStateManager;
    }
    
    /**
     * 获取系统整体状态概览
     * 
     * @return 系统状态概览
     */
    public SystemStateOverview getSystemStateOverview() {
        try {
            // Redis状态
            RedisStateManager.RedisStatistics redisStats = redisStateManager.getStatistics();
            
            // 攻击链状态
            AttackChainStateManager.AttackChainStatistics attackChainStats = 
                    attackChainStateManager.getStatistics();
            
            // 告警状态
            AlarmStateManager.AlarmStatistics alarmStats = 
                    alarmStateManager.getAlarmStatistics(24); // 最近24小时
            
            // 检测器状态
            DetectorStateManager.DetectorOverview detectorOverview = 
                    detectorStateManager.getDetectorOverview();
            
            // 会话状态
            SessionStateManager.SessionStatistics sessionStats = 
                    sessionStateManager.getSessionStatistics(24); // 最近24小时
            
            return SystemStateOverview.builder()
                    .redisStatistics(redisStats)
                    .attackChainStatistics(attackChainStats)
                    .alarmStatistics(alarmStats)
                    .detectorOverview(detectorOverview)
                    .sessionStatistics(sessionStats)
                    .overviewTime(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取系统状态概览失败", e);
            return SystemStateOverview.builder()
                    .overviewTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 执行全系统数据清理
     * 
     * @param maxAgeHours 最大年龄（小时）
     * @return 清理统计信息
     */
    public CleanupStatistics performSystemCleanup(int maxAgeHours) {
        log.info("开始执行系统数据清理，最大年龄: {}小时", maxAgeHours);
        
        long startTime = System.currentTimeMillis();
        CleanupStatistics stats = new CleanupStatistics();
        
        try {
            // 清理攻击链数据
            long attackChainCleaned = attackChainStateManager.cleanupExpiredAttackChains(maxAgeHours);
            stats.setAttackChainsCleaned(attackChainCleaned);
            
            // 清理告警数据
            long alarmsCleaned = alarmStateManager.cleanupExpiredAlarms(maxAgeHours);
            stats.setAlarmsCleaned(alarmsCleaned);
            
            // 清理检测器数据
            long detectorDataCleaned = detectorStateManager.cleanupExpiredDetectorData(maxAgeHours);
            stats.setDetectorDataCleaned(detectorDataCleaned);
            
            // 清理会话数据
            long sessionsCleaned = sessionStateManager.cleanupExpiredSessions(maxAgeHours);
            stats.setSessionsCleaned(sessionsCleaned);
            
            // 清理通用过期数据
            long generalDataCleaned = cleanupGeneralExpiredData();
            stats.setGeneralDataCleaned(generalDataCleaned);
            
            long endTime = System.currentTimeMillis();
            stats.setCleanupDurationMs(endTime - startTime);
            stats.setCleanupTime(LocalDateTime.now());
            stats.setSuccess(true);
            
            log.info("系统数据清理完成: 攻击链={}, 告警={}, 检测器数据={}, 会话={}, 通用数据={}, 耗时={}ms",
                    attackChainCleaned, alarmsCleaned, detectorDataCleaned, sessionsCleaned, 
                    generalDataCleaned, stats.getCleanupDurationMs());
            
        } catch (Exception e) {
            stats.setSuccess(false);
            stats.setErrorMessage(e.getMessage());
            log.error("系统数据清理失败", e);
        }
        
        return stats;
    }
    
    /**
     * 获取系统健康状态
     * 
     * @return 健康状态
     */
    public SystemHealthStatus getSystemHealthStatus() {
        try {
            SystemHealthStatus.SystemHealthStatusBuilder builder = SystemHealthStatus.builder();
            
            // Redis健康状态
            try {
                String redisInfo = redisStateManager.info();
                builder.redisHealthy(redisInfo != null && redisInfo.contains("redis_version"));
            } catch (Exception e) {
                builder.redisHealthy(false);
                builder.redisError(e.getMessage());
            }
            
            // 检测器健康状态
            DetectorStateManager.DetectorOverview detectorOverview = detectorStateManager.getDetectorOverview();
            int totalDetectors = detectorOverview.getTotalDetectors();
            int runningDetectors = detectorOverview.getRunningDetectors();
            int errorDetectors = detectorOverview.getErrorDetectors();
            
            builder.totalDetectors(totalDetectors);
            builder.runningDetectors(runningDetectors);
            builder.errorDetectors(errorDetectors);
            
            // 计算检测器健康度
            if (totalDetectors > 0) {
                double detectorHealthRatio = (double) runningDetectors / totalDetectors;
                builder.detectorHealthy(detectorHealthRatio >= 0.8); // 80%以上运行正常认为健康
                builder.detectorHealthRatio(detectorHealthRatio);
            } else {
                builder.detectorHealthy(true);
                builder.detectorHealthRatio(1.0);
            }
            
            // 系统整体健康状态
            boolean systemHealthy = builder.build().isRedisHealthy() && 
                                   builder.build().isDetectorHealthy();
            builder.systemHealthy(systemHealthy);
            builder.checkTime(LocalDateTime.now());
            
            return builder.build();
            
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return SystemHealthStatus.builder()
                    .systemHealthy(false)
                    .redisHealthy(false)
                    .detectorHealthy(false)
                    .checkTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 初始化清理调度器
     */
    private void initializeCleanupScheduler() {
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ThreatDetector-StateCleanup");
            t.setDaemon(true);
            return t;
        });
        
        // 定时执行清理任务
        cleanupExecutor.scheduleAtFixedRate(
                this::performScheduledCleanup,
                cleanupIntervalHours,
                cleanupIntervalHours,
                TimeUnit.HOURS
        );
        
        log.info("状态清理调度器已启动，清理间隔: {}小时", cleanupIntervalHours);
    }
    
    /**
     * 执行定时清理任务
     */
    private void performScheduledCleanup() {
        try {
            // 清理过期数据，保留最近48小时的数据
            CleanupStatistics stats = performSystemCleanup(48);
            
            if (stats.isSuccess()) {
                log.info("定时清理任务完成: 总清理数据={}, 耗时={}ms", 
                        stats.getTotalCleaned(), stats.getCleanupDurationMs());
            } else {
                log.error("定时清理任务失败: {}", stats.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("定时清理任务异常", e);
        }
    }
    
    /**
     * 清理通用过期数据
     * 
     * @return 清理的数据数量
     */
    private long cleanupGeneralExpiredData() {
        try {
            long totalCleaned = 0;
            
            // 清理缓存数据
            String cachePattern = RedisStateManager.KEY_PREFIX_CACHE + "*";
            totalCleaned += redisStateManager.cleanupExpiredData(cachePattern, 100);
            
            // 清理锁数据
            String lockPattern = RedisStateManager.KEY_PREFIX_LOCK + "*";
            totalCleaned += redisStateManager.cleanupExpiredData(lockPattern, 100);
            
            return totalCleaned;
            
        } catch (Exception e) {
            log.error("清理通用过期数据失败", e);
            return 0;
        }
    }
    
    /**
     * 关闭状态管理器
     */
    public void shutdown() {
        log.info("关闭威胁检测系统状态管理器");
        
        if (cleanupExecutor != null) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        redisStateManager.close();
        instance = null;
    }
    
    /**
     * 系统状态概览
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SystemStateOverview implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private RedisStateManager.RedisStatistics redisStatistics;
        private AttackChainStateManager.AttackChainStatistics attackChainStatistics;
        private AlarmStateManager.AlarmStatistics alarmStatistics;
        private DetectorStateManager.DetectorOverview detectorOverview;
        private SessionStateManager.SessionStatistics sessionStatistics;
        private LocalDateTime overviewTime;
    }
    
    /**
     * 清理统计信息
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CleanupStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private long attackChainsCleaned;
        private long alarmsCleaned;
        private long detectorDataCleaned;
        private long sessionsCleaned;
        private long generalDataCleaned;
        private long cleanupDurationMs;
        private LocalDateTime cleanupTime;
        private boolean success;
        private String errorMessage;
        
        public long getTotalCleaned() {
            return attackChainsCleaned + alarmsCleaned + detectorDataCleaned + 
                   sessionsCleaned + generalDataCleaned;
        }
    }
    
    /**
     * 系统健康状态
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SystemHealthStatus implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean systemHealthy;
        private boolean redisHealthy;
        private String redisError;
        private boolean detectorHealthy;
        private double detectorHealthRatio;
        private int totalDetectors;
        private int runningDetectors;
        private int errorDetectors;
        private LocalDateTime checkTime;
    }
}
