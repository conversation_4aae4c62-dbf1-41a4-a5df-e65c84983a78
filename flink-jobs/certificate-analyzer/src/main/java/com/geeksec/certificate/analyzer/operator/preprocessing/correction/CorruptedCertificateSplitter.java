package com.geeksec.certificate.analyzer.operator.preprocessing.correction;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.enums.CertificateSource;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.operator.analysis.ThreatLevelEvaluator;

import lombok.extern.slf4j.Slf4j;

/**
 * 错误证书纠正分流处理函数
 * 负责对错误证书进行纠正处理并按来源分流
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2023/8/7
 */
@Slf4j
public class CorruptedCertificateSplitter extends ProcessFunction<X509Certificate, X509Certificate> {

    /** 错误用户证书输出标签 */
    public static final OutputTag<X509Certificate> CORRUPTED_USER_CERT_RESULT =
        new OutputTag<>("corruptedUserCertResult", TypeInformation.of(X509Certificate.class));

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(X509Certificate cert, ProcessFunction<X509Certificate, X509Certificate>.Context context,
                             Collector<X509Certificate> collector) throws Exception {
        // 处理证书标签
        Set<CertificateLabel> labels = cert.getLabels();
        List<String> labelStrings = new ArrayList<>();
        for (CertificateLabel label : labels) {
            labelStrings.add(label.name());
        }

        // 设置威胁等级
        int threatLevel = ThreatLevelEvaluator.estimateThreatLevel(cert.getThreatScore());
        cert.setThreatLevel(String.valueOf(threatLevel));

        // 证书按来源分流
        // 用户证书（采集到的或导入的）
        CertificateSource source = cert.getSource();
        if (source != null && source.isUserCertificate()) {
            context.output(CORRUPTED_USER_CERT_RESULT, cert);
        } else {
            log.warn("发现非用户证书类型，来源值: {}", source);
            // 继续处理，输出到主流
            collector.collect(cert);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
    }
}
