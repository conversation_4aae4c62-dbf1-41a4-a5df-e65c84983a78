package com.geeksec.session.threat.detector.state.redis;

import com.geeksec.session.threat.detector.model.output.Alarm;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 告警状态管理器
 * 负责告警相关数据的Redis存储和查询，包括去重、统计等功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class AlarmStateManager implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Redis状态管理器
     */
    private final RedisStateManager redisStateManager;
    
    /**
     * 告警过期时间（秒）- 30天
     */
    private static final int ALARM_TTL = 30 * 24 * 3600;
    
    /**
     * 告警去重过期时间（秒）- 10分钟
     */
    private static final int ALARM_DEDUP_TTL = 10 * 60;
    
    /**
     * 告警统计过期时间（秒）- 7天
     */
    private static final int ALARM_STATS_TTL = 7 * 24 * 3600;
    
    /**
     * 构造函数
     */
    public AlarmStateManager() {
        this.redisStateManager = RedisStateManager.getInstance();
    }
    
    /**
     * 保存告警
     * 
     * @param alarm 告警信息
     */
    public void saveAlarm(Alarm alarm) {
        if (alarm == null || alarm.getAlarmId() == null) {
            return;
        }
        
        try {
            String alarmKey = buildAlarmKey(alarm.getAlarmId());
            
            // 保存告警基本信息
            redisStateManager.setObject(alarmKey, alarm, ALARM_TTL);
            
            // 更新告警索引
            updateAlarmIndexes(alarm);
            
            // 更新告警统计
            updateAlarmStatistics(alarm);
            
            log.debug("告警状态已保存: {}", alarm.getAlarmId());
            
        } catch (Exception e) {
            log.error("保存告警状态失败: {}", alarm.getAlarmId(), e);
        }
    }
    
    /**
     * 获取告警
     * 
     * @param alarmId 告警ID
     * @return 告警信息
     */
    public Alarm getAlarm(String alarmId) {
        if (alarmId == null) {
            return null;
        }
        
        try {
            String alarmKey = buildAlarmKey(alarmId);
            return redisStateManager.getObject(alarmKey, Alarm.class);
        } catch (Exception e) {
            log.error("获取告警状态失败: {}", alarmId, e);
            return null;
        }
    }
    
    /**
     * 检查告警是否重复
     * 
     * @param alarm 告警信息
     * @return 是否重复
     */
    public boolean isDuplicateAlarm(Alarm alarm) {
        if (alarm == null) {
            return false;
        }
        
        try {
            String dedupKey = buildAlarmDedupKey(alarm);
            boolean exists = redisStateManager.exists(dedupKey);
            
            if (!exists) {
                // 设置去重标记
                redisStateManager.setString(dedupKey, "1", ALARM_DEDUP_TTL);
            }
            
            return exists;
            
        } catch (Exception e) {
            log.error("检查告警重复失败: {}", alarm.getAlarmId(), e);
            return false;
        }
    }
    
    /**
     * 根据威胁类型查询告警
     * 
     * @param threatType 威胁类型
     * @param limit 限制数量
     * @return 告警ID列表
     */
    public List<String> getAlarmsByThreatType(String threatType, int limit) {
        if (threatType == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:threat_type:" + threatType;
            Set<String> alarmIds = redisStateManager.smembers(indexKey);
            
            return alarmIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据威胁类型查询告警失败: {}", threatType, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据源IP查询告警
     * 
     * @param srcIp 源IP
     * @param limit 限制数量
     * @return 告警ID列表
     */
    public List<String> getAlarmsBySrcIp(String srcIp, int limit) {
        if (srcIp == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:src_ip:" + srcIp;
            Set<String> alarmIds = redisStateManager.smembers(indexKey);
            
            return alarmIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据源IP查询告警失败: {}", srcIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据目标IP查询告警
     * 
     * @param dstIp 目标IP
     * @param limit 限制数量
     * @return 告警ID列表
     */
    public List<String> getAlarmsByDstIp(String dstIp, int limit) {
        if (dstIp == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:dst_ip:" + dstIp;
            Set<String> alarmIds = redisStateManager.smembers(indexKey);
            
            return alarmIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据目标IP查询告警失败: {}", dstIp, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据检测器查询告警
     * 
     * @param detectorName 检测器名称
     * @param limit 限制数量
     * @return 告警ID列表
     */
    public List<String> getAlarmsByDetector(String detectorName, int limit) {
        if (detectorName == null) {
            return new ArrayList<>();
        }
        
        try {
            String indexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:detector:" + detectorName;
            Set<String> alarmIds = redisStateManager.smembers(indexKey);
            
            return alarmIds.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("根据检测器查询告警失败: {}", detectorName, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据时间范围查询告警
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 告警ID列表
     */
    public List<String> getAlarmsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return new ArrayList<>();
        }
        
        try {
            List<String> allAlarmIds = new ArrayList<>();
            
            // 按小时查询索引
            LocalDateTime current = startTime.withMinute(0).withSecond(0).withNano(0);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
            
            while (!current.isAfter(endTime)) {
                String hourKey = current.format(formatter);
                String indexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:hour:" + hourKey;
                Set<String> hourAlarmIds = redisStateManager.smembers(indexKey);
                allAlarmIds.addAll(hourAlarmIds);
                current = current.plusHours(1);
            }
            
            return allAlarmIds.stream().distinct().collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("根据时间范围查询告警失败: {} - {}", startTime, endTime, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取告警统计信息
     * 
     * @param timeRange 时间范围（小时）
     * @return 统计信息
     */
    public AlarmStatistics getAlarmStatistics(int timeRange) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(timeRange);
            
            // 获取时间范围内的告警
            List<String> alarmIds = getAlarmsByTimeRange(startTime, endTime);
            
            // 统计各种维度
            Map<String, Integer> threatTypeStats = new HashMap<>();
            Map<String, Integer> detectorStats = new HashMap<>();
            Map<String, Integer> hourlyStats = new HashMap<>();
            
            DateTimeFormatter hourFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
            
            for (String alarmId : alarmIds) {
                Alarm alarm = getAlarm(alarmId);
                if (alarm != null) {
                    // 威胁类型统计
                    if (alarm.getThreatType() != null) {
                        threatTypeStats.merge(alarm.getThreatType(), 1, Integer::sum);
                    }
                    
                    // 检测器统计
                    if (alarm.getDetectorType() != null) {
                        detectorStats.merge(alarm.getDetectorDisplayName(), 1, Integer::sum);
                    }
                    
                    // 小时统计
                    if (alarm.getTimestamp() != null) {
                        String hourKey = alarm.getTimestamp().format(hourFormatter);
                        hourlyStats.merge(hourKey, 1, Integer::sum);
                    }
                }
            }
            
            return new AlarmStatistics(
                    alarmIds.size(),
                    threatTypeStats,
                    detectorStats,
                    hourlyStats,
                    startTime,
                    endTime
            );
            
        } catch (Exception e) {
            log.error("获取告警统计信息失败", e);
            return new AlarmStatistics(0, new HashMap<>(), new HashMap<>(), new HashMap<>(), null, null);
        }
    }
    
    /**
     * 清理过期告警
     * 
     * @param maxAgeHours 最大年龄（小时）
     * @return 清理的告警数量
     */
    public long cleanupExpiredAlarms(int maxAgeHours) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(maxAgeHours);
            String pattern = RedisStateManager.KEY_PREFIX_ALARM_STATE + "alarm:*";
            Set<String> alarmKeys = redisStateManager.keys(pattern);
            
            long deletedCount = 0;
            for (String alarmKey : alarmKeys) {
                Alarm alarm = redisStateManager.getObject(alarmKey, Alarm.class);
                if (alarm != null && alarm.getTimestamp() != null && 
                    alarm.getTimestamp().isBefore(cutoffTime)) {
                    deleteAlarm(alarm.getAlarmId());
                    deletedCount++;
                }
            }
            
            log.info("清理过期告警完成: 清理数量={}, 截止时间={}", deletedCount, cutoffTime);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("清理过期告警失败", e);
            return 0;
        }
    }
    
    /**
     * 删除告警
     * 
     * @param alarmId 告警ID
     */
    public void deleteAlarm(String alarmId) {
        if (alarmId == null) {
            return;
        }
        
        try {
            // 删除告警基本信息
            String alarmKey = buildAlarmKey(alarmId);
            redisStateManager.delete(alarmKey);
            
            // 从索引中移除
            removeFromIndexes(alarmId);
            
            log.debug("告警已删除: {}", alarmId);
            
        } catch (Exception e) {
            log.error("删除告警失败: {}", alarmId, e);
        }
    }
    
    /**
     * 更新告警索引
     * 
     * @param alarm 告警信息
     */
    private void updateAlarmIndexes(Alarm alarm) {
        String alarmId = alarm.getAlarmId();
        
        // 按威胁类型索引
        if (alarm.getThreatType() != null) {
            String threatTypeIndexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:threat_type:" + alarm.getThreatType();
            redisStateManager.sadd(threatTypeIndexKey, alarmId);
            redisStateManager.expire(threatTypeIndexKey, ALARM_TTL);
        }
        
        // 按源IP索引
        if (alarm.getSrcIp() != null) {
            String srcIpIndexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:src_ip:" + alarm.getSrcIp();
            redisStateManager.sadd(srcIpIndexKey, alarmId);
            redisStateManager.expire(srcIpIndexKey, ALARM_TTL);
        }
        
        // 按目标IP索引
        if (alarm.getDstIp() != null) {
            String dstIpIndexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:dst_ip:" + alarm.getDstIp();
            redisStateManager.sadd(dstIpIndexKey, alarmId);
            redisStateManager.expire(dstIpIndexKey, ALARM_TTL);
        }
        
        // 按检测器索引
        if (alarm.getDetectorType() != null) {
            String detectorIndexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:detector:" + alarm.getDetectorDisplayName();
            redisStateManager.sadd(detectorIndexKey, alarmId);
            redisStateManager.expire(detectorIndexKey, ALARM_TTL);
        }
        
        // 按时间索引（小时级别）
        if (alarm.getTimestamp() != null) {
            String hourKey = alarm.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
            String hourIndexKey = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:hour:" + hourKey;
            redisStateManager.sadd(hourIndexKey, alarmId);
            redisStateManager.expire(hourIndexKey, ALARM_TTL);
        }
    }
    
    /**
     * 更新告警统计
     * 
     * @param alarm 告警信息
     */
    private void updateAlarmStatistics(Alarm alarm) {
        try {
            // 总告警计数
            String totalCountKey = RedisStateManager.KEY_PREFIX_STATISTICS + "alarm:total_count";
            redisStateManager.incr(totalCountKey);
            redisStateManager.expire(totalCountKey, ALARM_STATS_TTL);
            
            // 按威胁类型计数
            if (alarm.getThreatType() != null) {
                String threatTypeCountKey = RedisStateManager.KEY_PREFIX_STATISTICS + "alarm:threat_type:" + alarm.getThreatType();
                redisStateManager.incr(threatTypeCountKey);
                redisStateManager.expire(threatTypeCountKey, ALARM_STATS_TTL);
            }
            
            // 按检测器计数
            if (alarm.getDetectorType() != null) {
                String detectorCountKey = RedisStateManager.KEY_PREFIX_STATISTICS + "alarm:detector:" + alarm.getDetectorDisplayName();
                redisStateManager.incr(detectorCountKey);
                redisStateManager.expire(detectorCountKey, ALARM_STATS_TTL);
            }
            
            // 按小时计数
            if (alarm.getTimestamp() != null) {
                String hourKey = alarm.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
                String hourCountKey = RedisStateManager.KEY_PREFIX_STATISTICS + "alarm:hour:" + hourKey;
                redisStateManager.incr(hourCountKey);
                redisStateManager.expire(hourCountKey, ALARM_STATS_TTL);
            }
            
        } catch (Exception e) {
            log.error("更新告警统计失败: {}", alarm.getAlarmId(), e);
        }
    }
    
    /**
     * 从索引中移除告警
     * 
     * @param alarmId 告警ID
     */
    private void removeFromIndexes(String alarmId) {
        String indexPattern = RedisStateManager.KEY_PREFIX_ALARM_STATE + "index:*";
        Set<String> indexKeys = redisStateManager.keys(indexPattern);
        
        for (String indexKey : indexKeys) {
            redisStateManager.srem(indexKey, alarmId);
        }
    }
    
    /**
     * 构建告警键
     * 
     * @param alarmId 告警ID
     * @return 告警键
     */
    private String buildAlarmKey(String alarmId) {
        return RedisStateManager.KEY_PREFIX_ALARM_STATE + "alarm:" + alarmId;
    }
    
    /**
     * 构建告警去重键
     * 
     * @param alarm 告警信息
     * @return 去重键
     */
    private String buildAlarmDedupKey(Alarm alarm) {
        // 基于关键字段生成去重键
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisStateManager.KEY_PREFIX_ALARM_STATE).append("dedup:");
        
        if (alarm.getThreatType() != null) {
            keyBuilder.append(alarm.getThreatType()).append(":");
        }
        if (alarm.getSrcIp() != null) {
            keyBuilder.append(alarm.getSrcIp()).append(":");
        }
        if (alarm.getDstIp() != null) {
            keyBuilder.append(alarm.getDstIp()).append(":");
        }
        if (alarm.getDetectorType() != null) {
            keyBuilder.append(alarm.getDetectorDisplayName()).append(":");
        }
        
        // 添加时间窗口（分钟级别）
        if (alarm.getTimestamp() != null) {
            String timeWindow = alarm.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm"));
            keyBuilder.append(timeWindow);
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 告警统计信息
     */
    @lombok.Getter
    @lombok.AllArgsConstructor
    public static class AlarmStatistics {
        private final int totalCount;
        private final Map<String, Integer> threatTypeStats;
        private final Map<String, Integer> detectorStats;
        private final Map<String, Integer> hourlyStats;
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
    }
}
