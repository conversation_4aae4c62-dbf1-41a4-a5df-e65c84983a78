package com.geeksec.session.threat.detector.detection.detector.fingerprint;

import com.geeksec.session.threat.detector.detection.DetectorType;
import com.geeksec.session.threat.detector.detection.ThreatDetector;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import com.geeksec.session.threat.detector.model.input.HttpInfo;
import com.geeksec.session.threat.detector.model.input.SslInfo;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 指纹随机化检测器
 * 检测短时间内频繁变化指纹的攻击行为
 *
 * <AUTHOR>
 */
@Slf4j
public class FingerprintRandomizationDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 指纹变化统计
    private transient Map<String, FingerprintStats> fingerprintStats = new ConcurrentHashMap<>();

    // 检测阈值
    private static final int FINGERPRINT_CHANGE_THRESHOLD = 5; // 指纹变化次数阈值
    private static final long TIME_WINDOW_MS = 300000; // 5分钟时间窗口
    private static final double ENTROPY_THRESHOLD = 3.5; // 熵值阈值
    private static final int MIN_UNIQUE_FINGERPRINTS = 3; // 最小唯一指纹数量

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.FINGERPRINT;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 初始化指纹统计
            if (fingerprintStats == null) {
                fingerprintStats = new ConcurrentHashMap<>();
            }

            // 1. HTTP指纹随机化检测
            if (event.getEventType() == NetworkEvent.EventType.HTTP && event.getHttpInfo() != null) {
                DetectionResult httpResult = detectHttpFingerprintRandomization(event);
                if (httpResult != null) {
                    results.add(httpResult);
                }
            }

            // 2. SSL指纹随机化检测
            if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
                DetectionResult sslResult = detectSslFingerprintRandomization(event);
                if (sslResult != null) {
                    results.add(sslResult);
                }
            }

        } catch (Exception e) {
            log.error("指纹随机化检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测HTTP指纹随机化
     */
    private DetectionResult detectHttpFingerprintRandomization(NetworkEvent event) {
        HttpInfo httpInfo = event.getHttpInfo();
        String srcIp = event.getSrcIp();
        String userAgent = httpInfo.getUserAgent();
        
        if (srcIp == null || userAgent == null) {
            return null;
        }

        // 获取或创建指纹统计
        String statsKey = srcIp + ":http_ua";
        FingerprintStats stats = fingerprintStats.computeIfAbsent(statsKey, 
                k -> new FingerprintStats());
        
        // 更新指纹统计
        long currentTime = System.currentTimeMillis();
        stats.addFingerprint(currentTime, userAgent);
        
        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);
        
        // 检查是否符合随机化模式
        if (isRandomizationPattern(stats)) {
            return createDetectionResult(event, "HTTP_UA_RANDOMIZATION", 
                    "HTTP User-Agent指纹随机化",
                    DetectionResult.ThreatLevel.MEDIUM, 0.8,
                    String.format("检测到User-Agent指纹随机化，变化次数: %d，唯一指纹: %d，熵值: %.2f", 
                            stats.getChangeCount(), stats.getUniqueCount(), stats.getEntropy()));
        }

        return null;
    }

    /**
     * 检测SSL指纹随机化
     */
    private DetectionResult detectSslFingerprintRandomization(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        String srcIp = event.getSrcIp();
        String ja3Hash = sslInfo.getJa3Hash();
        
        if (srcIp == null || ja3Hash == null) {
            return null;
        }

        // 获取或创建指纹统计
        String statsKey = srcIp + ":ssl_ja3";
        FingerprintStats stats = fingerprintStats.computeIfAbsent(statsKey, 
                k -> new FingerprintStats());
        
        // 更新指纹统计
        long currentTime = System.currentTimeMillis();
        stats.addFingerprint(currentTime, ja3Hash);
        
        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);
        
        // 检查是否符合随机化模式
        if (isRandomizationPattern(stats)) {
            return createDetectionResult(event, "SSL_JA3_RANDOMIZATION", 
                    "SSL JA3指纹随机化",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    String.format("检测到JA3指纹随机化，变化次数: %d，唯一指纹: %d，熵值: %.2f", 
                            stats.getChangeCount(), stats.getUniqueCount(), stats.getEntropy()));
        }

        return null;
    }

    /**
     * 检查是否符合随机化模式
     */
    private boolean isRandomizationPattern(FingerprintStats stats) {
        // 检查变化次数
        if (stats.getChangeCount() < FINGERPRINT_CHANGE_THRESHOLD) {
            return false;
        }
        
        // 检查唯一指纹数量
        if (stats.getUniqueCount() < MIN_UNIQUE_FINGERPRINTS) {
            return false;
        }
        
        // 检查熵值
        if (stats.getEntropy() < ENTROPY_THRESHOLD) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算字符串熵值
     */
    private double calculateEntropy(String text) {
        if (text == null || text.isEmpty()) {
            return 0.0;
        }
        
        Map<Character, Integer> charCount = new HashMap<>();
        for (char c : text.toCharArray()) {
            charCount.put(c, charCount.getOrDefault(c, 0) + 1);
        }
        
        double entropy = 0.0;
        int length = text.length();
        
        for (int count : charCount.values()) {
            double probability = (double) count / length;
            entropy -= probability * (Math.log(probability) / Math.log(2));
        }
        
        return entropy;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String ruleId, String threatType,
                                                  DetectionResult.ThreatLevel level, double confidence, String description) {
        return DetectionResult.builder()
                .detectorType(getDetectorType())
                .ruleId(ruleId)
                .threatType(threatType)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .detectionTime(event.getTimestamp())
                .build();
    }

    /**
     * 指纹统计类
     */
    private static class FingerprintStats {
        private final List<FingerprintEntry> entries = new ArrayList<>();
        private final Set<String> uniqueFingerprints = new HashSet<>();

        public void addFingerprint(long timestamp, String fingerprint) {
            entries.add(new FingerprintEntry(timestamp, fingerprint));
            uniqueFingerprints.add(fingerprint);
        }

        public void cleanExpiredData(long currentTime, long timeWindowMs) {
            entries.removeIf(entry -> currentTime - entry.timestamp > timeWindowMs);
            
            // 重新计算唯一指纹集合
            uniqueFingerprints.clear();
            for (FingerprintEntry entry : entries) {
                uniqueFingerprints.add(entry.fingerprint);
            }
        }

        public int getChangeCount() {
            if (entries.size() < 2) {
                return 0;
            }
            
            int changes = 0;
            for (int i = 1; i < entries.size(); i++) {
                if (!entries.get(i).fingerprint.equals(entries.get(i - 1).fingerprint)) {
                    changes++;
                }
            }
            
            return changes;
        }

        public int getUniqueCount() {
            return uniqueFingerprints.size();
        }

        public double getEntropy() {
            if (uniqueFingerprints.isEmpty()) {
                return 0.0;
            }
            
            // 计算指纹分布的熵值
            Map<String, Integer> fingerprintCount = new HashMap<>();
            for (FingerprintEntry entry : entries) {
                fingerprintCount.put(entry.fingerprint, 
                        fingerprintCount.getOrDefault(entry.fingerprint, 0) + 1);
            }
            
            double entropy = 0.0;
            int totalCount = entries.size();
            
            for (int count : fingerprintCount.values()) {
                double probability = (double) count / totalCount;
                entropy -= probability * (Math.log(probability) / Math.log(2));
            }
            
            return entropy;
        }

        /**
         * 指纹条目
         */
        private static class FingerprintEntry {
            final long timestamp;
            final String fingerprint;

            FingerprintEntry(long timestamp, String fingerprint) {
                this.timestamp = timestamp;
                this.fingerprint = fingerprint;
            }
        }
    }
}
