package com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge;

import com.geeksec.common.network.DomainUtils;
import com.geeksec.common.utils.crypto.HashUtils;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class DomainDerivesRegDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.DOMAIN_DERIVES_FROM_REGISTRABLE_DOMAIN_TAG;
    }

    /**
     * 域名属于锚域名 (DOMAIN -> FDOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        Set<String> domainSet = getSessionDomain(value);
        List<Row> edgeList = new ArrayList<>();
        // 处理锚域名MD5
        for (String domain : domainSet) {
            // 从域名中获取锚域名
            String fDomain = getBaseDomain(domain);
            domain = DomainUtils.formatDomain(domain);
            fDomain = DomainUtils.formatDomain(fDomain);

            edgeList.add(Row.of(domain, fDomain,
                    0, // rank暂定0
                    Boolean.FALSE));
        }
        return edgeList;
    }
}
