package com.geeksec.session.threat.detector.output.notification;

import com.geeksec.session.threat.detector.config.ThreatDetectorConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;

/**
 * 通知输出管理器
 * 负责将通知消息发送到Kafka订阅地址
 *
 * <AUTHOR>
 */
@Slf4j
public class NotificationOutputManager {

    /**
     * 私有构造方法，防止实例化
     */
    private NotificationOutputManager() {
        // 工具类，禁止实例化
    }

    /**
     * 配置通知消息输出
     *
     * @param notificationStream 通知消息数据流
     * @param config 配置参数
     */
    public static void configure(DataStream<String> notificationStream, ParameterTool config) {
        log.info("配置通知消息输出到Kafka");

        // 添加日志输出Sink（用于调试）
        notificationStream
                .addSink(new NotificationLogSink())
                .name("通知消息日志输出")
                .uid("notification-log-sink");

        // 添加Kafka输出Sink
        notificationStream
                .sinkTo(createKafkaSink(config))
                .name("通知消息Kafka输出")
                .uid("notification-kafka-sink");

        log.info("通知消息输出配置完成");
    }

    /**
     * 创建Kafka Sink
     *
     * @param config 配置参数
     * @return Kafka Sink
     */
    private static KafkaSink<String> createKafkaSink(ParameterTool config) {
        String bootstrapServers = ThreatDetectorConfig.getKafkaBootstrapServers();
        String topic = ThreatDetectorConfig.getThreatNotificationTopic();

        return KafkaSink.<String>builder()
                .setBootstrapServers(bootstrapServers)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(topic)
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .build();
    }

    /**
     * 通知消息日志输出Sink
     */
    private static class NotificationLogSink implements SinkFunction<String> {
        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(String value, Context context) {
            log.info("通知消息: {}", value);
        }
    }
}
