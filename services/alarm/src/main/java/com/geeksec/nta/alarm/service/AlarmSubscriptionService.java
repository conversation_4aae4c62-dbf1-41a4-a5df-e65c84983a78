package com.geeksec.nta.alarm.service;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.subscription.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警订阅服务接口
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface AlarmSubscriptionService {
    
    /**
     * 创建订阅
     * 
     * @param request 创建请求
     * @param userId 用户ID
     * @return 订阅ID
     */
    String createSubscription(CreateSubscriptionRequest request, String userId);
    
    /**
     * 更新订阅
     * 
     * @param id 订阅ID
     * @param request 更新请求
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateSubscription(String id, UpdateSubscriptionRequest request, String userId);
    
    /**
     * 删除订阅
     * 
     * @param id 订阅ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteSubscription(String id, String userId);
    
    /**
     * 获取订阅详情
     * 
     * @param id 订阅ID
     * @param userId 用户ID
     * @return 订阅详情
     */
    AlarmSubscriptionVo getSubscription(String id, String userId);
    
    /**
     * 分页查询用户订阅
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页大小
     * @param keyword 关键词
     * @return 分页结果
     */
    PageResultVo<AlarmSubscriptionVo> getSubscriptions(String userId, int page, int size, String keyword);
    
    /**
     * 启用/禁用订阅
     * 
     * @param id 订阅ID
     * @param enabled 是否启用
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean toggleSubscription(String id, boolean enabled, String userId);
    
    /**
     * 测试订阅规则
     * 
     * @param request 测试请求
     * @return 测试结果
     */
    TestSubscriptionResult testSubscription(TestSubscriptionRequest request);
    
    /**
     * 获取所有启用的订阅（供 alarm-notification Flink 作业启动时调用）
     * 
     * @return 启用的订阅列表
     */
    List<NotificationSubscriptionDto> getActiveSubscriptions();
    
    /**
     * 记录通知发送结果（供 alarm-notification Flink 作业调用）
     * 
     * @param results 发送结果列表
     */
    void recordNotificationResults(List<NotificationResultDto> results);
}
