package com.geeksec.alarm.processor.pipeline.processor;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 告警类型处理器抽象基类
 * 提供通用的处理逻辑和工具方法
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public abstract class AbstractAlarmTypeProcessor implements AlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public Alarm processAlarm(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        try {
            log.debug("开始处理告警类型: {}, 告警ID: {}", getSupportedType().getDisplayName(), alarm.getAlarmId());
            
            // 1. 生成原因分析
            List<Alarm.DetectionReason> reasonAnalysis = generateReasonAnalysis(alarm, knowledgeBaseClient);
            
            // 2. 生成处理建议
            Alarm.HandlingSuggestions handlingSuggestions = generateHandlingSuggestions(alarm, knowledgeBaseClient);
            
            // 3. 生成攻击路径
            List<Map<String, Object>> attackRoute = generateAttackRoute(alarm, knowledgeBaseClient);
            
            // 4. 获取受害者和攻击者信息
            List<Map<String, String>> victimInfo = getVictimInfo(alarm);
            List<Map<String, String>> attackerInfo = getAttackerInfo(alarm);
            
            // 5. 获取攻击家族信息
            List<Map<String, Object>> attackFamily = getAttackFamily(alarm, knowledgeBaseClient);
            
            // 6. 获取目标信息
            List<Map<String, Object>> targets = getTargets(alarm);
            
            // 7. 设置告警原理和检测原理
            String alarmPrinciple = getAlarmPrinciple(alarm);
            String detectionPrinciple = getDetectionPrinciple(alarm);
            
            // 8. 更新告警对象
            updateAlarmWithProcessedInfo(alarm, reasonAnalysis, handlingSuggestions, attackRoute,
                    victimInfo, attackerInfo, attackFamily, targets, alarmPrinciple, detectionPrinciple);
            
            log.debug("告警处理完成: {}", alarm.getAlarmId());
            return alarm;
            
        } catch (Exception e) {
            log.error("处理告警类型失败: {}, 告警ID: {}", getSupportedType().getDisplayName(), alarm.getAlarmId(), e);
            // 返回原始告警，避免数据丢失
            return alarm;
        }
    }
    
    /**
     * 更新告警对象的处理信息
     */
    private void updateAlarmWithProcessedInfo(Alarm alarm,
                                            List<Alarm.DetectionReason> reasonAnalysis,
                                            Alarm.HandlingSuggestions handlingSuggestions,
                                            List<Map<String, Object>> attackRoute,
                                            List<Map<String, String>> victimInfo,
                                            List<Map<String, String>> attackerInfo,
                                            List<Map<String, Object>> attackFamily,
                                            List<Map<String, Object>> targets,
                                            String alarmPrinciple,
                                            String detectionPrinciple) {
        
        // 设置原因分析
        if (alarm.getReasonAnalysis() == null) {
            alarm.setReasonAnalysis(Alarm.ReasonAnalysis.builder().build());
        }
        alarm.getReasonAnalysis().setDetailedReasons(reasonAnalysis);
        
        // 设置处理建议
        alarm.setHandlingSuggestions(handlingSuggestions);
        
        // 设置扩展属性
        if (alarm.getExtendedProperties() == null) {
            alarm.setExtendedProperties(new HashMap<>());
        }
        
        Map<String, Object> extendedProps = alarm.getExtendedProperties();
        extendedProps.put("attack_route", attackRoute);
        extendedProps.put("victim", victimInfo);
        extendedProps.put("attacker", attackerInfo);
        extendedProps.put("attack_family", attackFamily);
        extendedProps.put("targets", targets);
        extendedProps.put("alarm_principle", alarmPrinciple);
        extendedProps.put("detection_principle", detectionPrinciple);
        extendedProps.put("model_id", getModelId(alarm));
        extendedProps.put("alarm_type", "模型");
    }
    
    /**
     * 创建检测原因对象
     */
    protected Alarm.DetectionReason createDetectionReason(String reasonType, String description, 
                                                        String expectedValue, String actualValue, 
                                                        String detectedFeature, int importance) {
        return Alarm.DetectionReason.builder()
                .reasonType(reasonType)
                .description(description)
                .expectedValue(expectedValue)
                .actualValue(actualValue)
                .detectedFeature(detectedFeature)
                .importance(importance)
                .build();
    }
    
    /**
     * 获取默认的受害者信息
     */
    @Override
    public List<Map<String, String>> getVictimInfo(Alarm alarm) {
        List<Map<String, String>> victims = new ArrayList<>();
        if (alarm.getSrcIp() != null) {
            Map<String, String> victim = new HashMap<>();
            victim.put("ip", alarm.getSrcIp());
            victims.add(victim);
        }
        return victims;
    }
    
    /**
     * 获取默认的攻击者信息
     */
    @Override
    public List<Map<String, String>> getAttackerInfo(Alarm alarm) {
        List<Map<String, String>> attackers = new ArrayList<>();
        if (alarm.getDstIp() != null) {
            Map<String, String> attacker = new HashMap<>();
            attacker.put("ip", alarm.getDstIp());
            attackers.add(attacker);
        }
        return attackers;
    }
    
    /**
     * 获取默认的攻击家族信息
     */
    @Override
    public List<Map<String, Object>> getAttackFamily(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Map<String, Object>> attackFamily = new ArrayList<>();
        
        try {
            // 从知识库获取威胁情报
            if (alarm.getThreatType() != null) {
                Map<String, Object> threatInfo = knowledgeBaseClient.getThreatIntelligenceByName(alarm.getThreatType());
                if (threatInfo != null) {
                    attackFamily.add(threatInfo);
                }
            }
        } catch (Exception e) {
            log.warn("获取攻击家族信息失败: {}", e.getMessage());
        }
        
        return attackFamily;
    }
    
    /**
     * 获取默认的目标信息
     */
    @Override
    public List<Map<String, Object>> getTargets(Alarm alarm) {
        List<Map<String, Object>> targets = new ArrayList<>();
        
        if (alarm.getDstIp() != null) {
            Map<String, Object> target = new HashMap<>();
            target.put("ip", alarm.getDstIp());
            target.put("port", alarm.getDstPort());
            target.put("protocol", alarm.getProtocol());
            targets.add(target);
        }
        
        return targets;
    }
    
    /**
     * 获取默认的攻击路径
     */
    @Override
    public List<Map<String, Object>> generateAttackRoute(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        // 默认实现返回空列表，子类可以重写
        return new ArrayList<>();
    }
    
    /**
     * 获取默认的告警原理
     */
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        return getSupportedType().getDescription();
    }
    
    /**
     * 获取默认的检测原理
     */
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "基于" + getSupportedType().getDisplayName() + "的检测规则进行识别";
    }
    
    /**
     * 检查IP是否为内网IP
     */
    protected boolean isInternalIp(String ip) {
        if (ip == null) return false;
        
        return ip.startsWith("10.") || 
               ip.startsWith("192.168.") || 
               (ip.startsWith("172.") && isInRange172(ip));
    }
    
    private boolean isInRange172(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length >= 2) {
                int second = Integer.parseInt(parts[1]);
                return second >= 16 && second <= 31;
            }
        } catch (NumberFormatException e) {
            // 忽略解析错误
        }
        return false;
    }
}
