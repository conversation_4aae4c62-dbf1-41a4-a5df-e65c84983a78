package com.geeksec.nta.traffic.etl.etl.graph.extractor.dns.edge;

import com.geeksec.common.network.NetworkUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class OrgOwnsIPEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.ORG_OWNS_IP_TAG;
    }

    /**
     * 组织拥有IP (IP -> ORG)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        String domainIP = value.getField(FieldConstants.FIELD_DOMAIN_IP).toString();
        if (NetworkUtils.isValidIp(domainIP)){
            return List.of(Row.of(domainIP, "orgName",
                    0 // rank暂定0
                    ));
        }
        return Collections.emptyList();
    }
}
