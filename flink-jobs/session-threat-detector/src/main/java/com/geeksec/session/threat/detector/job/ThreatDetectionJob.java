package com.geeksec.session.threat.detector.job;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.session.threat.detector.config.ThreatDetectorConfig;
import com.geeksec.session.threat.detector.job.pipeline.ThreatDetectionPipeline;
import com.geeksec.session.threat.detector.output.OutputManager;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 威胁检测作业
 *
 * 业务流程：
 * 1. 接收网络会话元数据和协议元数据
 * 2. 通过特征规则和模型检测威胁
 * 3. 生成四种输出：
 *    - 会话标签更新Doris会话表
 *    - 资产标签写入Nebula图数据库（自环边形式）
 *    - 告警写入Doris告警表
 *    - 告警发送到Kafka订阅地址
 *
 * <AUTHOR>
 */
@Slf4j
public class ThreatDetectionJob {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        log.info("启动威胁检测作业");

        // 使用威胁检测器配置管理器获取配置
        final ParameterTool config = ThreatDetectorConfig.getConfig();

        // 合并命令行参数（命令行参数优先级更高）
        final ParameterTool parameterTool = ParameterTool.fromArgs(args).mergeWith(config);

        // 打印配置信息（调试模式下）
        ThreatDetectorConfig.printConfig();

        // 创建执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置全局参数
        env.getConfig().setGlobalJobParameters(parameterTool);

        // 配置检查点
        configureCheckpointing(env);

        // 创建数据源
        DataStream<Map<String, Object>> sessionStream = createSessionSource(env);
        DataStream<Map<String, Object>> protocolStream = createProtocolSource(env);

        // 创建检测器开关配置数据源（如果启用动态开关控制）
        DataStream<Map<Integer, Integer>> detectorSwitchStream = null;
        if (ThreatDetectorConfig.isDynamicSwitchEnabled()) {
            detectorSwitchStream = createDetectorSwitchSource(env);
        }

        // 构建威胁检测流水线
        ThreatDetectionPipeline.PipelineResult pipelineResult =
                ThreatDetectionPipeline.build(sessionStream, protocolStream, detectorSwitchStream, parameterTool);

        // 配置输出管理器
        OutputManager.configureAllOutputs(pipelineResult, parameterTool);

        // 执行作业
        env.execute("威胁检测作业");

        log.info("威胁检测作业执行完成");
    }
    
    /**
     * 配置检查点
     *
     * @param env 执行环境
     */
    private static void configureCheckpointing(StreamExecutionEnvironment env) {
        // 启用检查点，每60秒触发一次
        env.enableCheckpointing(60000);
        
        // 设置检查点模式为EXACTLY_ONCE
        env.getCheckpointConfig().setCheckpointingMode(
                org.apache.flink.streaming.api.CheckpointingMode.EXACTLY_ONCE);
        
        // 设置检查点之间的最小时间间隔
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(30000);
        
        // 设置检查点超时时间
        env.getCheckpointConfig().setCheckpointTimeout(120000);
        
        // 设置最大并发检查点数
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        
        // 设置任务取消时保留检查点
        env.getCheckpointConfig().setExternalizedCheckpointCleanup(
                org.apache.flink.streaming.api.environment.CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        
        log.info("检查点配置完成");
    }
    
    /**
     * 创建Kafka会话数据源
     *
     * @param env 执行环境
     * @return 会话数据流
     */
    private static DataStream<Map<String, Object>> createSessionSource(StreamExecutionEnvironment env) {
        // 创建Kafka源
        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(ThreatDetectorConfig.getKafkaBootstrapServers())
                .setTopics(ThreatDetectorConfig.getSessionMetadataTopic())
                .setGroupId(ThreatDetectorConfig.getKafkaGroupId())
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
        
        // 创建Kafka数据流
        DataStream<String> kafkaStream = env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "会话数据源");
        
        // 解析JSON为Map
        DataStream<Map<String, Object>> sessionStream = kafkaStream
                .map(json -> {
                    try {
                        return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
                    } catch (Exception e) {
                        log.error("解析会话数据JSON失败", e);
                        return null;
                    }
                })
                .filter(map -> map != null)
                .name("会话数据JSON解析");
        
        log.info("会话数据源创建完成");
        return sessionStream;
    }
    
    /**
     * 创建Kafka配置数据源
     *
     * @param env 执行环境
     * @return 配置数据流
     */
    private static DataStream<Map<String, Object>> createConfigSource(StreamExecutionEnvironment env) {
        // 创建Kafka源
        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(ThreatDetectorConfig.getKafkaBootstrapServers())
                .setTopics("threat-detector-config")
                .setGroupId(ThreatDetectorConfig.getKafkaGroupId())
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
        
        // 创建Kafka数据流
        DataStream<String> kafkaStream = env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "配置数据源");
        
        // 解析JSON为Map
        DataStream<Map<String, Object>> configStream = kafkaStream
                .map(json -> {
                    try {
                        return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
                    } catch (Exception e) {
                        log.error("解析配置数据JSON失败", e);
                        return null;
                    }
                })
                .filter(map -> map != null)
                .name("配置数据JSON解析");
        
        log.info("配置数据源创建完成");
        return configStream;
    }

    /**
     * 创建Kafka协议数据源
     *
     * @param env 执行环境
     * @return 协议数据流
     */
    private static DataStream<Map<String, Object>> createProtocolSource(StreamExecutionEnvironment env) {
        // 创建Kafka源 - 支持多个协议主题
        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(ThreatDetectorConfig.getKafkaBootstrapServers())
                .setTopics(
                    ThreatDetectorConfig.getHttpTopic(),
                    ThreatDetectorConfig.getDnsTopic(),
                    ThreatDetectorConfig.getSslTopic()
                )
                .setGroupId(ThreatDetectorConfig.getKafkaGroupId())
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        // 创建Kafka数据流
        DataStream<String> kafkaStream = env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "协议数据源");

        // 解析JSON为Map
        DataStream<Map<String, Object>> protocolStream = kafkaStream
                .map(json -> {
                    try {
                        return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
                    } catch (Exception e) {
                        log.error("解析协议数据JSON失败", e);
                        return null;
                    }
                })
                .filter(map -> map != null)
                .name("协议数据JSON解析");

        log.info("协议数据源创建完成");
        return protocolStream;
    }

    /**
     * 创建检测器开关配置数据源
     *
     * @param env 执行环境
     * @return 检测器开关配置数据流
     */
    private static DataStream<Map<Integer, Integer>> createDetectorSwitchSource(StreamExecutionEnvironment env) {
        // 创建Kafka源
        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(ThreatDetectorConfig.getKafkaBootstrapServers())
                .setTopics(ThreatDetectorConfig.getDetectorSwitchTopic())
                .setGroupId(ThreatDetectorConfig.getKafkaGroupId() + "-switch")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        // 创建Kafka数据流
        DataStream<String> kafkaStream = env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "检测器开关配置数据源");

        // 解析JSON为Map<Integer, Integer>
        DataStream<Map<Integer, Integer>> switchStream = kafkaStream
                .map(json -> {
                    try {
                        return OBJECT_MAPPER.readValue(json, new TypeReference<Map<Integer, Integer>>() {});
                    } catch (Exception e) {
                        log.error("解析检测器开关配置JSON失败", e);
                        return null;
                    }
                })
                .filter(map -> map != null)
                .name("检测器开关配置JSON解析");

        log.info("检测器开关配置数据源创建完成");
        return switchStream;
    }
}