package com.geeksec.certificate.analyzer.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 证书行业分类枚举
 * 封装了行业名称、匹配的英文关键词以及查找逻辑。
 */
@Getter
public enum IndustryCategory {

    FINANCE("金融", "bank", "finance", "securities", "insurance", "payment", "credit", "investment", "hsbc", "chase", "citi", "paypal", "stripe", "visa", "mastercard"),
    HEALTH("医疗", "health", "hospital", "medical", "pharma", "clinic", "biotech", "pharmaceutical"),
    EDUCATION("教育", "edu", "university", "school", "college", "institute", "academic"),
    GOVERNMENT("政府", "gov", "government", "mil"),
    ECOMMERCE("电商", "e-commerce", "shop", "store", "market", "retail", "alibaba", "amazon", "jd", "ebay", "shopify", "walmart", "target"),
    IT("IT", "tech", "software", "cloud", "network", "data", "hosting", "cdn", "google", "microsoft", "oracle", "ibm", "apple", "intel", "cisco", "vmware", "sap", "tencent"),
    GAMING("游戏", "game", "gaming", "entertainment", "blizzard", "nintendo", "sony", "riot", "valve", "steam"),
    MANUFACTURING("制造业", "manufacturing", "industrial", "motor", "auto", "boeing", "siemens"),
    ENERGY("能源", "energy", "power", "oil", "gas", "solar", "wind", "shell"),
    TRANSPORT("交通", "transport", "airline", "shipping", "logistics", "uber", "lyft", "fedex", "ups"),
    MEDIA("媒体", "media", "news", "entertainment", "social", "facebook", "twitter", "instagram", "linkedin", "netflix", "youtube", "disney"),
    OTHER("其他行业");

    private final String chineseName;
    private final List<String> keywords;

    IndustryCategory(String chineseName, String... keywords) {
        this.chineseName = chineseName;
        this.keywords = Arrays.asList(keywords);
    }

    /**
     * 根据证书的Common Name查找匹配的行业分类。
     *
     * @param commonName 证书的Common Name
     * @return 匹配到的行业中文名，如果未匹配到则返回“其他行业”
     */
    public static String findByCommonName(String commonName) {
        if (commonName == null || commonName.trim().isEmpty()) {
            return OTHER.getChineseName();
        }
        String lowerCaseCn = commonName.toLowerCase();
        for (IndustryCategory category : values()) {
            if (category == OTHER) continue;
            for (String keyword : category.keywords) {
                if (lowerCaseCn.contains(keyword)) {
                    return category.getChineseName();
                }
            }
        }
        return OTHER.getChineseName();
    }
}
