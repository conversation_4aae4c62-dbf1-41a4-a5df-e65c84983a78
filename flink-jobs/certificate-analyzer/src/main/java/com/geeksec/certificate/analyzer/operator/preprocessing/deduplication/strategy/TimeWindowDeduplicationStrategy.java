package com.geeksec.certificate.analyzer.operator.preprocessing.deduplication.strategy;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import java.time.Duration;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Map;

/**
 * 时间窗口去重策略
 * <p>
 * 基于时间窗口的证书去重实现，在指定时间窗口内对相同SHA1指纹的证书进行去重。
 * 这是默认的去重策略，适用于大多数场景。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class TimeWindowDeduplicationStrategy implements DeduplicationStrategy {
    
    private static final String STRATEGY_NAME = "TimeWindow";
    private static final long DEFAULT_WINDOW_SIZE_MS = 5000L;
    
    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public SingleOutputStreamOperator<X509Certificate> deduplicate(DataStream<X509Certificate> certificateStream) {
        return deduplicate(certificateStream, new DeduplicationConfig().setWindowSizeMs(DEFAULT_WINDOW_SIZE_MS));
    }
    
    @Override
    public SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream, 
            DeduplicationConfig config) {
        
        log.info("使用时间窗口去重策略，窗口大小: {}ms, 并行度: {}", 
                config.getWindowSizeMs(), config.getParallelism());
        
        return certificateStream
                // 按证书SHA1指纹分组
                .keyBy(new CertificateKeySelector())
                // 设置时间窗口
                .window(TumblingProcessingTimeWindows.of(Duration.ofMillis(config.getWindowSizeMs())))
                // 去重处理
                .process(new TimeWindowDeduplicationFunction())
                .name("时间窗口证书去重")
                .setParallelism(config.getParallelism());
    }
    
    /**
     * 证书键选择器
     */
    private static class CertificateKeySelector implements KeySelector<X509Certificate, String> {
        @Override
        public String getKey(X509Certificate certificate) throws Exception {
            String sha1 = certificate.getDerSha1();
            return sha1 != null ? sha1 : "unknown";
        }
    }
    
    /**
     * 时间窗口去重处理函数
     */
    private static class TimeWindowDeduplicationFunction 
            extends ProcessWindowFunction<X509Certificate, X509Certificate, String, TimeWindow> {
        
        @Override
        public void process(String key, Context context, 
                          Iterable<X509Certificate> elements, 
                          Collector<X509Certificate> out) throws Exception {
            
            Map<String, X509Certificate> certificateMap = new HashMap<>();
            int totalCount = 0;
            
            // 遍历窗口内的所有证书
            for (X509Certificate cert : elements) {
                totalCount++;
                String sha1 = cert.getDerSha1();
                
                if (sha1 != null) {
                    // 如果已存在相同SHA1的证书，比较时间戳，保留最新的
                    X509Certificate existing = certificateMap.get(sha1);
                    if (existing == null || isNewer(cert, existing)) {
                        certificateMap.put(sha1, cert);
                    }
                } else {
                    // 对于没有SHA1的证书，直接输出
                    out.collect(cert);
                }
            }
            
            // 输出去重后的证书
            for (X509Certificate cert : certificateMap.values()) {
                out.collect(cert);
            }
            
            int deduplicatedCount = certificateMap.size();
            if (totalCount > deduplicatedCount) {
                log.debug("窗口去重完成，原始数量: {}, 去重后数量: {}, 去重率: {:.2f}%", 
                         totalCount, deduplicatedCount, 
                         (1.0 - (double)deduplicatedCount / totalCount) * 100);
            }
        }
        
        /**
         * 判断证书是否更新
         */
        private boolean isNewer(X509Certificate cert1, X509Certificate cert2) {
            if (cert1.getImportTime() == null) return false;
            if (cert2.getImportTime() == null) return true;
            return cert1.getImportTime().isAfter(cert2.getImportTime());
        }
    }
}
