# Alarm Processor Redis 集成说明

## 概述

alarm-processor 项目现在使用统一的 `RedisConnectionManager`（来自 common 模块）来管理 Redis 连接，提供了一致的 Redis 访问接口和连接池管理。

## 架构设计

```
alarm-processor
├── RedisAlarmCacheManager (新增)
│   ├── 告警去重缓存
│   ├── 告警抑制规则缓存  
│   └── 告警统计缓存
├── RedisSuppressionCache (重构)
│   └── 使用 RedisAlarmCacheManager
└── common/RedisConnectionManager
    └── 统一的 Redis 连接管理
```

## 主要组件

### 1. RedisConnectionManager (common 模块)
- 统一的 Redis 连接池管理
- 提供基础的 Redis 操作方法
- 支持连接池配置和密码认证

### 2. RedisAlarmCacheManager (alarm-processor)
- 基于 RedisConnectionManager 构建
- 专门为告警处理场景设计
- 提供三大功能模块：
  - **告警去重**：防止重复告警
  - **告警抑制**：根据规则抑制告警
  - **告警统计**：统计告警频率

### 3. RedisSuppressionCache (重构后)
- 使用 RedisAlarmCacheManager 实现
- 保持原有接口兼容性
- 简化了内部实现

## 配置说明

### Redis 配置参数

```properties
# Redis 基础配置
alarm.processor.redis.host=localhost
alarm.processor.redis.port=6379
alarm.processor.redis.password=
alarm.processor.redis.database=0

# 连接配置
alarm.processor.redis.connectionTimeout=5000
alarm.processor.redis.socketTimeout=5000

# 连接池配置
alarm.processor.redis.maxTotal=20
alarm.processor.redis.maxIdle=10
alarm.processor.redis.minIdle=2

# 缓存配置
alarm.processor.redis.suppressionKeyPrefix=alarm:suppression:
alarm.processor.redis.cacheExpireSeconds=3600
```

### 在代码中使用

#### 1. 在 Flink 算子中使用

```java
public class AlarmProcessor extends RichMapFunction<AlarmEvent, AlarmEvent> {
    
    private transient RedisAlarmCacheManager cacheManager;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 获取配置
        AlarmProcessorConfig config = // ... 获取配置
        RedisConfig redisConfig = config.getRedisConfig();
        
        // 初始化缓存管理器
        cacheManager = new RedisAlarmCacheManager(redisConfig);
    }
    
    @Override
    public AlarmEvent map(AlarmEvent alarm) throws Exception {
        // 1. 检查告警去重
        String alarmKey = generateAlarmKey(alarm);
        if (cacheManager.isDuplicateAlarm(alarmKey)) {
            return null; // 跳过重复告警
        }
        
        // 2. 添加到去重缓存
        cacheManager.addAlarmToDedup(alarmKey, 300); // 5分钟过期
        
        // 3. 检查告警抑制
        if (cacheManager.shouldSuppressAlarm(alarm.getVictim(), 
                                           alarm.getAttacker(), 
                                           alarm.getLabel())) {
            return null; // 跳过被抑制的告警
        }
        
        // 4. 更新告警统计
        String statsKey = alarm.getLabel() + ":" + alarm.getVictim();
        long count = cacheManager.incrementAlarmStats(statsKey, 3600);
        
        // 5. 处理告警
        return processAlarm(alarm);
    }
    
    @Override
    public void close() throws Exception {
        if (cacheManager != null) {
            cacheManager.close();
        }
        super.close();
    }
}
```

#### 2. 告警抑制规则管理

```java
// 添加抑制规则
boolean success = cacheManager.addSuppressionRule(
    "*************",  // 受害者IP
    "********",       // 攻击者IP  
    "MALWARE",        // 告警标签
    3600              // 过期时间（秒）
);

// 检查是否被抑制
boolean suppressed = cacheManager.shouldSuppressAlarm(
    "*************", 
    "********", 
    "MALWARE"
);

// 移除抑制规则
boolean removed = cacheManager.removeSuppressionRule(
    "*************", 
    "********", 
    "MALWARE"
);
```

#### 3. 告警统计功能

```java
// 增加统计计数
long count = cacheManager.incrementAlarmStats("MALWARE:*************", 3600);

// 获取当前统计值
long currentCount = cacheManager.getAlarmStats("MALWARE:*************");

// 设置统计值
boolean success = cacheManager.setAlarmStats("MALWARE:*************", 100, 3600);
```

## Redis 键命名规范

### 1. 告警去重键
- 格式：`alarm:dedup:{alarmKey}`
- 示例：`alarm:dedup:*************|********|MALWARE|1234567890`

### 2. 告警抑制键
- 格式：`alarm:suppression:{victim}|{attacker}|{label}`
- 示例：`alarm:suppression:*************|********|MALWARE`

### 3. 告警统计键
- 格式：`alarm:stats:{statsKey}`
- 示例：`alarm:stats:MALWARE:*************`

## 最佳实践

### 1. 连接池配置
- 根据并发量调整 `maxTotal` 和 `maxIdle`
- 设置合适的超时时间避免连接堆积
- 启用连接测试确保连接可用性

### 2. 缓存过期策略
- 告警去重：建议 5-15 分钟
- 告警抑制：根据业务需求，通常 1-24 小时
- 告警统计：根据统计周期，通常 1 小时到 1 天

### 3. 错误处理
- 所有 Redis 操作都有异常处理
- Redis 不可用时不影响主要业务流程
- 记录详细的错误日志便于排查

### 4. 性能优化
- 使用批量操作减少网络开销
- 合理设置键的过期时间避免内存泄漏
- 监控 Redis 内存使用情况

## 监控和运维

### 1. 关键指标
- Redis 连接池使用率
- 缓存命中率
- 操作响应时间
- 错误率

### 2. 日志监控
- 连接池初始化日志
- Redis 操作错误日志
- 缓存统计日志

### 3. 故障处理
- Redis 连接失败时的降级策略
- 连接池耗尽时的处理方案
- 数据恢复和备份策略

## 示例代码

完整的使用示例请参考：
- `RedisIntegrationExample.java` - 基础使用示例
- `AlarmProcessorWithRedis` - Flink 算子集成示例

## 注意事项

1. **线程安全**：RedisAlarmCacheManager 是线程安全的
2. **资源管理**：使用完毕后要调用 `close()` 方法释放连接
3. **配置验证**：启动时会验证 Redis 连接配置
4. **向后兼容**：保持了原有 RedisSuppressionCache 接口的兼容性
