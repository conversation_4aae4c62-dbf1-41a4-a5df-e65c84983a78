package com.geeksec.session.threat.detector.util.exception;

/**
 * 基础异常类
 */
public class BaseException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private Object[] args;
    
    public BaseException() {
        super();
    }
    
    public BaseException(String message) {
        super(message);
    }
    
    public BaseException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public BaseException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }
    
    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public BaseException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public BaseException(Throwable cause) {
        super(cause);
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public Object[] getArgs() {
        return args != null ? args.clone() : null;
    }
}
