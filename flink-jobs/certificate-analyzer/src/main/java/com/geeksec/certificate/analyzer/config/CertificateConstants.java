package com.geeksec.certificate.analyzer.config;

/**
 * 证书分析相关常量定义
 * 统一管理证书字段名、配置键、错误信息等常量
 *
 * <AUTHOR>
 * @date 2024/12/22
 */
public final class CertificateConstants {

    // ==================== 证书字段名常量 ====================

    /** 通用名称 */
    public static final String FIELD_CN = "CN";

    /** 组织名称 */
    public static final String FIELD_O = "O";

    /** 地区/城市 */
    public static final String FIELD_L = "L";

    /** 国家 */
    public static final String FIELD_C = "C";

    /** 州/省 */
    public static final String FIELD_ST = "ST";

    /** 组织单位 */
    public static final String FIELD_OU = "OU";
    
    // ==================== 配置键常量 ====================
    
    /** 知识库服务URL配置键 */
    public static final String CONFIG_KNOWLEDGE_BASE_URL = "knowledge.base.url";
    
    /** 知识库服务默认URL */
    public static final String DEFAULT_KNOWLEDGE_BASE_URL = "http://knowledge-base:8080/knowledge-base";
    
    // ==================== 数据库相关常量 ====================
    
    /** 证书数据库表名 */
    public static final String TABLE_CERT_DB = "cert_db";
    
    /** 黑名单表名 */
    public static final String TABLE_BLACK_LIST = "black_list";
    
    // ==================== 通用常量 ====================
    
    /** 空字符串 */
    public static final String EMPTY_STRING = "";
    
    /** 分隔符 */
    public static final String SEPARATOR = "-";
    
    /** 数字零 */
    public static final String ZERO = "0";
    
    /** 数字一 */
    public static final String ONE = "1";
    
    /** 空值字符串 */
    public static final String NULL_STRING = "null";
    
    /** 键字段名 */
    public static final String FIELD_KEY = "key";
    
    /** 实际值字段名 */
    public static final String FIELD_ACTUAL_VALUE = "actual_value";
    
    // ==================== PEM 格式相关常量 ====================

    /** PEM格式头部标识 */
    public static final String PEM_HEADER = "-----BEGIN CERTIFICATE-----";

    /** PEM格式尾部标识 */
    public static final String PEM_FOOTER = "-----END CERTIFICATE-----";

    /** PEM格式每行字符数 */
    public static final int PEM_LINE_LENGTH = 64;

    // ==================== 证书扩展字段常量 ====================

    /** 主题密钥标识符扩展 */
    public static final String EXTENSION_SUBJECT_KEY_IDENTIFIER = "subjectKeyIdentifier";

    /** 授权密钥标识符扩展 */
    public static final String EXTENSION_AUTHORITY_KEY_IDENTIFIER = "authorityKeyIdentifier";

    /** 基本约束扩展 */
    public static final String EXTENSION_BASIC_CONSTRAINTS = "basicConstraints";

    /** CA证书标识 */
    public static final String CA_TRUE = "CA:TRUE";

    /** 密钥ID前缀 */
    public static final String KEY_ID_PREFIX = "keyid:";

    // ==================== 纠错相关常量 ====================

    /** 默认纠错阈值 */
    public static final int DEFAULT_CORRECTION_THRESHOLD = 10;

    /** 字节序列纠错阈值 */
    public static final int BYTE_SEQUENCE_THRESHOLD = 5;

    /** 块哈希纠错阈值 */
    public static final int CHUNK_HASH_THRESHOLD = 15;

    // ==================== 容量配置常量 ====================

    /** DN解析时Map的初始容量 */
    public static final int DN_MAP_INITIAL_CAPACITY = 8;

    // ==================== 签名算法常量 ====================

    /** MD5 with RSA 签名算法常量 */
    public static final String MD5_WITH_RSA = "MD5withRSAEncryption";

    /** SHA1 with RSA 签名算法常量 */
    public static final String SHA1_WITH_RSA = "SHA1withRSAEncryption";

    /** 证书版本V3常量 */
    public static final String CERT_VERSION_V3 = "v3";

    // ==================== 错误信息常量 ====================

    /** SQL日志格式 */
    public static final String LOG_SQL_FORMAT = "sql——{}";

    // ==================== 私有构造函数 ====================

    private CertificateConstants() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
}
