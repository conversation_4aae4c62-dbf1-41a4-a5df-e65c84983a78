package com.geeksec.certificate.analyzer.output.doris;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.flink.types.Row;
import org.junit.jupiter.api.Test;

import com.geeksec.certificate.analyzer.enums.CertificateSource;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;

/**
 * X509CertToRowConverter测试类
 *
 * <AUTHOR>
 */
class X509CertToRowConverterTest {

    @Test
    void testConvertToRow() {
        // 创建测试证书对象
        X509Certificate cert = createTestCert();

        // 转换为Row
        Row row = CertificateToDorisRowConverter.convertToRow(cert);

        // 验证基本字段
        assertNotNull(row);
        // 验证主键字段
        assertEquals("test_sha1", row.getField("der_sha1"));
        assertEquals("test_md5", row.getField("der_md5"));
        assertEquals("test_sha256", row.getField("der_sha256"));
        assertEquals("3", row.getField("version"));
        assertEquals(0, row.getField("source")); // COLLECTED的ordinal是0
    }

    @Test
    void testSourceTypeConversion() {
        // 测试系统证书
        X509Certificate systemCert = createTestCert();
        systemCert.setSource(CertificateSource.SYSTEM_BUILTIN);
        Row systemRow = CertificateToDorisRowConverter.convertToRow(systemCert);
        assertEquals(2, systemRow.getField("source")); // source应该为2

        // 测试采集证书
        X509Certificate collectedCert = createTestCert();
        collectedCert.setSource(CertificateSource.COLLECTED);
        Row collectedRow = CertificateToDorisRowConverter.convertToRow(collectedCert);
        assertEquals(0, collectedRow.getField("source")); // source应该为0

        // 测试导入证书
        X509Certificate importedCert = createTestCert();
        importedCert.setSource(CertificateSource.IMPORTED);
        Row importedRow = CertificateToDorisRowConverter.convertToRow(importedCert);
        assertEquals(1, importedRow.getField("source")); // source应该为1

        // 测试默认值
        X509Certificate defaultCert = createTestCert();
        defaultCert.setSource(null);
        Row defaultRow = CertificateToDorisRowConverter.convertToRow(defaultCert);
        assertEquals(1, defaultRow.getField("source")); // 默认为1
    }

    @Test
    void testNullHandling() {
        // 测试空值处理
        X509Certificate cert = new X509Certificate(new byte[0]);
        cert.setDerSha1("test_sha1"); // 设置必需的主键

        Row row = CertificateToDorisRowConverter.convertToRow(cert);
        assertNotNull(row);
        assertEquals("test_sha1", row.getField("der_sha1"));
        // 其他字段应该为null或默认值
    }

    /**
     * 创建测试证书对象
     */
    private X509Certificate createTestCert() {
        X509Certificate cert = new X509Certificate(new byte[0]);

        // 设置基础字段
        cert.setDerSha1("test_sha1");
        cert.setDerMd5("test_md5");
        cert.setDerSha256("test_sha256");
        cert.setPemMd5("test_pem_md5");
        cert.setPemSha256("test_pem_sha256");
        cert.setPemSha1("test_pem_sha1");
        cert.setVersion("3");
        cert.setSerialNumber("123456789");
        cert.setFormat("X.509");

        // 设置关联ID
        cert.setIssuerId("issuer_md5");
        cert.setSubjectId("subject_md5");

        // 设置业务字段
        cert.setSource(CertificateSource.COLLECTED);
        cert.setUserCategory("individual");
        cert.setBusinessCategory("web");
        cert.setIssuerCategory("public");
        cert.setWellFormed(true);
        cert.setCorrupted(false);
        cert.setCertOccurrenceCount(1);
        cert.setProcessingMethod(false);

        // 设置评分
        cert.setThreatScore(0);
        cert.setTrustScore(100);
        cert.setThreatLevel("low");

        return cert;
    }
}
