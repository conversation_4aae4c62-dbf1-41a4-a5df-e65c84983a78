package com.geeksec.certificate.analyzer.operator.common.outputtags;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书预处理阶段输出标签
 * 用于证书预处理和分类流程的流控制
 *
 * <AUTHOR>
 */
public final class PreprocessingOutputTags {
    private PreprocessingOutputTags() {
        // 防止实例化
    }

    /** 需要进一步分析的证书标签 */
    public static final OutputTag<X509Certificate> NEED_FURTHER_ANALYSIS =
        new OutputTag<>("need-further-analysis", TypeInformation.of(X509Certificate.class));

    /** 可信证书标签 */
    public static final OutputTag<X509Certificate> TRUSTED_CERTIFICATE =
        new OutputTag<>("trusted-certificate", TypeInformation.of(X509Certificate.class));

    /** 自签名证书标签 */
    public static final OutputTag<X509Certificate> SELF_SIGNED_CERTIFICATE =
        new OutputTag<>("self-signed-certificate", TypeInformation.of(X509Certificate.class));

    // ==================== 证书分类标签 ====================

    /** 正常证书标签 (替代旧的USER_CERTIFICATE和SYSTEM_CERTIFICATE) */
    public static final OutputTag<X509Certificate> NORMAL_CERTIFICATE =
        new OutputTag<>("normal-certificate", TypeInformation.of(X509Certificate.class));

    /** 损坏证书标签 */
    public static final OutputTag<X509Certificate> CORRUPTED_CERTIFICATE =
        new OutputTag<>("corrupted-certificate", TypeInformation.of(X509Certificate.class));

    /** 完整证书标签 */
    public static final OutputTag<X509Certificate> INTACT_CERTIFICATE =
        new OutputTag<>("intact-certificate", TypeInformation.of(X509Certificate.class));
}
