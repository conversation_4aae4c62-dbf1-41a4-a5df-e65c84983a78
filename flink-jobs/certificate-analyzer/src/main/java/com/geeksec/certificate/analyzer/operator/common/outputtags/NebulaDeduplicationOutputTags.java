package com.geeksec.certificate.analyzer.operator.common.outputtags;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Nebula图数据库去重输出标签
 * 用于证书图数据去重流程的流控制
 *
 * <AUTHOR>
 */
public final class NebulaDeduplicationOutputTags {
    private NebulaDeduplicationOutputTags() {
        // 防止实例化
    }

    /** 唯一的Nebula证书数据标签 */
    public static final OutputTag<Row> UNIQUE_NEBULA_CERTIFICATE =
        new OutputTag<>("unique-nebula-certificate", TypeInformation.of(Row.class));

    /** 重复的Nebula证书数据标签 */
    public static final OutputTag<Row> DUPLICATE_NEBULA_CERTIFICATE =
        new OutputTag<>("duplicate-nebula-certificate", TypeInformation.of(Row.class));

    /** 重复的Nebula颁发者数据标签 */
    public static final OutputTag<Row> DUPLICATE_NEBULA_ISSUER =
        new OutputTag<>("duplicate-nebula-issuer", TypeInformation.of(Row.class));

    /** 重复的Nebula主题数据标签 */
    public static final OutputTag<Row> DUPLICATE_NEBULA_SUBJECT =
        new OutputTag<>("duplicate-nebula-subject", TypeInformation.of(Row.class));

    /** 重复的Nebula URL数据标签 */
    public static final OutputTag<Row> DUPLICATE_NEBULA_URL =
        new OutputTag<>("duplicate-nebula-url", TypeInformation.of(Row.class));

    /** 重复的Nebula组织数据标签 */
    public static final OutputTag<Row> DUPLICATE_NEBULA_ORGANIZATION =
        new OutputTag<>("duplicate-nebula-organization", TypeInformation.of(Row.class));
}
