package com.geeksec.certificate.analyzer.sink.doris;

import java.util.List;

import com.geeksec.certificate.analyzer.model.extension.CertificateExtensionData;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书扩展字段提取器
 * 用于从CertificateExtensionData中提取和转换扩展字段信息
 *
 * <AUTHOR>
 */
@Slf4j
public final class CertificateExtensionExtractor {

    /**
     * 私有构造函数，防止实例化
     */
    private CertificateExtensionExtractor() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 从扩展信息中提取扩展密钥用途
     *
     * @param extensionData 证书扩展数据对象
     * @return 扩展密钥用途数组
     */
    public static String[] extractExtendedKeyUsage(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getExtendedKeyUsage() == null) {
            return null;
        }

        List<String> extendedKeyUsage = extensionData.getExtendedKeyUsage();
        if (extendedKeyUsage.isEmpty()) {
            return null;
        }

        // 转换为数组
        return extendedKeyUsage.toArray(new String[0]);
    }

    /**
     * 从扩展信息中提取CRL分发点
     *
     * @param extensionData 证书扩展数据对象
     * @return CRL分发点数组
     */
    public static String[] extractCrlDistributionPoints(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getCrlDistributionPoints() == null) {
            return null;
        }

        List<String> crlDistPoints = extensionData.getCrlDistributionPoints();
        if (crlDistPoints.isEmpty()) {
            return null;
        }

        return crlDistPoints.toArray(new String[0]);
    }

    /**
     * 从扩展信息中提取OCSP URI
     *
     * @param extensionData 证书扩展数据对象
     * @return OCSP URI数组
     */
    public static String[] extractOcspUris(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getOcspUris() == null) {
            return null;
        }

        List<String> ocspUris = extensionData.getOcspUris();
        if (ocspUris.isEmpty()) {
            return null;
        }

        return ocspUris.toArray(new String[0]);
    }

    /**
     * 从扩展信息中提取CA颁发者URI
     *
     * @param extensionData 证书扩展数据对象
     * @return CA颁发者URI数组
     */
    public static String[] extractCaIssuersUris(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getCaIssuersUris() == null) {
            return null;
        }

        List<String> caIssuersUris = extensionData.getCaIssuersUris();
        if (caIssuersUris.isEmpty()) {
            return null;
        }

        return caIssuersUris.toArray(new String[0]);
    }

    /**
     * 从扩展信息中提取证书策略OID
     *
     * @param extensionData 证书扩展数据对象
     * @return 证书策略OID数组
     */
    public static String[] extractCertificatePolicies(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getCertificatePoliciesOids() == null) {
            return null;
        }

        List<String> policyOids = extensionData.getCertificatePoliciesOids();
        if (policyOids.isEmpty()) {
            return null;
        }

        return policyOids.toArray(new String[0]);
    }

    /**
     * 从扩展信息中提取主题备用名称
     *
     * @param extensionData 证书扩展数据对象
     * @return 主题备用名称数组
     */
    public static String[] extractSubjectAltNames(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getSubjectAltName() == null) {
            return null;
        }

        List<String> subjectAltNames = extensionData.getSubjectAltName();
        if (subjectAltNames.isEmpty()) {
            return null;
        }

        return subjectAltNames.toArray(new String[0]);
    }

    /**
     * 检查证书是否为CA证书
     *
     * @param extensionData 证书扩展数据对象
     * @return 是否为CA证书
     */
    public static Boolean isCA(CertificateExtensionData extensionData) {
        if (extensionData == null) {
            return false;
        }

        return extensionData.getCa();
    }

    /**
     * 提取路径长度约束
     *
     * @param extensionData 证书扩展数据对象
     * @return 路径长度约束
     */
    public static Integer extractPathLength(CertificateExtensionData extensionData) {
        if (extensionData == null) {
            return null;
        }

        return extensionData.getPathLenConstraint();
    }

    /**
     * 从扩展信息中提取TLS特性
     *
     * @param extensionData 证书扩展数据对象
     * @return TLS特性数组
     */
    public static String[] extractTlsFeatures(CertificateExtensionData extensionData) {
        if (extensionData == null || extensionData.getTlsFeatures() == null) {
            return null;
        }

        List<String> tlsFeatures = extensionData.getTlsFeatures();
        if (tlsFeatures.isEmpty()) {
            return null;
        }

        return tlsFeatures.toArray(new String[0]);
    }
}
