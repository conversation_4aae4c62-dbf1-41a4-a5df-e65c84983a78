package com.geeksec.session.threat.detector.control;

import com.geeksec.session.threat.detector.model.enums.DetectorTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 检测器开关配置类
 * 管理所有检测器的启用/禁用状态
 * 
 * <AUTHOR>
 */
@Data
@Slf4j
public class DetectorSwitchConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 检测器开关状态映射
     * Key: 检测器ID, Value: 开关状态 (1=启用, 0=禁用)
     */
    private final Map<Integer, Integer> detectorSwitches;
    
    /**
     * 默认开关状态 (1=启用, 0=禁用)
     */
    private static final int DEFAULT_SWITCH_STATE = 1;
    
    /**
     * 开关状态常量
     */
    public static final int SWITCH_ENABLED = 1;
    public static final int SWITCH_DISABLED = 0;
    
    /**
     * 构造函数 - 初始化所有检测器为默认状态
     */
    public DetectorSwitchConfig() {
        this.detectorSwitches = new ConcurrentHashMap<>();
        initializeDefaultSwitches();
    }
    
    /**
     * 构造函数 - 使用指定的开关配置
     * 
     * @param detectorSwitches 检测器开关配置
     */
    public DetectorSwitchConfig(Map<Integer, Integer> detectorSwitches) {
        this.detectorSwitches = new ConcurrentHashMap<>(detectorSwitches);
        // 确保所有检测器都有配置
        fillMissingDetectors();
    }
    
    /**
     * 初始化所有检测器为默认状态
     */
    private void initializeDefaultSwitches() {
        for (DetectorTypeEnum detectorType : DetectorTypeEnum.values()) {
            detectorSwitches.put(detectorType.getDetectorId(), DEFAULT_SWITCH_STATE);
        }
        log.info("初始化检测器开关配置，共 {} 个检测器，默认状态: {}", 
                detectorSwitches.size(), DEFAULT_SWITCH_STATE == SWITCH_ENABLED ? "启用" : "禁用");
    }
    
    /**
     * 填充缺失的检测器配置
     */
    private void fillMissingDetectors() {
        for (DetectorTypeEnum detectorType : DetectorTypeEnum.values()) {
            detectorSwitches.putIfAbsent(detectorType.getDetectorId(), DEFAULT_SWITCH_STATE);
        }
    }
    
    /**
     * 检查检测器是否启用
     * 
     * @param detectorId 检测器ID
     * @return 是否启用
     */
    public boolean isDetectorEnabled(Integer detectorId) {
        Integer switchState = detectorSwitches.get(detectorId);
        if (switchState == null) {
            log.warn("检测器ID {} 未找到配置，使用默认状态: {}", detectorId, DEFAULT_SWITCH_STATE);
            return DEFAULT_SWITCH_STATE == SWITCH_ENABLED;
        }
        return switchState == SWITCH_ENABLED;
    }
    
    /**
     * 检查检测器是否启用（通过检测器类型）
     * 
     * @param detectorType 检测器类型
     * @return 是否启用
     */
    public boolean isDetectorEnabled(DetectorTypeEnum detectorType) {
        return isDetectorEnabled(detectorType.getDetectorId());
    }
    
    /**
     * 检查检测器是否启用（通过检测器名称）
     * 
     * @param detectorName 检测器名称
     * @return 是否启用
     */
    public boolean isDetectorEnabled(String detectorName) {
        DetectorTypeEnum detectorType = DetectorTypeEnum.getByDetectorName(detectorName);
        if (detectorType == null) {
            log.warn("检测器名称 {} 未找到对应类型，使用默认状态: {}", detectorName, DEFAULT_SWITCH_STATE);
            return DEFAULT_SWITCH_STATE == SWITCH_ENABLED;
        }
        return isDetectorEnabled(detectorType);
    }
    
    /**
     * 设置检测器开关状态
     * 
     * @param detectorId 检测器ID
     * @param enabled 是否启用
     */
    public void setDetectorSwitch(Integer detectorId, boolean enabled) {
        int switchState = enabled ? SWITCH_ENABLED : SWITCH_DISABLED;
        Integer oldState = detectorSwitches.put(detectorId, switchState);
        
        DetectorTypeEnum detectorType = DetectorTypeEnum.getByDetectorId(detectorId);
        String detectorName = detectorType != null ? detectorType.getDetectorName() : "未知检测器";
        
        log.info("检测器开关状态变更: {} (ID: {}) {} -> {}", 
                detectorName, detectorId,
                oldState == null ? "未配置" : (oldState == SWITCH_ENABLED ? "启用" : "禁用"),
                enabled ? "启用" : "禁用");
    }
    
    /**
     * 设置检测器开关状态（通过检测器类型）
     * 
     * @param detectorType 检测器类型
     * @param enabled 是否启用
     */
    public void setDetectorSwitch(DetectorTypeEnum detectorType, boolean enabled) {
        setDetectorSwitch(detectorType.getDetectorId(), enabled);
    }
    
    /**
     * 批量更新检测器开关状态
     * 
     * @param switchUpdates 开关更新映射 (检测器ID -> 开关状态)
     */
    public void updateDetectorSwitches(Map<Integer, Integer> switchUpdates) {
        if (switchUpdates == null || switchUpdates.isEmpty()) {
            log.warn("检测器开关更新为空，跳过更新");
            return;
        }
        
        log.info("批量更新检测器开关状态，更新数量: {}", switchUpdates.size());
        
        for (Map.Entry<Integer, Integer> entry : switchUpdates.entrySet()) {
            Integer detectorId = entry.getKey();
            Integer switchState = entry.getValue();
            
            if (switchState != SWITCH_ENABLED && switchState != SWITCH_DISABLED) {
                log.warn("检测器ID {} 的开关状态值无效: {}，跳过更新", detectorId, switchState);
                continue;
            }
            
            setDetectorSwitch(detectorId, switchState == SWITCH_ENABLED);
        }
    }
    
    /**
     * 启用所有检测器
     */
    public void enableAllDetectors() {
        log.info("启用所有检测器");
        for (Integer detectorId : detectorSwitches.keySet()) {
            detectorSwitches.put(detectorId, SWITCH_ENABLED);
        }
    }
    
    /**
     * 禁用所有检测器
     */
    public void disableAllDetectors() {
        log.info("禁用所有检测器");
        for (Integer detectorId : detectorSwitches.keySet()) {
            detectorSwitches.put(detectorId, SWITCH_DISABLED);
        }
    }
    
    /**
     * 获取启用的检测器数量
     * 
     * @return 启用的检测器数量
     */
    public int getEnabledDetectorCount() {
        return (int) detectorSwitches.values().stream()
                .filter(state -> state == SWITCH_ENABLED)
                .count();
    }
    
    /**
     * 获取禁用的检测器数量
     * 
     * @return 禁用的检测器数量
     */
    public int getDisabledDetectorCount() {
        return (int) detectorSwitches.values().stream()
                .filter(state -> state == SWITCH_DISABLED)
                .count();
    }
    
    /**
     * 获取所有检测器的开关状态摘要
     * 
     * @return 开关状态摘要
     */
    public Map<String, Object> getSwitchSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalDetectors", detectorSwitches.size());
        summary.put("enabledDetectors", getEnabledDetectorCount());
        summary.put("disabledDetectors", getDisabledDetectorCount());
        summary.put("detectorSwitches", new HashMap<>(detectorSwitches));
        return summary;
    }
    
    /**
     * 打印检测器开关状态
     */
    public void printSwitchStatus() {
        log.info("检测器开关状态摘要:");
        log.info("  总检测器数量: {}", detectorSwitches.size());
        log.info("  启用检测器数量: {}", getEnabledDetectorCount());
        log.info("  禁用检测器数量: {}", getDisabledDetectorCount());
        
        if (log.isDebugEnabled()) {
            log.debug("详细开关状态:");
            for (Map.Entry<Integer, Integer> entry : detectorSwitches.entrySet()) {
                Integer detectorId = entry.getKey();
                Integer switchState = entry.getValue();
                DetectorTypeEnum detectorType = DetectorTypeEnum.getByDetectorId(detectorId);
                String detectorName = detectorType != null ? detectorType.getDetectorName() : "未知检测器";
                
                log.debug("  {} (ID: {}): {}", detectorName, detectorId, 
                        switchState == SWITCH_ENABLED ? "启用" : "禁用");
            }
        }
    }
    
    /**
     * 创建配置副本
     * 
     * @return 配置副本
     */
    public DetectorSwitchConfig copy() {
        return new DetectorSwitchConfig(new HashMap<>(this.detectorSwitches));
    }
}
