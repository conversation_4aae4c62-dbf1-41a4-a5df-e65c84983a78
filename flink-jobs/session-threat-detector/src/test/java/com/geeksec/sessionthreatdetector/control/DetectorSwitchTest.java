package com.geeksec.session.threat.detector.control;

import com.geeksec.session.threat.detector.model.enums.DetectorTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 检测器开关控制机制测试
 * 
 * <AUTHOR>
 */
public class DetectorSwitchTest {
    
    private static final Logger log = LoggerFactory.getLogger(DetectorSwitchTest.class);
    
    private DetectorSwitchManager switchManager;
    
    @BeforeEach
    void setUp() {
        switchManager = DetectorSwitchManager.getInstance();
        switchManager.initialize();
    }
    
    @Test
    void testDetectorSwitchConfig() {
        log.info("测试检测器开关配置");
        
        // 测试默认配置
        DetectorSwitchConfig defaultConfig = new DetectorSwitchConfig();
        assertTrue(defaultConfig.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT));
        assertTrue(defaultConfig.isDetectorEnabled(DetectorTypeEnum.WEBSHELL));
        
        // 测试自定义配置
        Map<Integer, Integer> customSwitches = new HashMap<>();
        customSwitches.put(DetectorTypeEnum.SSL_FINGERPRINT.getDetectorId(), DetectorSwitchConfig.SWITCH_ENABLED);
        customSwitches.put(DetectorTypeEnum.WEBSHELL.getDetectorId(), DetectorSwitchConfig.SWITCH_DISABLED);
        
        DetectorSwitchConfig customConfig = new DetectorSwitchConfig(customSwitches);
        assertTrue(customConfig.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT));
        assertFalse(customConfig.isDetectorEnabled(DetectorTypeEnum.WEBSHELL));
        
        log.info("检测器开关配置测试通过");
    }
    
    @Test
    void testDetectorSwitchManager() {
        log.info("测试检测器开关管理器");
        
        // 测试默认状态
        assertTrue(switchManager.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT));
        assertTrue(switchManager.isDetectorEnabled(DetectorTypeEnum.WEBSHELL));
        
        // 测试禁用检测器
        switchManager.disableDetector(DetectorTypeEnum.WEBSHELL.getDetectorId());
        assertFalse(switchManager.isDetectorEnabled(DetectorTypeEnum.WEBSHELL));
        
        // 测试启用检测器
        switchManager.enableDetector(DetectorTypeEnum.WEBSHELL.getDetectorId());
        assertTrue(switchManager.isDetectorEnabled(DetectorTypeEnum.WEBSHELL));
        
        // 测试批量更新
        Map<Integer, Integer> updates = new HashMap<>();
        updates.put(DetectorTypeEnum.SSL_FINGERPRINT.getDetectorId(), DetectorSwitchConfig.SWITCH_DISABLED);
        updates.put(DetectorTypeEnum.PORT_SCAN.getDetectorId(), DetectorSwitchConfig.SWITCH_ENABLED);
        
        switchManager.updateDetectorSwitches(updates);
        assertFalse(switchManager.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT));
        assertTrue(switchManager.isDetectorEnabled(DetectorTypeEnum.PORT_SCAN));
        
        log.info("检测器开关管理器测试通过");
    }
    
    @Test
    void testDetectorTypeEnum() {
        log.info("测试检测器类型枚举");
        
        // 测试根据ID获取枚举
        DetectorTypeEnum sslFingerprint = DetectorTypeEnum.getByDetectorId(99016);
        assertNotNull(sslFingerprint);
        assertEquals(DetectorTypeEnum.SSL_FINGERPRINT, sslFingerprint);
        
        // 测试根据名称获取枚举
        DetectorTypeEnum webshell = DetectorTypeEnum.getByDetectorName("WebShell检测器");
        assertNotNull(webshell);
        assertEquals(DetectorTypeEnum.WEBSHELL, webshell);
        
        // 测试检查ID是否存在
        assertTrue(DetectorTypeEnum.existsDetectorId(99016));
        assertFalse(DetectorTypeEnum.existsDetectorId(99999));
        
        // 测试获取所有ID
        Integer[] allIds = DetectorTypeEnum.getAllDetectorIds();
        assertTrue(allIds.length > 0);
        
        log.info("检测器类型枚举测试通过");
    }
    
    @Test
    void testDetectorSwitchConfigLoader() {
        log.info("测试检测器开关配置加载器");
        
        // 测试从默认文件加载
        DetectorSwitchConfig config = DetectorSwitchConfigLoader.loadFromDefaultFile();
        assertNotNull(config);
        
        // 测试创建默认配置
        DetectorSwitchConfig defaultConfig = DetectorSwitchConfigLoader.createDefaultConfig();
        assertNotNull(defaultConfig);
        assertTrue(defaultConfig.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT));
        
        // 测试创建测试配置
        DetectorSwitchConfig testConfig = DetectorSwitchConfigLoader.createTestConfig();
        assertNotNull(testConfig);
        assertTrue(testConfig.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT));
        assertTrue(testConfig.isDetectorEnabled(DetectorTypeEnum.WEBSHELL));
        
        // 测试配置验证
        assertTrue(DetectorSwitchConfigLoader.validateConfig(defaultConfig));
        assertTrue(DetectorSwitchConfigLoader.validateConfig(testConfig));
        
        log.info("检测器开关配置加载器测试通过");
    }
    
    @Test
    void testConfigSummary() {
        log.info("测试配置摘要");
        
        DetectorSwitchConfig config = new DetectorSwitchConfig();
        
        // 测试获取摘要
        Map<String, Object> summary = config.getSwitchSummary();
        assertNotNull(summary);
        assertTrue(summary.containsKey("totalDetectors"));
        assertTrue(summary.containsKey("enabledDetectors"));
        assertTrue(summary.containsKey("disabledDetectors"));
        
        // 测试统计信息
        assertTrue(config.getEnabledDetectorCount() > 0);
        assertEquals(0, config.getDisabledDetectorCount());
        
        // 禁用一些检测器后再测试
        config.setDetectorSwitch(DetectorTypeEnum.SSL_FINGERPRINT, false);
        config.setDetectorSwitch(DetectorTypeEnum.WEBSHELL, false);
        
        assertEquals(2, config.getDisabledDetectorCount());
        
        log.info("配置摘要测试通过");
    }
    
    @Test
    void testInvalidOperations() {
        log.info("测试无效操作");
        
        DetectorSwitchConfig config = new DetectorSwitchConfig();
        
        // 测试无效的检测器ID
        assertFalse(config.isDetectorEnabled(99999));
        
        // 测试无效的检测器名称
        assertFalse(config.isDetectorEnabled("不存在的检测器"));
        
        // 测试空的更新
        Map<Integer, Integer> emptyUpdates = new HashMap<>();
        config.updateDetectorSwitches(emptyUpdates);
        
        // 测试null更新
        config.updateDetectorSwitches(null);
        
        log.info("无效操作测试通过");
    }
    
    @Test
    void testManagerStatistics() {
        log.info("测试管理器统计信息");
        
        // 打印当前状态
        switchManager.printCurrentStatus();
        
        // 获取统计信息
        String statistics = switchManager.getStatistics();
        assertNotNull(statistics);
        assertTrue(statistics.contains("检测器统计"));
        
        // 获取摘要
        Map<String, Object> summary = switchManager.getSwitchSummary();
        assertNotNull(summary);
        
        log.info("管理器统计信息测试通过");
    }
}
