package com.geeksec.knowledgebase.repository.mapper;

import com.geeksec.knowledgebase.domain.entity.ThreatIntelligence;
import com.geeksec.knowledgebase.domain.enums.ThreatLevel;
import com.geeksec.knowledgebase.domain.enums.ThreatType;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * 威胁情报 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ThreatIntelligenceMapper extends BaseMapper<ThreatIntelligence> {

    /**
     * 根据IOC值查找威胁情报
     *
     * @param iocValue IOC值
     * @return 威胁情报
     */
    default Optional<ThreatIntelligence> findByIocValue(String iocValue) {
        ThreatIntelligence threat = selectOneByQuery(QueryWrapper.create()
                .where("ioc_value = ?", iocValue)
                .and("is_active = ?", true));
        return Optional.ofNullable(threat);
    }

    /**
     * 根据威胁名称查找威胁情报
     *
     * @param threatName 威胁名称
     * @return 威胁情报
     */
    default Optional<ThreatIntelligence> findByThreatName(String threatName) {
        ThreatIntelligence threat = selectOneByQuery(QueryWrapper.create()
                .where("threat_name = ?", threatName)
                .and("is_active = ?", true));
        return Optional.ofNullable(threat);
    }

    /**
     * 根据威胁类型查询威胁情报
     * 
     * @param threatType 威胁类型
     * @return 威胁情报列表
     */
    default List<ThreatIntelligence> selectByThreatType(ThreatType threatType) {
        return selectListByQuery(QueryWrapper.create()
                .where(ThreatIntelligence::getThreatType).eq(threatType)
                .and(ThreatIntelligence::getActive).eq(true));
    }

    /**
     * 根据威胁等级查询威胁情报
     * 
     * @param severity 威胁等级
     * @return 威胁情报列表
     */
    default List<ThreatIntelligence> selectBySeverity(ThreatLevel severity) {
        return selectListByQuery(QueryWrapper.create()
                .where(ThreatIntelligence::getSeverity).eq(severity)
                .and(ThreatIntelligence::getActive).eq(true));
    }

    /**
     * 分页查询威胁情报
     * 
     * @param page 分页参数
     * @param threatType 威胁类型（可选）
     * @param severity 威胁等级（可选）
     * @return 分页结果
     */
    default Page<ThreatIntelligence> selectPageByCondition(Page<ThreatIntelligence> page, 
                                                          @Param("threatType") ThreatType threatType,
                                                          @Param("severity") ThreatLevel severity) {
        QueryWrapper query = QueryWrapper.create()
                .where(ThreatIntelligence::getIsActive).eq(true);
        
        if (threatType != null) {
            query.and(ThreatIntelligence::getThreatType).eq(threatType);
        }
        
        if (severity != null) {
            query.and(ThreatIntelligence::getSeverity).eq(severity);
        }
        
        return paginate(page, query);
    }

    /**
     * 根据关键词搜索威胁情报
     * 
     * @param keyword 关键词
     * @return 威胁情报列表
     */
    default List<ThreatIntelligence> searchByKeyword(String keyword) {
        return selectListByQuery(QueryWrapper.create()
                .where(ThreatIntelligence::getThreatName).like(keyword)
                .or(ThreatIntelligence::getDescription).like(keyword)
                .or(ThreatIntelligence::getCategory).like(keyword)
                .and(ThreatIntelligence::getActive).eq(true));
    }
}
