package com.geeksec.session.threat.detector.output;

import com.geeksec.session.threat.detector.job.pipeline.ThreatDetectionPipeline;
import com.geeksec.session.threat.detector.output.asset.AssetLabelOutputManager;
import com.geeksec.session.threat.detector.output.notification.NotificationOutputManager;
import com.geeksec.session.threat.detector.output.session.SessionLabelOutputManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

/**
 * 输出管理器
 * 统一管理四种输出的配置和处理
 *
 * <AUTHOR>
 */
@Slf4j
public class OutputManager {

    /**
     * 私有构造方法，防止实例化
     */
    private OutputManager() {
        // 工具类，禁止实例化
    }

    /**
     * 配置所有输出
     *
     * @param pipelineResult 流水线结果
     * @param config 配置参数
     */
    public static void configureAllOutputs(
            ThreatDetectionPipeline.PipelineResult pipelineResult,
            ParameterTool config) {

        log.info("开始配置所有输出");

        try {
            // 1. 配置会话标签输出（更新Doris会话表）
            configureSessionLabelOutput(pipelineResult, config);

            // 2. 配置资产标签输出（写入Nebula图数据库）
            configureAssetLabelOutput(pipelineResult, config);

            // 3. 告警事件输出已迁移到统一告警处理系统 (alarm-processor)
            log.info("告警事件输出已迁移到统一告警处理系统");

            // 4. 配置通知消息输出（发送到Kafka）
            configureNotificationOutput(pipelineResult, config);

            log.info("所有输出配置完成");

        } catch (Exception e) {
            log.error("配置输出时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("输出配置失败", e);
        }
    }

    /**
     * 配置会话标签输出
     *
     * @param pipelineResult 流水线结果
     * @param config 配置参数
     */
    private static void configureSessionLabelOutput(
            ThreatDetectionPipeline.PipelineResult pipelineResult,
            ParameterTool config) {

        if (pipelineResult.getSessionLabelStream() == null) {
            log.warn("会话标签数据流为null，跳过配置");
            return;
        }

        log.info("配置会话标签输出");
        SessionLabelOutputManager.configure(pipelineResult.getSessionLabelStream(), config);
    }

    /**
     * 配置资产标签输出
     *
     * @param pipelineResult 流水线结果
     * @param config 配置参数
     */
    private static void configureAssetLabelOutput(
            ThreatDetectionPipeline.PipelineResult pipelineResult,
            ParameterTool config) {

        if (pipelineResult.getAssetLabelStream() == null) {
            log.warn("资产标签数据流为null，跳过配置");
            return;
        }

        log.info("配置资产标签输出");
        AssetLabelOutputManager.configure(pipelineResult.getAssetLabelStream(), config);
    }

    /**
     * 配置告警事件输出（已迁移到统一告警处理系统）
     *
     * @deprecated 告警事件输出已迁移到 alarm-processor 模块统一处理
     */
    @Deprecated
    private static void configureAlarmEventOutput(
            ThreatDetectionPipeline.PipelineResult pipelineResult,
            ParameterTool config) {

        log.info("告警事件输出已迁移到统一告警处理系统 (alarm-processor)");
        log.info("threat-detector 模块将生成的告警事件发送到 Kafka，由 alarm-processor 统一处理");
    }

    /**
     * 配置通知消息输出
     *
     * @param pipelineResult 流水线结果
     * @param config 配置参数
     */
    private static void configureNotificationOutput(
            ThreatDetectionPipeline.PipelineResult pipelineResult,
            ParameterTool config) {

        if (pipelineResult.getNotificationStream() == null) {
            log.warn("通知消息数据流为null，跳过配置");
            return;
        }

        log.info("配置通知消息输出");
        NotificationOutputManager.configure(pipelineResult.getNotificationStream(), config);
    }
}
