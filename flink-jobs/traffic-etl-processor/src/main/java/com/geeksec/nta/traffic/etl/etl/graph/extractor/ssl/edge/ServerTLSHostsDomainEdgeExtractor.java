package com.geeksec.nta.traffic.etl.etl.graph.extractor.ssl.edge;

import com.geeksec.common.network.DomainUtils;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class ServerTLSHostsDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.SERVER_TLS_HOSTS_DOMAIN_TAG;
    }

    /**
     * TLS服务端托管域名 (目的IP -> SNI DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();

        if (!DomainUtils.isValidDomain(sni) || sni.equals(dIP)){
            return Collections.emptyList();
        }

        sni = DomainUtils.formatDomain(sni);
        return List.of(Row.of(dIP, sni,
                0 // rank暂定0
        ));

    }
}
