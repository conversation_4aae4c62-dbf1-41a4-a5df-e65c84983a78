package com.geeksec.certificate.analyzer.operator.analysis.security;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 证书安全分析器
 * 从 threat-detector 模块迁移的综合证书安全分析功能
 * 对证书进行全面的安全性分析和风险评估
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateSecurityAnalyzer {
    
    private static final String ANALYZER_VERSION = "3.0.0";
    
    // 弱密钥长度阈值
    private static final int MIN_RSA_KEY_LENGTH = 2048;
    private static final int MIN_EC_KEY_LENGTH = 256;
    
    // 不安全的签名算法
    private static final List<String> INSECURE_ALGORITHMS = Arrays.asList(
            "MD5withRSA", "SHA1withRSA", "MD2withRSA", "MD4withRSA"
    );
    
    // 过期警告天数
    private static final int EXPIRY_WARNING_DAYS = 30;
    
    /**
     * 分析证书安全性并添加相应标签
     * 
     * @param certificate 证书对象
     */
    public void analyzeCertificateSecurity(X509Certificate certificate) {
        if (certificate == null) {
            return;
        }
        
        log.debug("开始分析证书安全性: {}", certificate.getDerSha1());
        
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }
        
        // 执行各项安全检查
        performKeyStrengthCheck(certificate, labels);
        performAlgorithmSecurityCheck(certificate, labels);
        performValidityPeriodCheck(certificate, labels);
        performCertificateChainCheck(certificate, labels);
        performExtensionsCheck(certificate, labels);
        performAnomalyDetection(certificate, labels);
        
        certificate.setLabels(labels);
        
        log.debug("证书安全分析完成: {}", certificate.getDerSha1());
    }
    
    /**
     * 密钥强度检查
     */
    private void performKeyStrengthCheck(X509Certificate certificate, Set<CertificateLabel> labels) {
        String algorithm = certificate.getPublicKeyAlgorithm();
        Integer keyLength = certificate.getKeyLength();
        
        if (algorithm == null || keyLength == null) {
            return;
        }
        
        if ("RSA".equals(algorithm)) {
            if (keyLength < MIN_RSA_KEY_LENGTH) {
                labels.add(CertificateLabel.WEAK_KEY);
                log.debug("检测到弱RSA密钥: {} bits", keyLength);
            }
        } else if ("EC".equals(algorithm) || algorithm.startsWith("EC")) {
            if (keyLength < MIN_EC_KEY_LENGTH) {
                labels.add(CertificateLabel.WEAK_KEY);
                log.debug("检测到弱EC密钥: {} bits", keyLength);
            }
        }
    }
    
    /**
     * 算法安全检查
     */
    private void performAlgorithmSecurityCheck(X509Certificate certificate, Set<CertificateLabel> labels) {
        String signatureAlgorithm = certificate.getSignatureAlgName();
        
        if (signatureAlgorithm != null && INSECURE_ALGORITHMS.contains(signatureAlgorithm)) {
            labels.add(CertificateLabel.INSECURE_ALGORITHM);
            log.debug("检测到不安全的签名算法: {}", signatureAlgorithm);
        }
    }
    
    /**
     * 有效期检查
     */
    private void performValidityPeriodCheck(X509Certificate certificate, Set<CertificateLabel> labels) {
        LocalDateTime notBefore = certificate.getNotBefore();
        LocalDateTime notAfter = certificate.getNotAfter();
        LocalDateTime now = LocalDateTime.now();
        
        if (notBefore == null || notAfter == null) {
            return;
        }
        
        // 检查是否过期
        if (now.isAfter(notAfter)) {
            labels.add(CertificateLabel.EXPIRED_CERT);
            log.debug("检测到过期证书，过期时间: {}", notAfter);
        }
        
        // 检查是否即将过期
        else if (ChronoUnit.DAYS.between(now, notAfter) <= EXPIRY_WARNING_DAYS) {
            labels.add(CertificateLabel.EXPIRING_SOON);
            log.debug("检测到即将过期证书，过期时间: {}", notAfter);
        }
        
        // 检查是否还未生效
        if (now.isBefore(notBefore)) {
            labels.add(CertificateLabel.NOT_YET_VALID);
            log.debug("检测到未生效证书，生效时间: {}", notBefore);
        }
    }
    
    /**
     * 证书链检查
     */
    private void performCertificateChainCheck(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 检查是否为自签名证书
        if (Boolean.TRUE.equals(certificate.getIsSelfSigned())) {
            labels.add(CertificateLabel.SELF_SIGNED_CERT);
            log.debug("检测到自签名证书");
        }
    }
    
    /**
     * 扩展检查
     */
    private void performExtensionsCheck(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 检查关键扩展
        List<String> criticalExtensions = certificate.getCriticalExtensionOIDs();
        if (criticalExtensions != null && !criticalExtensions.isEmpty()) {
            // 检查是否有未知的关键扩展
            for (String oid : criticalExtensions) {
                if (isUnknownCriticalExtension(oid)) {
                    labels.add(CertificateLabel.UNKNOWN_CRITICAL_EXTENSION);
                    log.debug("检测到未知关键扩展: {}", oid);
                    break;
                }
            }
        }
    }
    
    /**
     * 异常检测
     */
    private void performAnomalyDetection(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 检查序列号异常
        String serialNumber = certificate.getSerialNumber();
        if (serialNumber != null && isAnomalousSerialNumber(serialNumber)) {
            labels.add(CertificateLabel.ANOMALOUS_SERIAL);
            log.debug("检测到异常序列号: {}", serialNumber);
        }
        
        // 检查主题异常
        if (hasAnomalousSubject(certificate)) {
            labels.add(CertificateLabel.ANOMALOUS_SUBJECT);
            log.debug("检测到异常主题信息");
        }
    }
    
    /**
     * 检查是否为未知的关键扩展
     */
    private boolean isUnknownCriticalExtension(String oid) {
        // 常见的关键扩展OID
        Set<String> knownCriticalExtensions = Set.of(
            "2.5.29.15", // Key Usage
            "2.5.29.19", // Basic Constraints
            "2.5.29.32", // Certificate Policies
            "2.5.29.17"  // Subject Alternative Name
        );
        return !knownCriticalExtensions.contains(oid);
    }
    
    /**
     * 检查序列号是否异常
     */
    private boolean isAnomalousSerialNumber(String serialNumber) {
        // 检查序列号长度和格式
        if (serialNumber.length() < 2 || serialNumber.length() > 40) {
            return true;
        }
        
        // 检查是否为全零或全一
        return serialNumber.matches("^0+$") || serialNumber.matches("^[fF]+$");
    }
    
    /**
     * 检查主题信息是否异常
     */
    private boolean hasAnomalousSubject(X509Certificate certificate) {
        Map<String, Object> subject = certificate.getSubject();
        if (subject == null || subject.isEmpty()) {
            return true;
        }
        
        // 检查CN字段
        Object cn = subject.get("CN");
        if (cn != null) {
            String cnStr = cn.toString();
            // 检查是否包含可疑字符或模式
            if (cnStr.contains("..") || cnStr.startsWith(".") || cnStr.endsWith(".")) {
                return true;
            }
        }
        
        return false;
    }
}
