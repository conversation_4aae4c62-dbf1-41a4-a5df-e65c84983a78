package com.geeksec.certificate.analyzer.model.extension;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 不常见OID模型类
 * 用于表示证书中不常见的对象标识符
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2023/12/19
 */
@Data
public class UncommonOID {
    /**
     * 目标OID：OID
     */
    @JSONField(name = "OID")
    private String OID;

    /**
     * 目标所属的字段
     */
    @JSONField(name = "Type")
    private String Type;

    /**
     * 目标OID的详情
     */
    @JSONField(name = "Description")
    private String Description;

    /**
     * 默认构造函数
     */
    public UncommonOID() {
    }

    /**
     * 构造函数
     *
     * @param oid         OID字符串
     * @param type        类型
     * @param description 描述
     */
    public UncommonOID(String oid, String type, String description) {
        this.OID = oid;
        this.Type = type;
        this.Description = description;
    }
}
