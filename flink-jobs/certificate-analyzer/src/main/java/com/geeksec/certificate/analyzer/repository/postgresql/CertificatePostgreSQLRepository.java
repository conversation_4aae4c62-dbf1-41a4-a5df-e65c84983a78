package com.geeksec.certificate.analyzer.repository.postgresql;

import java.sql.Array;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.model.threat.ThreatInfo;
import com.geeksec.common.database.postgresql.PostgreSQLConnectionManager;

/**
 * 证书PostgreSQL数据库操作类
 * 提供证书相关的数据库操作功能
 *
 * <AUTHOR>
 */
public class CertificatePostgreSQLRepository {

    protected static final Logger LOG = LoggerFactory.getLogger(CertificatePostgreSQLRepository.class);

    //706证书导入
    public static int setCertLoadStatus(String SHA1, Integer status, List<String> labels_set, Integer black_list) throws SQLException {
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        PreparedStatement update_preparedStatement = null;
        ResultSet selectResultSet = null;
        int rowsAffected = 0;
        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");

            String select_query = "SELECT upload_status,update_time,labels,black_list FROM tb_cert_upload_file WHERE cert_sha1=? AND upload_status=? LIMIT 1";
            select_preparedStatement = connection.prepareStatement(select_query);
            select_preparedStatement.setString(1, SHA1);
            select_preparedStatement.setInt(2, 0);
            selectResultSet = select_preparedStatement.executeQuery();

            Map<String, Object> cert_info = new HashMap<>();
            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    cert_info.put(columnName, value);
                }
            }
            if (cert_info.size() == 0) {
                LOG.info("不是用户导入证书或已经检测完毕");
                return rowsAffected;
            } else {
                int user_black_list = (int) cert_info.get("black_list");
                String update_query = "UPDATE tb_cert_upload_file SET upload_status=?,update_time=?,labels=?,black_list=? WHERE cert_sha1=? AND upload_status=?";
                update_preparedStatement = connection.prepareStatement(update_query);
                update_preparedStatement.setInt(1, status);
                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formattedDateTime = now.format(formatter);
                update_preparedStatement.setString(2, formattedDateTime);
                update_preparedStatement.setString(3, String.valueOf(labels_set));
                update_preparedStatement.setInt(4, Math.min(black_list + user_black_list, 100));
                update_preparedStatement.setString(5, SHA1);
                update_preparedStatement.setInt(6, 0);
                rowsAffected = update_preparedStatement.executeUpdate();
                return rowsAffected;
            }
        } finally {
            if (selectResultSet != null) {
                selectResultSet.close();
            }
            if (select_preparedStatement != null) {
                select_preparedStatement.close();
            }
            if (update_preparedStatement != null) {
                update_preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    //57所证书导入batch表
    public static int setPostgreSQLCertInfo(String SHA1, boolean is_repeat, boolean is_fail, List<String> task_batch_info) throws SQLException {
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        PreparedStatement update_preparedStatement = null;
        ResultSet selectResultSet = null;
        int rowsAffected_all = 0;
        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");

            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            //对于每个taskId和batchId,更新对应的值
            for (String task_batch : task_batch_info) {
                String[] task_batch_info_list = task_batch.split("-");
                String task_id = task_batch_info_list[0];
                String batch_id = task_batch_info_list[1];
                String select_query = "SELECT repeat_num,cert_finish,fail_num FROM tb_cert_anay_batch WHERE task_id=? AND batch_id=? LIMIT 1";
                select_preparedStatement = connection.prepareStatement(select_query);
                select_preparedStatement.setInt(1, Integer.parseInt(task_id));
                select_preparedStatement.setInt(2, Integer.parseInt(batch_id));
                LOG.info("sql——{}", select_preparedStatement);
                selectResultSet = select_preparedStatement.executeQuery();

                Map<String, Object> cert_info = new HashMap<>();
                while (selectResultSet.next()) {
                    ResultSetMetaData metaData = selectResultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = selectResultSet.getObject(i);
                        cert_info.put(columnName, value);
                    }
                }
                Integer repeat_num = (Integer) cert_info.get("repeat_num");
                Integer fail_num = (Integer) cert_info.get("fail_num");
                Integer cert_finish = (Integer) cert_info.get("cert_finish") + 1;
                if (is_repeat) {
                    repeat_num = repeat_num + 1;
                }
                if (is_fail) {
                    fail_num = fail_num + 1;
                }
                String update_query = "UPDATE tb_cert_anay_batch SET repeat_num=?,cert_finish=?,fail_num=?,updated_time=? WHERE task_id=? AND batch_id=?";
                update_preparedStatement = connection.prepareStatement(update_query);
                update_preparedStatement.setInt(1, repeat_num);
                update_preparedStatement.setInt(2, cert_finish);
                update_preparedStatement.setInt(3, fail_num);
                update_preparedStatement.setString(4, formattedDateTime);
                update_preparedStatement.setInt(5, Integer.parseInt(task_id));
                update_preparedStatement.setInt(6, Integer.parseInt(batch_id));
                LOG.info("sql——{}", update_preparedStatement);
                int rowsAffected = update_preparedStatement.executeUpdate();
                rowsAffected_all += rowsAffected;
            }

            if (task_batch_info.size() != rowsAffected_all) {
                LOG.error("更新PostgreSQL证书检测结果不匹配错误，redis更新条数——{}——，PostgreSQL更新条数——{}——", task_batch_info.size(), rowsAffected_all);
                return -1;
            }
        } catch (Exception e) {
            LOG.error("PostgreSQL查询失败，error--->{},SHA1 is--->{}", e, SHA1);
            return -1;
        } finally {
            if (selectResultSet != null) {
                selectResultSet.close();
            }
            if (select_preparedStatement != null) {
                select_preparedStatement.close();
            }
            if (update_preparedStatement != null) {
                update_preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
        return rowsAffected_all;
    }

    //57所证书导入证书 tb_cert_upload_file 表
    //更新 subject_name , updated_time , black_list , labels , upload_status
    public static int setPostgreSQLUploadCertInfo(X509Certificate cert, List<String> task_batch_info) throws SQLException {
        Connection connection = null;
        PreparedStatement update_preparedStatement = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        int rowsAffected_all = 0;
        String SHA1 = cert.getDerSha1();
        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");
            Map<String, String> subjectName = cert.getSubject();
            String subjectNameResult = "";
            if (subjectName.size() != 0) {
                subjectNameResult = subjectName.entrySet().stream()
                        .map(entry -> entry.getKey() + "=" + entry.getValue())
                        .collect(Collectors.joining(", "));
            }
            Set<CertificateLabel> labels = cert.getLabels();
            String labels_result = labels.toString();
            int BlackList = cert.getThreatScore();
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            //对于每个taskId和batchId,更新对应的值
            for (String task_batch : task_batch_info) {
                String[] task_batch_info_list = task_batch.split("-");
                String task_id = task_batch_info_list[0];
                String batch_id = task_batch_info_list[1];

                // 查询操作
                String select_query = "SELECT black_list FROM tb_cert_upload_file WHERE task_id=? AND batch_id=? AND cert_sha1=? LIMIT 1";
                select_preparedStatement = connection.prepareStatement(select_query);
                select_preparedStatement.setInt(1, Integer.parseInt(task_id));
                select_preparedStatement.setInt(2, Integer.parseInt(batch_id));
                select_preparedStatement.setString(3, SHA1);
                LOG.info("sql——{}", select_preparedStatement);
                selectResultSet = select_preparedStatement.executeQuery();

                Map<String, Object> black_info = new HashMap<>();
                while (selectResultSet.next()) {
                    ResultSetMetaData metaData = selectResultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = selectResultSet.getObject(i);
                        black_info.put(columnName, value);
                    }
                }
                BlackList = Math.max((Integer) black_info.get("black_list"), BlackList);

                // 更新操作
                String update_query = "UPDATE tb_cert_upload_file SET subject_name=?,updated_time=?,black_list=?,labels=?,upload_status=? WHERE task_id=? AND batch_id=? AND cert_sha1=?";
                update_preparedStatement = connection.prepareStatement(update_query);
                update_preparedStatement.setString(1, subjectNameResult);
                update_preparedStatement.setString(2, formattedDateTime);
                update_preparedStatement.setInt(3, BlackList);
                update_preparedStatement.setString(4, labels_result);
                update_preparedStatement.setInt(5, 1);
                update_preparedStatement.setInt(6, Integer.parseInt(task_id));
                update_preparedStatement.setInt(7, Integer.parseInt(batch_id));
                update_preparedStatement.setString(8, SHA1);
                LOG.info("sql——{}", update_preparedStatement);
                int rowsAffected = update_preparedStatement.executeUpdate();
                rowsAffected_all += rowsAffected;
            }
        } catch (Exception e) {
            LOG.error("PostgreSQL UploadCert 查询失败，error--->{},SHA1 is--->{}", e, SHA1);
            return -1;
        } finally {
            if (update_preparedStatement != null) {
                update_preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
        return rowsAffected_all;
    }

    //证书是否终止导入,操作batch表,以及file表
    public static boolean postgresql_stop_import(String task_batch, String SHA1) throws SQLException {
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        PreparedStatement delete_preparedStatement = null;
        ResultSet selectResultSet = null;
        boolean is_stop = false;
        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");
            //对于每个taskId和batchId,更新对应的值
            String[] task_batch_info_list = task_batch.split("-");
            String task_id = task_batch_info_list[0];
            String batch_id = task_batch_info_list[1];
            //获取目前表里的信息
            String select_query = "SELECT status FROM tb_cert_anay_batch WHERE task_id=? AND batch_id=? LIMIT 1";
            select_preparedStatement = connection.prepareStatement(select_query);
            select_preparedStatement.setInt(1, Integer.parseInt(task_id));
            select_preparedStatement.setInt(2, Integer.parseInt(batch_id));
            LOG.info("sql——{}", select_preparedStatement);
            selectResultSet = select_preparedStatement.executeQuery();

            Map<String, Object> cert_info = new HashMap<>();
            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    cert_info.put(columnName, value);
                }
            }
            Integer status = (Integer) cert_info.get("status");
            if (status == 3) {
                is_stop = true;
                //对于每个taskId和batchId,更新对应的值
                String delete_query = "DELETE FROM tb_cert_upload_file WHERE task_id=? AND batch_id=? AND cert_sha1=? AND upload_status=?";
                delete_preparedStatement = connection.prepareStatement(delete_query);
                delete_preparedStatement.setInt(1, Integer.parseInt(task_id));
                delete_preparedStatement.setInt(2, Integer.parseInt(batch_id));
                delete_preparedStatement.setString(3, SHA1);
                delete_preparedStatement.setInt(4, 0);
                LOG.info("sql——{}", delete_preparedStatement);
                int rowsAffected = delete_preparedStatement.executeUpdate();
            }
            return is_stop;
        } catch (Exception e) {
            LOG.error("PostgreSQL stop import 查询失败，error--->{},SHA1 is--->{}，但是依然会继续证书导入步骤", e, SHA1);
            return false;
        } finally {
            if (select_preparedStatement != null) {
                select_preparedStatement.close();
            }
            if (delete_preparedStatement != null) {
                delete_preparedStatement.close();
            }
            if (selectResultSet != null) {
                selectResultSet.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    public static List<String> setCertModelBasicInfo() throws SQLException {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        List<String> model_on = new ArrayList<>();

        String sql = "UPDATE tb_cert_model SET judge_num = judge_num + 1, last_run_time = ? WHERE model_switch = 1";
        String select_sql = "SELECT model_id FROM tb_cert_model WHERE model_switch = 1";

        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");
            preparedStatement = connection.prepareStatement(sql);
            select_preparedStatement = connection.prepareStatement(select_sql);
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            preparedStatement.setString(1, formattedDateTime);
            preparedStatement.executeUpdate();
            selectResultSet = select_preparedStatement.executeQuery();

            while (selectResultSet.next()) {
                String model_id = String.valueOf(selectResultSet.getInt("model_id"));
                model_on.add(model_id);
            }

        } catch (SQLException e) {
            LOG.error("PostgreSQL CertModel 证书模型基础信息更新失败，error--->{}", e.toString());
        } finally {
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (select_preparedStatement != null) {
                select_preparedStatement.close();
            }
            if (selectResultSet != null) {
                selectResultSet.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
        return model_on;
    }

    public static int setCertModelUpdateInfo(List<String> model_now) throws SQLException {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet selectResultSet = null;
        int rowsAffected = 0;

        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            String model = String.join(",", model_now);
            String sql = String.format("UPDATE tb_cert_model SET hit_num = hit_num + 1, last_hit_time = '%s' WHERE model_id IN (%s)",
                    formattedDateTime, model);
            preparedStatement = connection.prepareStatement(sql);
            rowsAffected = preparedStatement.executeUpdate();

        } catch (SQLException e) {
            LOG.error("PostgreSQL ModelUpdate 证书模型基础信息更新失败，error--->{}", e.toString());
        } finally {
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (selectResultSet != null) {
                selectResultSet.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
        return rowsAffected;
    }

    public static boolean setErrorCertInfo(String originSHA1, String correctSHA1, String errorType) throws SQLException {
        Connection connection = null;
        PreparedStatement preparedStatement = null;

        String sql = "INSERT INTO tb_error_cert (OriginSHA1, CorrectSHA1, error_type, update_time) VALUES (?,?,?,?)";

        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");
            preparedStatement = connection.prepareStatement(sql);
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            preparedStatement.setString(1, originSHA1);
            preparedStatement.setString(2, correctSHA1);
            preparedStatement.setString(3, errorType);
            preparedStatement.setString(4, formattedDateTime);
            // 执行SQL语句
            int rowsInserted = preparedStatement.executeUpdate();

            if (rowsInserted > 0) {
                LOG.info("数据插入成功！");
                return true;
            }

        } catch (SQLException e) {
            LOG.error("PostgreSQL ErrorCert 证书纠错信息更新失败，error--->{}，错误证书的SHA1--->{}", e.toString(), originSHA1);
            return false;
        } finally {
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
        return false;
    }

    public static List<String> getPostgreSQLUserIDList(String[] task_id_list) throws SQLException {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet selectResultSet = null;
        List<Integer> int_task_id_list = new ArrayList<>();
        for (String task_id : task_id_list) {
            int_task_id_list.add(Integer.parseInt(task_id));
        }
        String sql = "SELECT user_id FROM tb_cert_anay_task WHERE task_id = ANY(?)";
        List<String> PostgreSQLUserIDList = new ArrayList<>();

        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");
            preparedStatement = connection.prepareStatement(sql);
            Array array = connection.createArrayOf("INTEGER", int_task_id_list.toArray());
            preparedStatement.setArray(1, array);

            // 执行查询
            selectResultSet = preparedStatement.executeQuery();

            while (selectResultSet.next()) {
                String user_id = selectResultSet.getString("user_id");
                if (!PostgreSQLUserIDList.contains(user_id)) {
                    PostgreSQLUserIDList.add(user_id);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOG.error("PostgreSQL UserIDList 证书纠错信息更新失败，error--->{}，错误证书的任务ID--->{}", e.toString(), task_id_list);
        } finally {
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
            if (selectResultSet != null) {
                selectResultSet.close();
            }
        }
        return PostgreSQLUserIDList;
    }

    //57所证书导入证书 tb_cert_upload_file 表
    //更新 subject_name , updated_time , black_list , labels , upload_status
    public static int getBlackListFromPostgreSQL(List<String> task_batch_info, String SHA1) throws SQLException {
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        int blackMax = 0;
        try {
            connection = PostgreSQLConnectionManager.getConnection("cert_db");

            // 构建PostgreSQL兼容的查询语句
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("SELECT black_list FROM tb_cert_upload_file WHERE cert_sha1 = ? AND (task_id, batch_id) IN (");

            for (int i = 0; i < task_batch_info.size(); i++) {
                if (i > 0) {
                    queryBuilder.append(", ");
                }
                queryBuilder.append("(?, ?)");
            }
            queryBuilder.append(")");

            select_preparedStatement = connection.prepareStatement(queryBuilder.toString());
            select_preparedStatement.setString(1, SHA1);

            int paramIndex = 2;
            for (String task_batch : task_batch_info) {
                String[] task_batch_info_list = task_batch.split("-");
                String task_id = task_batch_info_list[0];
                String batch_id = task_batch_info_list[1];
                select_preparedStatement.setInt(paramIndex++, Integer.parseInt(task_id));
                select_preparedStatement.setInt(paramIndex++, Integer.parseInt(batch_id));
            }

            LOG.info("查询PostgreSQL中黑名单权重的sql为：{}", select_preparedStatement);
            selectResultSet = select_preparedStatement.executeQuery();

            List<Integer> blackListResult = new ArrayList<>();
            // 处理查询结果
            while (selectResultSet.next()) {
                int blackList = selectResultSet.getInt("black_list");
                blackListResult.add(blackList);
            }

            if (!blackListResult.isEmpty()) {
                blackMax = Collections.max(blackListResult);
            }
        } catch (Exception e) {
            LOG.error("PostgreSQL BlackList 查询失败，error--->{},SHA1 is--->{}", e, SHA1);
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
        return blackMax;
    }

    /**
     * 从威胁情报数据库中获取所有的威胁情报
     */
    public static List<ThreatInfo> getAllThreatInfoList() throws SQLException {
        Connection connection = null;
        ResultSet selectResultSet = null;
        PreparedStatement preparedStatement = null;
        List<ThreatInfo> resultList = new ArrayList<>();
        String ThreatSql = "SELECT target,target_type,tag_name FROM tb_threat_info";
        try {
            connection = PostgreSQLConnectionManager.getConnection("th_analysis");
            preparedStatement = connection.prepareStatement(ThreatSql);
            selectResultSet = preparedStatement.executeQuery();

            while (selectResultSet.next()) {
                ThreatInfo info = new ThreatInfo();
                info.setTarget(selectResultSet.getString(1));
                info.setTargetType(selectResultSet.getString(2));
                info.setTagName(selectResultSet.getString(3));
                resultList.add(info);
            }
        } catch (Exception e) {
            LOG.error("获取 威胁情报信息 失败，error--->{}", e.toString());
        } finally {
            if (connection != null) {
                connection.close();
            }
            if (selectResultSet != null) {
                selectResultSet.close();
            }
        }
        LOG.info("威胁情报信息 查询成功");
        return resultList;
    }

    /**
     * 从威胁情报数据库中获取指定类型的威胁情报
     */
    public static List<ThreatInfo> getThreatInfoListByTag(String tag_name) throws SQLException {
        Connection connection = null;
        ResultSet selectResultSet = null;
        PreparedStatement preparedStatement = null;
        List<ThreatInfo> resultList = new ArrayList<>();
        String ThreatSql = "SELECT target,target_type,target_name FROM tb_threat_info WHERE tag_name=?";
        try {
            connection = PostgreSQLConnectionManager.getConnection("th_analysis");
            preparedStatement = connection.prepareStatement(ThreatSql);
            preparedStatement.setString(1, tag_name);
            selectResultSet = preparedStatement.executeQuery();

            while (selectResultSet.next()) {
                ThreatInfo info = new ThreatInfo();
                info.setTarget(selectResultSet.getString(1));
                info.setTargetType(selectResultSet.getString(2));
                info.setTagName(selectResultSet.getString(3));
                resultList.add(info);
            }
        } catch (Exception e) {
            LOG.error("获取威胁情报信息失败，error--->{}", e.toString());
        } finally {
            if (connection != null) {
                connection.close();
            }
            if (selectResultSet != null) {
                selectResultSet.close();
            }
        }
        LOG.info("威胁情报查询完成");
        return resultList;
    }

    /**
     * 从已经提取的威胁情报数据库中获取指定资产的威胁情报
     * TargetType 过滤
     */
    public static List<String> getSpecificTargetTypeThreatInfoList(List<ThreatInfo> ThreatInfoList, String targetType) {
        List<String> resultList = new ArrayList<>();
        for (ThreatInfo threatInfo : ThreatInfoList) {
            // 此处使用包含，ip,ipv4,ip_v4都在表里，只能模糊匹配了，domain的话就是domain
            if (threatInfo.getTargetType().contains(targetType)) {
                resultList.add(threatInfo.getTarget());
            }
        }
        return resultList;
    }

    /**
     * 从已经提取的威胁情报数据库中获取指定资产的威胁情报
     * TagName 过滤
     */
    public static List<String> getSpecificTagNameThreatInfoList(List<ThreatInfo> ThreatInfoList, String tagName) {
        List<String> resultList = new ArrayList<>();
        for (ThreatInfo threatInfo : ThreatInfoList) {
            // 此处使用包含，ip,ipv4,ip_v4都在表里，只能模糊匹配了，domain的话就是domain
            if (threatInfo.getTagName().contains(tagName)) {
                resultList.add(threatInfo.getTarget());
            }
        }
        return resultList;
    }

    /**
     * 从已经提取的威胁情报数据库中获取指定资产的威胁情报
     * TagName 过滤
     * TargetType 过滤
     */
    public static List<String> getSpecificThreatInfoList(List<ThreatInfo> ThreatInfoList, String tagName, String targetType) {
        List<String> resultList = new ArrayList<>();
        for (ThreatInfo threatInfo : ThreatInfoList) {
            // 此处使用包含，ip,ipv4,ip_v4都在表里，只能模糊匹配了，domain的话就是domain
            if (threatInfo.getTagName().contains(tagName) && threatInfo.getTargetType().contains(targetType)) {
                resultList.add(threatInfo.getTarget());
            }
        }
        return resultList;
    }

    // 日志外发查询，查询tb_alarm_output表，取其中的status值，若为0，则进行外发，若为1，则外发
    public static List<Map<String, Object>> getAlarmOutputStatus() throws SQLException {
        List<Map<String, Object>> tool_on = new ArrayList<>();
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        try {
            connection = PostgreSQLConnectionManager.getConnection("th_analysis");

            String select_query = "SELECT * FROM tb_alarm_output WHERE status=1";
            select_preparedStatement = connection.prepareStatement(select_query);
            selectResultSet = select_preparedStatement.executeQuery();
            LOG.info("sql——{}", select_preparedStatement);

            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                Map<String, Object> tool_info = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    tool_info.put(columnName, value);
                }
                tool_on.add(tool_info);
            }
            if (tool_on.size() != 0) {
                LOG.info("告警外发的组件开启为：{}", tool_on);
            } else {
                LOG.info("--------------告警外发PostgreSQL表中status都为空--------------");
            }
        } catch (Exception e) {
            LOG.error("查询PostgreSQL出现错误，报错内容:——{}——", e.toString());
        } finally {
            if (selectResultSet != null) {
                selectResultSet.close();
            }
            if (select_preparedStatement != null) {
                select_preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        }

        return tool_on;
    }
}
