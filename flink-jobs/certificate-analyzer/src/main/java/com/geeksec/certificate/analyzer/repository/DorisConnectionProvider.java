package com.geeksec.certificate.analyzer.repository;

import java.sql.Connection;
import java.sql.SQLException;

import com.geeksec.common.database.DatabaseConnectionManager;
import com.geeksec.common.database.doris.DorisConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * Doris数据库连接提供者实现。
 *
 * <AUTHOR>
 */
@Slf4j
public class DorisConnectionProvider implements ConnectionProvider<Connection> {

    private final DatabaseConnectionManager connectionManager;

    public DorisConnectionProvider() {
        // 使用 DorisConnectionManager 单例实例
        this.connectionManager = DorisConnectionManager.getInstance();
        log.info("DorisConnectionProvider initialized.");
    }

    @Override
    public Connection getConnection() throws SQLException {
        // Delegates to the existing DorisConnectionManager
        // This might need adjustment if DorisConnectionManager manages a pool and connections need to be explicitly checked out/in.
        try {
            return connectionManager.getConnection();
        } catch (Exception e) {
            log.error("Failed to get Doris connection from DorisConnectionManager", e);
            if (e instanceof SQLException) {
                throw (SQLException) e;
            }
            throw new SQLException("Failed to get Doris connection: " + e.getMessage(), e);
        }
    }

    @Override
    public void releaseConnection(Connection connection) {
        // If DorisConnectionManager uses a connection pool, this is where the connection would be returned to the pool.
        // If getSingletonConnection() always returns the same shared connection, or new ones that are auto-closed by try-with-resources in client code,
        // this method might do nothing or just log.
        // For safety, we'll try to close it if it's not null and not already closed.
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                    log.debug("Doris connection released/closed.");
                }
            } catch (SQLException e) {
                log.warn("Error while trying to close/release Doris connection", e);
            }
        }
    }

    @Override
    public void close() throws Exception {
        // This method is for cleaning up the ConnectionProvider itself, e.g., shutting down a connection pool
        // managed by this provider. If DorisConnectionManager is a global singleton managing its own lifecycle,
        // this provider might not have anything specific to clean up.
        // However, if DorisConnectionManager had a shutdown method, it could be called here.
        // For now, we'll assume DorisConnectionManager handles its own shutdown if necessary.
        log.info("DorisConnectionProvider closed. (No specific resources to release in this implementation if DorisConnectionManager is global)");
        // Example: If DorisConnectionManager had a shutdown method:
        // DorisConnectionManager.shutdown();
    }
}
