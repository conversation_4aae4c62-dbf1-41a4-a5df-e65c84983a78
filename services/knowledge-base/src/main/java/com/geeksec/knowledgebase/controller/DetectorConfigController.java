package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.DetectorConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 检测器配置控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/detector-config")
@RequiredArgsConstructor
@Tag(name = "Detector Config Controller", description = "检测器配置查询")
public class DetectorConfigController {

    private final DetectorConfigService detectorConfigService;

    @GetMapping("/{detectorName}")
    @Operation(summary = "根据检测器名称获取配置")
    public ApiResponse<Map<String, Object>> getDetectorConfig(
            @Parameter(description = "检测器名称") @PathVariable String detectorName) {
        log.debug("获取检测器配置: {}", detectorName);
        Map<String, Object> config = detectorConfigService.getDetectorConfig(detectorName);
        return ApiResponse.success(config);
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有检测器配置列表")
    public ApiResponse<Map<String, Object>> getAllDetectorConfigs() {
        log.debug("获取所有检测器配置列表");
        Map<String, Object> configs = detectorConfigService.getAllDetectorConfigs();
        return ApiResponse.success(configs);
    }
}
