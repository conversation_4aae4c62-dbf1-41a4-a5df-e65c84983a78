package com.geeksec.certificate.analyzer.sink;

import org.apache.flink.api.java.utils.ParameterTool;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.sink.doris.DorisCertificateSinkManager;
import com.geeksec.certificate.analyzer.pipeline.CertificateAnalysisPipeline;

import lombok.extern.slf4j.Slf4j;

/**
 * Sink协调器
 * 负责配置和管理所有数据输出组件
 *
 * <AUTHOR>
 */
@Slf4j
public class SinkCoordinator {

    /**
     * 配置所有输出组件
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    public static void configureAllOutputs(CertificateAnalysisPipeline.PipelineResult pipelineResult,
                                         ParameterTool config) {
        log.info("配置所有输出组件");

        // 使用CertificateSinkFactory为处理后的数据流添加所有启用的Sink
        if (pipelineResult.getProcessedStream() != null) {
            CertificateSinkFactory.addAllEnabledSinks(pipelineResult.getProcessedStream(), config);
        }

        // 处理错误流（如果需要）
        if (pipelineResult.getErrorStream() != null) {
            configureErrorStreamOutput(pipelineResult, config);
        }

        log.info("所有输出组件配置完成");
    }

    /**
     * 配置错误流输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configureErrorStreamOutput(CertificateAnalysisPipeline.PipelineResult pipelineResult,
                                                  ParameterTool config) {
        log.info("配置错误流输出");

        if (pipelineResult.getErrorStream() != null) {
            // 错误证书可以输出到特殊的存储或日志系统
            pipelineResult.getErrorStream()
                    .addSink(CertificateSinkFactory.createKafkaSink(config))
                    .name("错误证书Kafka输出")
                    .setParallelism(1);

            log.info("错误流输出配置完成");
        }
    }

    /**
     * 配置Doris数据仓库输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configureDorisOutput(CertificateAnalysisPipeline.PipelineResult pipelineResult,
                                           ParameterTool config) {
        log.info("配置Doris数据仓库输出");

        if (pipelineResult.getProcessedStream() != null) {
            // 添加证书Doris输出
            DorisCertificateSinkManager.addDorisCertificateOutput(
                    pipelineResult.getProcessedStream(), config);

            // 添加威胁证书Doris输出（可选）
            DorisSinkManager.addThreatCertificateDorisSink(
                    pipelineResult.getProcessedStream(), config);

            log.info("Doris输出配置完成");
        } else {
            log.warn("处理流为空，跳过Doris输出配置");
        }
    }

    /**
     * 配置Nebula图数据库输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configureNebulaOutput(CertificateAnalysisPipeline.PipelineResult pipelineResult,
                                            ParameterTool config) {
        log.info("配置Nebula图数据库输出");

        if (pipelineResult.getProcessedStream() != null) {
            // 暂时注释掉Nebula输出，等待实现完成
            log.info("Nebula输出功能待实现");
            // pipelineResult.getProcessedStream()
            //         .addSink(new CertificateNebulaSink())
            //         .name("证书Nebula图数据库输出")
            //         .setParallelism(2);
        }
    }

    /**
     * 配置PostgreSQL输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configurePostgreSQLOutput(CertificateProcessingPipeline.PipelineResult pipelineResult,
                                                ParameterTool config) {
        log.info("配置PostgreSQL输出");

        if (pipelineResult.getProcessedStream() != null) {
            // 暂时注释掉PostgreSQL输出，等待实现完成
            log.info("PostgreSQL输出功能待实现");
            // pipelineResult.getProcessedStream().addSink(new PostgreSQLCertSink());
        }
    }


}
