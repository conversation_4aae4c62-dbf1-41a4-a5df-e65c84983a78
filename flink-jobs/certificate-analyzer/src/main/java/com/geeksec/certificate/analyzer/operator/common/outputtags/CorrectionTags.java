package com.geeksec.certificate.analyzer.operator.common.outputtags;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书纠错输出标签定义
 * 用于Flink流处理中的证书纠错侧输出流标记
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class CorrectionTags {

    /**
     * 正向块哈希纠错标签
     */
    public static class ForwardChunkHash {
        public static final OutputTag<X509Certificate> SUCCESS =
                new OutputTag<>("forward-chunk-hash-success", TypeInformation.of(X509Certificate.class));
        public static final OutputTag<X509Certificate> FAIL =
                new OutputTag<>("forward-chunk-hash-fail", TypeInformation.of(X509Certificate.class));
    }

    /**
     * 反向块哈希纠错标签
     */
    public static class ReverseChunkHash {
        public static final OutputTag<X509Certificate> SUCCESS =
                new OutputTag<>("reverse-chunk-hash-success", TypeInformation.of(X509Certificate.class));
        public static final OutputTag<X509Certificate> FAIL =
                new OutputTag<>("reverse-chunk-hash-fail", TypeInformation.of(X509Certificate.class));
    }

    /**
     * 字节序列反向纠错标签
     */
    public static class ByteSequenceReverse {
        public static final OutputTag<X509Certificate> SUCCESS =
                new OutputTag<>("byte-sequence-reverse-success", TypeInformation.of(X509Certificate.class));
        public static final OutputTag<X509Certificate> FAIL =
                new OutputTag<>("byte-sequence-reverse-fail", TypeInformation.of(X509Certificate.class));
    }

    // 私有构造函数，防止实例化
    private CorrectionTags() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
}
