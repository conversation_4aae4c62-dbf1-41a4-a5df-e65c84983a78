package com.geeksec.certificate.analyzer.operator.preprocessing.correction;

import java.util.Arrays;
import com.geeksec.certificate.analyzer.repository.CertificateRepository;
import java.util.List;
import java.util.Map;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.operator.common.outputtags.CorrectionTags;
import com.geeksec.certificate.analyzer.util.cert.CertificateUtils;
import com.geeksec.certificate.analyzer.sink.minio.MinioCertificateClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 处理证书字节翻转错误的处理函数
 * 使用MinIO存储证书数据
 *
 * <AUTHOR>
 * @date 2023/7/7
 * @modified hufengkai
 * @date 2024/10/15
 */
@Slf4j
public class ByteSequenceReverser {
    private transient CertificateRepository certificateRepository;
    // private transient JedisPool jedisPool = null; // Removed
    /**
     * Constructor with CertificateRepository dependency.
     *
     * @param certificateRepository The repository for accessing certificate data.
     */
    public ByteSequenceReverser(CertificateRepository certificateRepository) {
        this.certificateRepository = certificateRepository;
    }

    // @Override // open() and close() for JedisPool removed
    // public void open(Configuration parameters) throws Exception { ... }
    // @Override
    // public void close() throws Exception { ... }

    @Override
    public void processElement(X509Certificate originalCert, ProcessFunction<X509Certificate, X509Certificate>.Context context,
            Collector<X509Certificate> collector) throws Exception {
        // 提取证书的正向哈希列表 (used for byte flip detection)
        List<String> forwardChunkHashes = originalCert.getForwardChunkHashes(); 
        int length = forwardChunkHashes.size();
        originalCert.setWellFormed(false);

        try {
            // ByteSequenceReverse uses forward hashes for lookup, as per existing logic.
            String finalCorrectSha1 = this.certificateRepository.findDerSha1ByForwardHashes(forwardChunkHashes, length);
            // String sourceOfSha1 = "Repository"; // Simplified

            if (finalCorrectSha1 != null) {
                log.info("CertificateRepository中查询到字节翻转的纠错信息，SHA1: {}", finalCorrectSha1);
                List<String> sha1List = Arrays.asList(finalCorrectSha1.split("_")[0]);
                Map<String, byte[]> resultMap = MinioCertificateClient.batchGetCertificatesBySha1(sha1List);

                if (resultMap != null && !resultMap.isEmpty()) {
                    processCertificateData(originalCert, resultMap, finalCorrectSha1, context, "Repository");
                } else {
                    log.error("MinIO中无法查询到 Repository 中SHA1为 {} 的证书 (字节翻转场景)", finalCorrectSha1);
                    originalCert.setWellFormed(false);
                    context.output(CorrectionTags.ByteSequenceReverse.FAIL, originalCert);
                }
            } else {
                // CertificateRepository 未命中
                log.warn("CertificateRepository 未查询到证书 {} (byteSequenceReverse: {}) 的字节序列反转纠错信息", originalCert.getDerSha1(), forwardChunkHashes);
                originalCert.setWellFormed(false);
                context.output(CorrectionTags.ByteSequenceReverse.FAIL, originalCert);
            }
        } catch (Exception e) {
            log.error("处理证书 {} (byteSequenceReverse: {}) 失败: {}",
                      originalCert.getDerSha1(), forwardChunkHashes, e.getMessage(), e);
            originalCert.setWellFormed(false);
            context.output(CorrectionTags.ByteSequenceReverse.FAIL, originalCert);
        }
    }

    /**
     * 处理证书数据
     *
     * @param cert  原始证书
     * @param resultMap 查询结果
     * @param sha1      证书SHA1
     * @param context   处理上下文
     * @param source    数据来源（Redis或ES）
     */
    private void processCertificateData(X509Certificate originalCert, Map<String, byte[]> resultMap,
            String foundSha1InCache, ProcessFunction<X509Certificate, X509Certificate>.Context context, String source) {

        for (Map.Entry<String, byte[]> entry : resultMap.entrySet()) {
            byte[] correctedCertByte = entry.getValue();
            String errorName = "ByteFlip";
            X509Certificate correctedCert = new X509Certificate(correctedCertByte);

            // Compare the original certificate's bytes with the corrected one from MinIO
            if (CertificateUtils.validateCorrectionResult(originalCert.getCert(), correctedCertByte, errorName)) {
                // Parse the content of the corrected certificate
                // 证书内容已在构建 X509Certificate 对象时通过 CertificateParser 解析
                // correctedCert.setDerSha1() is not needed as it's already set during parsing
                correctedCert.setWellFormed(true);
                correctedCert.setCorrupted(false); // Mark as no longer corrupted
                // Add a label to indicate this cert was corrected by byte sequence reverse
                // Note: getLabels() returns List<Integer>, need to add an appropriate label ID
                // correctedCert.getLabels().add(3); // Assuming 3 is the ID for byte sequence reverse corrected certs

                log.info("证书 {} (original SHA1: {}) 纠错成功 using data from {}, corrected SHA1: {}. Outputting corrected certificate.", 
                         originalCert.getDerSha1(), originalCert.getDerSha1(), source, correctedCert.getDerSha1());
                context.output(CorrectionTags.ByteSequenceReverse.SUCCESS, correctedCert);
            } else {
                log.error("证书 {} (original SHA1: {}) 纠错失败 using data from {}. LevenshteinDistance过长. Found SHA1 in cache: {}. Outputting original certificate.",
                          originalCert.getDerSha1(), originalCert.getDerSha1(), source, foundSha1InCache);
                originalCert.setWellFormed(false);
                context.output(CorrectionTags.ByteSequenceReverse.FAIL, originalCert);
            }
            break;
        }
    }
}
