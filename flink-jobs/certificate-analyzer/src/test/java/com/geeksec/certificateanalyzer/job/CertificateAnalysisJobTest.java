package com.geeksec.certificate.analyzer.job;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书分析作业测试
 * 
 * <AUTHOR>
 */
@Slf4j
class CertificateAnalysisJobTest {

    private ParameterTool testConfig;
    private StreamExecutionEnvironment testEnv;

    @BeforeEach
    void setUp() {
        log.info("初始化测试环境");
        
        // 创建测试配置
        Map<String, String> configMap = new HashMap<>();
        configMap.put("certificate.analyzer.debug.enabled", "true");
        configMap.put("certificate.analyzer.parallelism", "2");
        configMap.put("certificate.analyzer.buffer.size", "100");
        configMap.put("certificate.analyzer.timeout.ms", "5000");
        configMap.put("certificate.analyzer.minio.enabled", "false");
        configMap.put("certificate.output.postgresql.enabled", "false");

        configMap.put("certificate.output.nebula.enabled", "false");
        configMap.put("kafka.topic.certificate.files", "test-certfile");
        configMap.put("kafka.topic.system.certificates", "test-certfile-system");
        
        testConfig = ParameterTool.fromMap(configMap);
        
        // 创建测试执行环境
        testEnv = StreamExecutionEnvironment.getExecutionEnvironment();
        testEnv.setParallelism(1);
        testEnv.getConfig().setGlobalJobParameters(testConfig);
    }

    @Test
    @DisplayName("测试证书分析作业配置")
    void testJobConfiguration() {
        log.info("测试证书分析作业配置");
        
        // 测试配置获取
        ParameterTool config = CertificateAnalyzerConfig.getConfig();
        assertNotNull(config);
        
        // 测试配置项
        assertTrue(CertificateAnalyzerConfig.isDebugEnabled());
        assertEquals(4, CertificateAnalyzerConfig.getAnalyzerParallelism());
        assertEquals(1000, CertificateAnalyzerConfig.getAnalyzerBufferSize());
        assertEquals(30000L, CertificateAnalyzerConfig.getAnalyzerTimeoutMs());
        
        // 测试主题配置
        assertNotNull(CertificateAnalyzerConfig.getCertificateFilesTopic());
        assertNotNull(CertificateAnalyzerConfig.getSystemCertificatesTopic());
        
        log.info("证书分析作业配置测试通过");
    }

    @Test
    @DisplayName("测试证书数据模型")
    void testCertificateDataModel() {
        log.info("测试证书数据模型");
        
        // 创建测试证书
        X509Certificate testCert = createTestCertificate();
        
        // 验证基本属性
        assertNotNull(testCert.getCertId());
        assertNotNull(testCert.getSha1());
        assertNotNull(testCert.getSubject());
        assertNotNull(testCert.getIssuer());
        assertNotNull(testCert.getLabels());
        assertNotNull(testCert.getCreateTime());
        
        // 验证证书状态
        assertTrue(testCert.isWellFormed());
        
        log.info("证书数据模型测试通过: {}", testCert.getCertId());
    }

    @Test
    @DisplayName("测试配置验证")
    void testConfigurationValidation() {
        log.info("测试配置验证");
        
        // 测试必需配置项
        String certificateFilesTopic = CertificateAnalyzerConfig.getCertificateFilesTopic();
        assertNotNull(certificateFilesTopic);
        assertFalse(certificateFilesTopic.trim().isEmpty());
        
        String systemCertificatesTopic = CertificateAnalyzerConfig.getSystemCertificatesTopic();
        assertNotNull(systemCertificatesTopic);
        assertFalse(systemCertificatesTopic.trim().isEmpty());
        
        // 测试数值配置的合理性
        int parallelism = CertificateAnalyzerConfig.getAnalyzerParallelism();
        assertTrue(parallelism > 0 && parallelism <= 32);
        
        int bufferSize = CertificateAnalyzerConfig.getAnalyzerBufferSize();
        assertTrue(bufferSize > 0);
        
        long timeoutMs = CertificateAnalyzerConfig.getAnalyzerTimeoutMs();
        assertTrue(timeoutMs > 0);
        
        log.info("配置验证测试通过");
    }

    @Test
    @DisplayName("测试输出开关配置")
    void testOutputSwitchConfiguration() {
        log.info("测试输出开关配置");
        
        // 测试各种输出开关
        boolean postgresqlEnabled = CertificateAnalyzerConfig.isPostgreSQLEnabled();
        boolean nebulaEnabled = CertificateAnalyzerConfig.isNebulaEnabled();

        // 输出开关状态（可以是任意值，但不应该抛出异常）
        log.info("PostgreSQL输出: {}", postgresqlEnabled);
        log.info("Nebula输出: {}", nebulaEnabled);
        log.info("MinIO存储: 必须启用（用于证书文件存储）");
        
        log.info("输出开关配置测试通过");
    }

    @Test
    @DisplayName("测试执行环境配置")
    void testExecutionEnvironmentConfiguration() {
        log.info("测试执行环境配置");
        
        // 验证执行环境
        assertNotNull(testEnv);
        assertEquals(1, testEnv.getParallelism());
        
        // 验证全局参数
        ParameterTool globalParams = (ParameterTool) testEnv.getConfig().getGlobalJobParameters();
        assertNotNull(globalParams);
        assertEquals("true", globalParams.get("certificate.analyzer.debug.enabled"));
        
        log.info("执行环境配置测试通过");
    }

    /**
     * 创建测试证书
     */
    private X509Certificate createTestCertificate() {
        X509Certificate cert = new X509Certificate();
        cert.setCertId("test-cert-001");
        cert.setSha1("da39a3ee5e6b4b0d3255bfef95601890afd80709");
        cert.setSubject("CN=test.example.com,O=Test Organization,C=US");
        cert.setIssuer("CN=Test CA,O=Test CA Organization,C=US");
        cert.setWellFormed(true);
        cert.setCreateTime(LocalDateTime.now());
        cert.setLabels(new java.util.ArrayList<>());
        cert.getLabels().add("Test Certificate");
        return cert;
    }
}
