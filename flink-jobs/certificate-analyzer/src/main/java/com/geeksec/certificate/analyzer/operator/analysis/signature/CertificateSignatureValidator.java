package com.geeksec.certificate.analyzer.operator.analysis.signature;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.enums.CertificateTrustStatus;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.util.cert.CertificateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import java.io.ByteArrayInputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.SignatureException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 证书签名验证算子
 * <p>
 * 负责验证证书的签名是否有效，实现证书链验证、伪造证书检测、白名单证书识别等功能。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateSignatureValidator extends RichMapFunction<X509Certificate, X509Certificate> {

    // 系统证书库连接
    private transient CertificateRepository certificateRepository;
    
    // 可信CA证书缓存
    private transient Map<String, X509Certificate> trustedCaCertCache;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 从配置中获取知识库服务URL
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://localhost:8080");

        // 初始化证书仓库
        certificateRepository = new CertificateRepository(knowledgeBaseUrl);

        // 初始化可信CA证书缓存
        trustedCaCertCache = new HashMap<>();

        log.info("证书签名验证算子初始化完成，知识库服务地址: {}", knowledgeBaseUrl);
    }
    
    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        // 如果是自签名证书，直接处理
        if (certificate.getTrustStatus() == CertificateTrustStatus.SELF_SIGNED) {
            processSelfSignedCertificate(certificate);
            return certificate;
        }
        
        // 验证证书链
        validateCertificateChain(certificate);
        
        return certificate;
    }
    
    /**
     * 处理自签名证书
     * 
     * @param certificate 自签名证书
     */
    private void processSelfSignedCertificate(X509Certificate certificate) {
        // 检查是否是CA证书
        Map<String, String> extensions = certificate.getExtensions();
        if (extensions != null && extensions.containsKey(CertificateConstants.EXTENSION_BASIC_CONSTRAINTS)) {
            String basicConstraints = extensions.get(CertificateConstants.EXTENSION_BASIC_CONSTRAINTS);
            if (!basicConstraints.contains(CertificateConstants.CA_TRUE)) {
                // 非CA的自签名证书，标记为未知CA
                certificate.addLabel(CertificateLabel.UNKNOWN_CA);
            }
        }
        
        // 添加自身到证书链
        List<String> certChain = certificate.getCertificateChain();
        if (certChain == null) {
            certChain = new ArrayList<>();
            certificate.setCertificateChain(certChain);
        }
        
        if (!certChain.contains(certificate.getDerSha1())) {
            certChain.add(certificate.getDerSha1());
        }
        
        // 检查是否是系统可信证书
        if (certificateRepository.isTrustedCaCertificate(certificate.getDerSha1())) {
            certificate.addLabel(CertificateLabel.TRUSTED_CA);
        }
    }
    
    /**
     * 验证证书链
     * 
     * @param certificate 待验证的证书
     */
    private void validateCertificateChain(X509Certificate certificate) {
        try {
            // 1. 查找父证书
            Map<String, X509Certificate> parentCerts = findParentCertificates(certificate);
            
            // 如果找不到父证书，标记为证书链缺失
            if (parentCerts.isEmpty()) {
                certificate.addLabel(CertificateLabel.MISSING_CHAIN);
                return;
            }
            
            // 如果找到多个父证书，标记为多证书链
            if (parentCerts.size() > 1) {
                certificate.addLabel(CertificateLabel.MULTIPLE_CHAINS);
            }
            
            // 2. 验证每个父证书
            boolean validSignature = false;
            X509Certificate validParentCert = null;
            
            for (X509Certificate parentCert : parentCerts.values()) {
                // 检查证书链是否合法
                if (isIllegalCertificateChain(certificate, parentCert)) {
                    certificate.addLabel(CertificateLabel.ILLEGAL_CHAIN);
                }
                
                // 验证签名
                boolean isValid = verifyCertificateSignature(certificate, parentCert);
                
                if (isValid) {
                    validSignature = true;
                    validParentCert = parentCert;
                    break;
                }
            }
            
            // 3. 处理验证结果
            if (!validSignature) {
                // 签名验证失败，标记为伪造证书
                certificate.addLabel(CertificateLabel.FAKE_CERT);
                return;
            }
            
            // 4. 更新证书链
            updateCertificateChain(certificate, validParentCert);
            
            // 5. 检查父证书是否是可信CA
            if (isTrustedCaCertificate(validParentCert)) {
                certificate.addLabel(CertificateLabel.TRUSTED_CHAIN);
            } else if (isSelfSignedCertificate(validParentCert) && !certificateRepository.isTrustedCaCertificate(validParentCert.getDerSha1())) {
                // 父证书是自签名但不在可信CA列表中，标记为未知CA
                certificate.addLabel(CertificateLabel.UNKNOWN_CA);
            }
            
        } catch (Exception e) {
            log.error("验证证书链时发生错误: {}", certificate.getCommonName(), e);
            certificate.addLabel(CertificateLabel.VALIDATION_ERROR);
        }
    }
    
    /**
     * 查找证书的父证书
     * 
     * @param certificate 子证书
     * @return 父证书映射表 (SHA1 -> 证书对象)
     */
    private Map<String, X509Certificate> findParentCertificates(X509Certificate certificate) {
        Map<String, X509Certificate> parentCerts = new HashMap<>();
        
        try {
            // 根据颁发者信息查找父证书
            String issuerDn = certificate.getIssuerDn();
            String authorityKeyId = CertificateUtils.getAuthorityKeyIdentifier(certificate);
            
            // 从证书库中查找匹配的父证书
            List<X509Certificate> candidates = certificateRepository.findCertificatesBySubjectAndKeyId(
                    issuerDn, authorityKeyId);
            
            for (X509Certificate candidate : candidates) {
                parentCerts.put(candidate.getDerSha1(), candidate);
            }
            
        } catch (Exception e) {
            log.error("查找父证书时发生错误: {}", certificate.getCommonName(), e);
        }
        
        return parentCerts;
    }
    
    /**
     * 验证证书签名
     * 
     * @param certificate 待验证的证书
     * @param parentCert 父证书
     * @return 签名是否有效
     */
    private boolean verifyCertificateSignature(X509Certificate certificate, X509Certificate parentCert) {
        try {
            // 将证书转换为Java证书对象
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            java.security.cert.X509Certificate cert = (java.security.cert.X509Certificate) cf.generateCertificate(
                    new ByteArrayInputStream(certificate.getCert()));
            
            // 获取父证书的公钥
            PublicKey parentPublicKey = CertificateUtils.getPublicKey(parentCert);
            
            // 验证签名
            cert.verify(parentPublicKey);
            return true;
            
        } catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            // 这些异常表示签名验证失败
            log.debug("证书签名验证失败: {}", certificate.getCommonName(), e);
            return false;
        } catch (CertificateException | Exception e) {
            // 其他异常表示验证过程出错
            log.error("证书签名验证过程出错: {}", certificate.getCommonName(), e);
            return false;
        }
    }
    
    /**
     * 检查证书链是否合法
     * 
     * @param certificate 子证书
     * @param parentCert 父证书
     * @return 是否存在不合法情况
     */
    private boolean isIllegalCertificateChain(X509Certificate certificate, X509Certificate parentCert) {
        // 检查父证书是否是CA证书
        Map<String, String> extensions = parentCert.getExtensions();
        if (extensions == null || !extensions.containsKey(CertificateConstants.EXTENSION_BASIC_CONSTRAINTS) ||
                !extensions.get(CertificateConstants.EXTENSION_BASIC_CONSTRAINTS).contains(CertificateConstants.CA_TRUE)) {
            return true;
        }
        
        // 检查有效期
        long notBefore = certificate.getNotBeforeTimestamp();
        long notAfter = certificate.getNotAfterTimestamp();
        long parentNotBefore = parentCert.getNotBeforeTimestamp();
        long parentNotAfter = parentCert.getNotAfterTimestamp();
        
        // 子证书的有效期应该在父证书的有效期内
        return parentNotBefore > notBefore || parentNotAfter < notAfter;
    }
    
    /**
     * 更新证书链
     * 
     * @param certificate 子证书
     * @param parentCert 父证书
     */
    private void updateCertificateChain(X509Certificate certificate, X509Certificate parentCert) {
        // 初始化证书链
        List<String> certChain = certificate.getCertificateChain();
        if (certChain == null) {
            certChain = new ArrayList<>();
            certificate.setCertificateChain(certChain);
        }
        
        // 添加父证书到证书链
        String parentSha1 = parentCert.getDerSha1();
        
        // 检查是否存在循环引用
        if (certChain.contains(parentSha1) && !isSelfSignedCertificate(parentCert)) {
            certificate.addLabel(CertificateLabel.CHAIN_LOOP);
            return;
        }
        
        // 添加到证书链
        if (!certChain.contains(parentSha1)) {
            certChain.add(parentSha1);
        }
    }
    
    /**
     * 检查证书是否是自签名证书
     * 
     * @param certificate 待检查的证书
     * @return 是否是自签名证书
     */
    private boolean isSelfSignedCertificate(X509Certificate certificate) {
        // 检查主题和颁发者是否相同
        String subjectDn = certificate.getSubjectDn();
        String issuerDn = certificate.getIssuerDn();
        
        if (subjectDn.equals(issuerDn)) {
            return true;
        }
        
        // 检查主题密钥标识符和授权密钥标识符是否相同
        String subjectKeyId = CertificateUtils.getSubjectKeyIdentifier(certificate);
        String authorityKeyId = CertificateUtils.getAuthorityKeyIdentifier(certificate);
        
        return subjectKeyId != null && authorityKeyId != null && subjectKeyId.equals(authorityKeyId);
    }
    
    /**
     * 检查证书是否是可信CA证书
     * 
     * @param certificate 待检查的证书
     * @return 是否是可信CA证书
     */
    private boolean isTrustedCaCertificate(X509Certificate certificate) {
        return certificateRepository.isTrustedCaCertificate(certificate.getDerSha1());
    }
}
