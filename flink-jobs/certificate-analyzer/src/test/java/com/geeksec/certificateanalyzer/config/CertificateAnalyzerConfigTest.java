package com.geeksec.certificate.analyzer.config;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.common.constants.ConfigConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书分析器配置测试
 * 
 * <AUTHOR>
 */
@Slf4j
class CertificateAnalyzerConfigTest {

    private Map<String, String> testConfigMap;

    @BeforeEach
    void setUp() {
        log.info("初始化配置测试环境");
        
        testConfigMap = new HashMap<>();
        testConfigMap.put(ConfigConstants.KAFKA_TOPIC_CERTIFICATE_FILES, "test-certfile");
        testConfigMap.put(ConfigConstants.KAFKA_TOPIC_SYSTEM_CERTIFICATES, "test-certfile-system");
        testConfigMap.put(ConfigConstants.CERTIFICATE_ANALYZER_PARALLELISM, "8");
        testConfigMap.put(ConfigConstants.CERTIFICATE_ANALYZER_BUFFER_SIZE, "2000");
        testConfigMap.put(ConfigConstants.CERTIFICATE_ANALYZER_TIMEOUT_MS, "60000");
        testConfigMap.put(ConfigConstants.CERTIFICATE_ANALYZER_DEBUG_ENABLED, "true");
        testConfigMap.put(ConfigConstants.CERTIFICATE_OUTPUT_POSTGRESQL_ENABLED, "true");

        testConfigMap.put(ConfigConstants.CERTIFICATE_OUTPUT_NEBULA_ENABLED, "true");
    }

    @Test
    @DisplayName("测试主题配置常量")
    void testTopicsConfiguration() {
        log.info("测试主题配置常量");
        
        // 验证主题配置常量存在
        assertNotNull(CertificateAnalyzerConfig.Topics.CERTIFICATE_FILES);
        assertNotNull(CertificateAnalyzerConfig.Topics.SYSTEM_CERTIFICATES);
        
        // 验证常量值
        assertEquals("kafka.topic.certificate.files", CertificateAnalyzerConfig.Topics.CERTIFICATE_FILES);
        assertEquals("kafka.topic.system.certificates", CertificateAnalyzerConfig.Topics.SYSTEM_CERTIFICATES);
        
        log.info("主题配置常量测试通过");
    }

    @Test
    @DisplayName("测试分析器配置常量")
    void testAnalysisConfiguration() {
        log.info("测试分析器配置常量");
        
        // 验证分析器配置常量存在
        assertNotNull(CertificateAnalyzerConfig.Analysis.ANALYZER_PARALLELISM);
        assertNotNull(CertificateAnalyzerConfig.Analysis.ANALYZER_BUFFER_SIZE);
        assertNotNull(CertificateAnalyzerConfig.Analysis.ANALYZER_TIMEOUT_MS);
        assertNotNull(CertificateAnalyzerConfig.Analysis.DEBUG_ENABLED);
        
        // 验证常量值
        assertEquals("certificate.analyzer.parallelism", CertificateAnalyzerConfig.Analysis.ANALYZER_PARALLELISM);
        assertEquals("certificate.analyzer.buffer.size", CertificateAnalyzerConfig.Analysis.ANALYZER_BUFFER_SIZE);
        assertEquals("certificate.analyzer.timeout.ms", CertificateAnalyzerConfig.Analysis.ANALYZER_TIMEOUT_MS);
        assertEquals("certificate.analyzer.debug.enabled", CertificateAnalyzerConfig.Analysis.DEBUG_ENABLED);
        
        log.info("分析器配置常量测试通过");
    }

    @Test
    @DisplayName("测试输出配置常量")
    void testOutputConfiguration() {
        log.info("测试输出配置常量");
        
        // 验证输出配置常量存在
        assertNotNull(CertificateAnalyzerConfig.Output.POSTGRESQL_ENABLED);
        assertNotNull(CertificateAnalyzerConfig.Output.NEBULA_ENABLED);

        // 验证常量值
        assertEquals("certificate.output.postgresql.enabled", CertificateAnalyzerConfig.Output.POSTGRESQL_ENABLED);
        assertEquals("certificate.output.nebula.enabled", CertificateAnalyzerConfig.Output.NEBULA_ENABLED);
        
        log.info("输出配置常量测试通过");
    }

    @Test
    @DisplayName("测试配置获取方法")
    void testConfigurationGetters() {
        log.info("测试配置获取方法");
        
        // 测试获取配置参数工具
        ParameterTool config = CertificateAnalyzerConfig.getConfig();
        assertNotNull(config);
        
        // 测试主题配置获取
        String certificateFilesTopic = CertificateAnalyzerConfig.getCertificateFilesTopic();
        assertNotNull(certificateFilesTopic);
        
        String systemCertificatesTopic = CertificateAnalyzerConfig.getSystemCertificatesTopic();
        assertNotNull(systemCertificatesTopic);
        
        // 测试分析器配置获取
        int parallelism = CertificateAnalyzerConfig.getAnalyzerParallelism();
        assertTrue(parallelism > 0);
        
        int bufferSize = CertificateAnalyzerConfig.getAnalyzerBufferSize();
        assertTrue(bufferSize > 0);
        
        long timeoutMs = CertificateAnalyzerConfig.getAnalyzerTimeoutMs();
        assertTrue(timeoutMs > 0);
        
        log.info("配置获取方法测试通过");
    }

    @Test
    @DisplayName("测试布尔配置获取")
    void testBooleanConfigurationGetters() {
        log.info("测试布尔配置获取");
        
        // 测试调试模式配置
        boolean debugEnabled = CertificateAnalyzerConfig.isDebugEnabled();
        // 调试模式可以是任意值，不抛出异常即可
        log.info("调试模式: {}", debugEnabled);
        
        // 测试各种输出开关
        boolean postgresqlEnabled = CertificateAnalyzerConfig.isPostgreSQLEnabled();
        boolean nebulaEnabled = CertificateAnalyzerConfig.isNebulaEnabled();

        log.info("PostgreSQL启用: {}", postgresqlEnabled);
        log.info("Nebula启用: {}", nebulaEnabled);
        log.info("MinIO存储: 必须启用（用于证书文件存储）");
        
        log.info("布尔配置获取测试通过");
    }

    @Test
    @DisplayName("测试默认值处理")
    void testDefaultValueHandling() {
        log.info("测试默认值处理");
        
        // 测试默认主题名称
        String defaultCertTopic = CertificateAnalyzerConfig.getCertificateFilesTopic();
        assertTrue(defaultCertTopic.equals("certfile") || !defaultCertTopic.trim().isEmpty());
        
        String defaultSystemTopic = CertificateAnalyzerConfig.getSystemCertificatesTopic();
        assertTrue(defaultSystemTopic.equals("certfile_system") || !defaultSystemTopic.trim().isEmpty());
        
        // 测试默认数值配置
        int defaultParallelism = CertificateAnalyzerConfig.getAnalyzerParallelism();
        assertTrue(defaultParallelism >= 1 && defaultParallelism <= 32);
        
        int defaultBufferSize = CertificateAnalyzerConfig.getAnalyzerBufferSize();
        assertTrue(defaultBufferSize > 0);
        
        long defaultTimeout = CertificateAnalyzerConfig.getAnalyzerTimeoutMs();
        assertTrue(defaultTimeout > 0);
        
        log.info("默认值处理测试通过");
    }

    @Test
    @DisplayName("测试配置打印功能")
    void testConfigurationPrinting() {
        log.info("测试配置打印功能");
        
        // 测试配置打印不抛出异常
        assertDoesNotThrow(() -> {
            CertificateAnalyzerConfig.printConfig();
        });
        
        log.info("配置打印功能测试通过");
    }
}
