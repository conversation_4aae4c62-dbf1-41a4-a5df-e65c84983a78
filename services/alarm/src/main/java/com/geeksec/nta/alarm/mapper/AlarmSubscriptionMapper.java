package com.geeksec.nta.alarm.mapper;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.geeksec.nta.alarm.entity.AlarmSubscription;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警订阅 Mapper
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface AlarmSubscriptionMapper extends BaseMapper<AlarmSubscription> {
    
    /**
     * 根据用户ID分页查询订阅
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param keyword 关键词搜索
     * @return 分页结果
     */
    Page<AlarmSubscription> selectByUserIdWithPage(Page<AlarmSubscription> page, 
                                                  @Param("userId") String userId,
                                                  @Param("keyword") String keyword);
    
    /**
     * 查询用户的所有启用订阅
     * 
     * @param userId 用户ID
     * @return 启用的订阅列表
     */
    List<AlarmSubscription> selectEnabledByUserId(@Param("userId") String userId);
    
    /**
     * 查询所有启用的订阅（供 alarm-notification Flink 作业启动时获取全量订阅信息）
     * 
     * @return 所有启用的订阅列表
     */
    List<AlarmSubscription> selectAllEnabled();
    
    /**
     * 批量更新触发统计信息
     * 
     * @param subscriptionIds 订阅ID列表
     * @param triggerTime 触发时间
     * @return 更新数量
     */
    int batchUpdateTriggerInfo(@Param("subscriptionIds") List<String> subscriptionIds,
                              @Param("triggerTime") LocalDateTime triggerTime);
    
    /**
     * 根据用户ID和订阅名称查询（用于重名检查）
     * 
     * @param userId 用户ID
     * @param subscriptionName 订阅名称
     * @param excludeId 排除的ID（更新时使用）
     * @return 订阅数量
     */
    long countByUserIdAndName(@Param("userId") String userId,
                             @Param("subscriptionName") String subscriptionName,
                             @Param("excludeId") String excludeId);
}
