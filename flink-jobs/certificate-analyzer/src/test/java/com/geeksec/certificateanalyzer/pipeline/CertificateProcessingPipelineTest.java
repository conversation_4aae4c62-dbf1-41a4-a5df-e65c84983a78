package com.geeksec.certificate.analyzer.pipeline;

import com.geeksec.certificate.analyzer.pipeline.CertificateProcessingPipeline;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书分析流水线测试
 * 
 * <AUTHOR>
 */
@Slf4j
class CertificateProcessingPipelineTest {

    private StreamExecutionEnvironment testEnv;
    private ParameterTool testConfig;

    @BeforeEach
    void setUp() {
        log.info("初始化流水线测试环境");
        
        // 创建测试配置
        Map<String, String> configMap = new HashMap<>();
        configMap.put("certificate.analyzer.debug.enabled", "true");
        configMap.put("certificate.analyzer.parallelism", "2");
        configMap.put("certificate.analyzer.minio.enabled", "false");
        configMap.put("certificate.output.postgresql.enabled", "false");

        configMap.put("certificate.output.nebula.enabled", "false");
        
        testConfig = ParameterTool.fromMap(configMap);
        
        // 创建测试执行环境
        testEnv = StreamExecutionEnvironment.getExecutionEnvironment();
        testEnv.setParallelism(1);
        testEnv.getConfig().setGlobalJobParameters(testConfig);
    }

    @Test
    @DisplayName("测试流水线结果结构")
    void testPipelineResultStructure() {
        log.info("测试流水线结果结构");
        
        // 创建流水线结果对象
        CertificateProcessingPipeline.PipelineResult result = new CertificateProcessingPipeline.PipelineResult();
        
        // 验证结果对象可以正常创建
        assertNotNull(result);
        
        // 测试设置和获取各种数据流
        DataStream<X509Certificate> mockCertStream = createMockCertificateStream();
        result.setProcessedCertificateStream(mockCertStream);
        
        assertNotNull(result.getProcessedCertificateStream());
        
        log.info("流水线结果结构测试通过");
    }

    @Test
    @DisplayName("测试流水线构建参数验证")
    void testPipelineBuildParameterValidation() {
        log.info("测试流水线构建参数验证");
        
        // 创建测试证书数据流
        DataStream<X509Certificate> certificateStream = createMockCertificateStream();
        
        // 验证参数不为空时不抛出异常
        assertDoesNotThrow(() -> {
            CertificateProcessingPipeline.PipelineResult result =
                CertificateProcessingPipeline.build(certificateStream, testConfig);
            assertNotNull(result);
        });
        
        log.info("流水线构建参数验证测试通过");
    }

    @Test
    @DisplayName("测试流水线组件配置")
    void testPipelineComponentConfiguration() {
        log.info("测试流水线组件配置");
        
        // 验证配置参数
        assertNotNull(testConfig);
        assertEquals("true", testConfig.get("certificate.analyzer.debug.enabled"));
        assertEquals("2", testConfig.get("certificate.analyzer.parallelism"));
        assertEquals("false", testConfig.get("certificate.analyzer.minio.enabled"));
        
        // 验证各种输出开关
        assertFalse(testConfig.getBoolean("certificate.output.postgresql.enabled", true));

        assertFalse(testConfig.getBoolean("certificate.output.nebula.enabled", true));
        
        log.info("流水线组件配置测试通过");
    }

    @Test
    @DisplayName("测试流水线数据流类型")
    void testPipelineDataStreamTypes() {
        log.info("测试流水线数据流类型");
        
        // 创建流水线结果
        CertificateProcessingPipeline.PipelineResult result = new CertificateProcessingPipeline.PipelineResult();
        
        // 测试各种数据流的类型安全性
        DataStream<X509Certificate> certStream = createMockCertificateStream();
        result.setProcessedCertificateStream(certStream);
        
        // 验证数据流类型
        assertNotNull(result.getProcessedCertificateStream());
        
        log.info("流水线数据流类型测试通过");
    }

    @Test
    @DisplayName("测试流水线错误处理")
    void testPipelineErrorHandling() {
        log.info("测试流水线错误处理");
        
        // 测试空配置的处理
        ParameterTool emptyConfig = ParameterTool.fromMap(new HashMap<>());
        DataStream<X509Certificate> certificateStream = createMockCertificateStream();
        
        // 即使配置为空，也应该能够构建流水线（使用默认值）
        assertDoesNotThrow(() -> {
            CertificateProcessingPipeline.PipelineResult result =
                CertificateProcessingPipeline.build(certificateStream, emptyConfig);
            assertNotNull(result);
        });
        
        log.info("流水线错误处理测试通过");
    }

    @Test
    @DisplayName("测试流水线并行度配置")
    void testPipelineParallelismConfiguration() {
        log.info("测试流水线并行度配置");
        
        // 验证执行环境并行度
        assertEquals(1, testEnv.getParallelism());
        
        // 验证配置中的并行度设置
        int configuredParallelism = testConfig.getInt("certificate.analyzer.parallelism", 4);
        assertEquals(2, configuredParallelism);
        
        log.info("流水线并行度配置测试通过");
    }

    @Test
    @DisplayName("测试流水线资源管理")
    void testPipelineResourceManagement() {
        log.info("测试流水线资源管理");
        
        // 验证执行环境资源配置
        assertNotNull(testEnv.getConfig());
        assertNotNull(testEnv.getConfig().getGlobalJobParameters());
        
        // 验证配置参数可以正确获取
        ParameterTool globalParams = (ParameterTool) testEnv.getConfig().getGlobalJobParameters();
        assertEquals("true", globalParams.get("certificate.analyzer.debug.enabled"));
        
        log.info("流水线资源管理测试通过");
    }

    /**
     * 创建模拟证书数据流
     */
    private DataStream<X509Certificate> createMockCertificateStream() {
        // 创建测试证书
        X509Certificate testCert = new X509Certificate();
        testCert.setCertId("test-pipeline-cert-001");
        testCert.setSha1("test-sha1-hash");
        testCert.setSubject("CN=pipeline.test.com");
        testCert.setIssuer("CN=Test Pipeline CA");
        testCert.setWellFormed(true);
        testCert.setCreateTime(LocalDateTime.now());
        testCert.setLabels(new java.util.ArrayList<>());
        
        // 从单个元素创建数据流
        return testEnv.fromElements(testCert);
    }
}
